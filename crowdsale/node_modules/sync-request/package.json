{"name": "sync-request", "version": "6.1.0", "description": "Make synchronous web requests", "main": "lib/index.js", "browser": "lib/browser.js", "types": "lib/index.d.ts", "keywords": ["request", "http", "https", "cache", "browserify", "synchronous", "sync"], "dependencies": {"http-response-object": "^3.0.1", "sync-rpc": "^1.2.1", "then-request": "^6.0.0"}, "devDependencies": {"body-parser": "^1.14.1", "cross-env": "^5.1.3", "express": "^4.13.3", "flowgen2": "^2.2.0", "jest": "^22.1.4", "morgan": "^1.6.1", "rimraf": "^2.6.2", "typescript": "^2.6.2"}, "scripts": {"pretest": "npm run build", "test": "jest && cross-env SYNC_REQUEST_LEGACY=true jest && node test/benchmark", "jest": "jest", "prepublishOnly": "npm run build", "prebuild": "<PERSON><PERSON><PERSON> lib", "build": "tsc && flowgen lib/**/*"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/sync-request.git"}, "author": "ForbesLindesay", "license": "MIT", "engines": {"node": ">=8.0.0"}}