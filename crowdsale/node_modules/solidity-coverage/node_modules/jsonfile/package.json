{"name": "jsonfile", "version": "4.0.0", "description": "Easily read/write JSON files.", "repository": {"type": "git", "url": "**************:jprichardson/node-jsonfile.git"}, "keywords": ["read", "write", "file", "json", "fs", "fs-extra"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "license": "MIT", "dependencies": {}, "optionalDependencies": {"graceful-fs": "^4.1.6"}, "devDependencies": {"mocha": "2.x", "rimraf": "^2.4.0", "standard": "^10.0.3"}, "main": "index.js", "files": ["index.js"], "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "mocha"}}