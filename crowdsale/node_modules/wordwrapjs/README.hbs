[![view on npm](https://badgen.net/npm/v/wordwrapjs)](https://www.npmjs.org/package/wordwrapjs)
[![npm module downloads](https://badgen.net/npm/dt/wordwrapjs)](https://www.npmjs.org/package/wordwrapjs)
[![Gihub repo dependents](https://badgen.net/github/dependents-repo/75lb/wordwrapjs)](https://github.com/75lb/wordwrapjs/network/dependents?dependent_type=REPOSITORY)
[![Gihub package dependents](https://badgen.net/github/dependents-pkg/75lb/wordwrapjs)](https://github.com/75lb/wordwrapjs/network/dependents?dependent_type=PACKAGE)
[![Build Status](https://travis-ci.org/75lb/wordwrapjs.svg?branch=master)](https://travis-ci.org/75lb/wordwrapjs)
[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](https://github.com/feross/standard)

# wordwrapjs

Word wrapping, with a few features.

- force-break option
- wraps hypenated words
- multilingual - wraps any language that uses whitespace for word separation.

## Synopsis

Wrap some text in a 20 character column.

```js
> wordwrap = require('wordwrapjs')

> text = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'

> result = wordwrap.wrap(text, { width: 20 })
```

`result` now looks like this:
```
Lorem ipsum dolor
sit amet,
consectetur
adipiscing elit, sed
do eiusmod tempor
incididunt ut labore
et dolore magna
aliqua.
```

By default, long words will not break. Unless you set the `break` option.
```js
> url = 'https://github.com/75lb/wordwrapjs'

> wrap.lines(url, { width: 18 })
[ 'https://github.com/75lb/wordwrapjs' ]

> wrap.lines(url, { width: 18, break: true })
[ 'https://github.com', '/75lb/wordwrapjs' ]
```

## API Reference

{{#module name="wordwrapjs"}}
{{>body~}}
{{>member-index~}}
{{>members~}}
{{/module}}

* * *

&copy; 2015-21 Lloyd Brookes \<<EMAIL>\>. Documented by [jsdoc-to-markdown](https://github.com/jsdoc2md/jsdoc-to-markdown).
