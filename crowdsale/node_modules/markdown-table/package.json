{"name": "markdown-table", "version": "1.1.3", "description": "Markdown/ASCII tables", "license": "MIT", "keywords": ["text", "markdown", "table", "align", "ascii", "rows", "tabular"], "repository": "wooorm/markdown-table", "bugs": "https://github.com/wooorm/markdown-table/issues", "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js"], "dependencies": {}, "devDependencies": {"browserify": "^16.0.0", "chalk": "^2.0.0", "nyc": "^14.0.0", "prettier": "^1.12.1", "remark-cli": "^6.0.0", "remark-preset-wooorm": "^4.0.0", "strip-ansi": "^5.0.0", "tape": "^4.4.0", "tinyify": "^2.5.0", "xo": "^0.24.0"}, "scripts": {"format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify . -s markdownTable -o markdown-table.js", "build-mangle": "browserify . -s markdownTable -p tinyify -o markdown-table.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run build && npm run test-coverage"}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"complexity": "off", "max-depth": "off"}, "ignores": ["markdown-table.js"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}}