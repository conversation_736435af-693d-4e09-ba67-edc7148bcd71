{"name": "crowdsale", "version": "1.0.0", "description": "", "dependencies": {"@openzeppelin/contracts": "^4.9.3", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "bootstrap": "^5.3.2", "ethers": "^6.8.1", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "hardhat": "^2.18.3"}}