{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/Documents/vscode/crowdsale/contracts/Crowdsale.sol": {"lastModificationDate": 1750182855729, "contentHash": "d3e1b354108bb837db7d8c7bb40ada71", "sourceName": "contracts/Crowdsale.sol", "solcConfig": {"version": "0.8.28", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./Token.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Crowdsale"]}, "/home/<USER>/Documents/vscode/crowdsale/contracts/Token.sol": {"lastModificationDate": 1750180333677, "contentHash": "b47d047aafd016e5246771bfc9e5f7da", "sourceName": "contracts/Token.sol", "solcConfig": {"version": "0.8.28", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["hardhat/console.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Token"]}, "/home/<USER>/Documents/vscode/crowdsale/node_modules/hardhat/console.sol": {"lastModificationDate": 1750180699618, "contentHash": "681c532e816169606d13a5fe8b475074", "sourceName": "hardhat/console.sol", "solcConfig": {"version": "0.8.28", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.22 <0.9.0"], "artifacts": ["console"]}}}