//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

// Imports the Token contract from your Token.sol file
import "./Token.sol";

// This is the titled "Crowdsale" contract
// This contract manages the sale of tokens with customizable pricing and timing
contract Crowdsale {
    // Contract owner's address who can perform administrative functions
    address public crowdsaleAdmin;
    
    // Reference to the token being sold
    Token public tokenBeingSold;
    
    // Price of each token in Wei (1 ETH = 10^18 Wei)
    uint256 public pricePerToken;
    
    // Maximum number of tokens available for sale
    uint256 public tokenSaleCapacity;
    
    // Running total of tokens that have been sold
    uint256 public tokensSoldSoFar;
    
    // Timestamp when the crowdsale begins (in Unix timestamp format)
    uint256 public crowdsaleLaunchTimestamp;

    // Events to log important contract actions
    event TokensPurchased(uint256 numberOfTokens, address buyerAddress);
    event Buy(uint256 numberOfTokens, address buyerAddress); // For test compatibility
    event CrowdsaleFinalized(uint256 finalTokensSold, uint256 totalEthRaised);
    event Finalize(uint256 finalTokensSold, uint256 totalEthRaised); // For test compatibility
    event PriceUpdated(uint256 oldPrice, uint256 newPrice);

    // @notice Contract constructor to initialize the crowdsale
    // @param _tokenContract Address of the token contract being sold
    // @param _pricePerToken Price per token in Wei
    // @param _tokenSaleCapacity Maximum number of tokens available for sale
    // @param _startTimestamp Unix timestamp when the sale should start
    constructor(
        Token _tokenContract, // Reference to the token being sold, using the "_tokenContract" parameter for internal use
        uint256 _pricePerToken, // Price per token in Wei
        uint256 _tokenSaleCapacity, // Maximum number of tokens available for sale
        uint256 _startTimestamp // Unix timestamp when the sale should start
    ) {
        require(address(_tokenContract) != address(0), "Error: Token contract address cannot be zero");
        require(_pricePerToken > 0, "Error: Price per token must be greater than 0");
        require(_tokenSaleCapacity > 0, "Error: Token sale capacity must be greater than 0");
        require(_startTimestamp > 0, "Error: Start timestamp must be greater than 0");

        crowdsaleAdmin = msg.sender; // Set the contract deployer as the admin
        tokenBeingSold = _tokenContract; // Initialize the token being sold with the provided address
        pricePerToken = _pricePerToken; // Set the price per token
        tokenSaleCapacity = _tokenSaleCapacity; // Set the maximum token sale capacity
        tokensSoldSoFar = 0; // Initialize the total tokens sold to 0
        crowdsaleLaunchTimestamp = _startTimestamp; // Set the crowdsale launch timestamp
    }

    // @notice Restricts function access to contract owner only
    modifier onlyAdmin() {
        require(msg.sender == crowdsaleAdmin, "Error: Caller is not the crowdsale administrator");
        _;
    }

    // @notice Ensures the crowdsale has started before allowing token purchase
    modifier saleIsActive() {
        require(block.timestamp >= crowdsaleLaunchTimestamp, "Error: Crowdsale has not started yet");
        _;
    }

    // @notice Allows direct ETH transfers to contract to purchase tokens
    // @dev Automatically calculates tokens based on ETH sent
    receive() external payable saleIsActive {
        require(msg.value > 0, "Error: Must send ETH to purchase tokens");
        uint256 tokensToPurchase = (msg.value * 1e18) / pricePerToken; // Calculate tokens with proper decimals
        require(tokensToPurchase > 0, "Error: ETH amount too small");

        // Check sale capacity
        require(tokensSoldSoFar + tokensToPurchase <= tokenSaleCapacity, "Error: Purchase exceeds sale capacity");

        // Check if contract has enough tokens
        uint256 availableTokens = tokenBeingSold.balanceOf(address(this));
        require(availableTokens >= tokensToPurchase, "Error: Insufficient tokens available");

        // Transfer tokens to buyer
        bool transferSuccessful = tokenBeingSold.transfer(msg.sender, tokensToPurchase);
        require(transferSuccessful, "Error: Token transfer failed");

        // Update total tokens sold
        tokensSoldSoFar += tokensToPurchase;

        // Emit purchase event
        emit TokensPurchased(tokensToPurchase, msg.sender);
        emit Buy(tokensToPurchase, msg.sender); // For test compatibility
    }

    // @notice Main function to purchase tokens
    // @param _requestedTokenAmount Amount of tokens to purchase (in smallest unit, e.g., Wei)
    function buyTokens(uint256 _requestedTokenAmount) public payable saleIsActive {
        require(_requestedTokenAmount > 0, "Error: Must request more than 0 tokens");

        // Verify correct ETH amount was sent
        uint256 expectedEthPayment = (_requestedTokenAmount / 1e18) * pricePerToken;
        require(msg.value == expectedEthPayment, "Error: Incorrect ETH amount sent");

        // Check sale capacity
        require(tokensSoldSoFar + _requestedTokenAmount <= tokenSaleCapacity, "Error: Purchase exceeds sale capacity");

        // Check if contract has enough tokens to fulfill purchase
        uint256 availableTokens = tokenBeingSold.balanceOf(address(this));
        require(availableTokens >= _requestedTokenAmount, "Error: Insufficient tokens available");

        // Transfer tokens to buyer
        bool transferSuccessful = tokenBeingSold.transfer(msg.sender, _requestedTokenAmount);
        require(transferSuccessful, "Error: Token transfer failed");

        // Update total tokens sold
        tokensSoldSoFar += _requestedTokenAmount;

        // Emit purchase event
        emit TokensPurchased(_requestedTokenAmount, msg.sender);
        emit Buy(_requestedTokenAmount, msg.sender); // For test compatibility
    }

    // @notice Allows owner to update token price
    // @param _updatedPrice New price per token in Wei
    function setPrice(uint256 _updatedPrice) public onlyAdmin {
        require(_updatedPrice > 0, "Error: Price must be greater than 0");
        uint256 oldPrice = pricePerToken;
        pricePerToken = _updatedPrice;
        emit PriceUpdated(oldPrice, _updatedPrice);
    }

    // @notice Finalizes the crowdsale and transfers remaining tokens and ETH to owner
    function finalize() public onlyAdmin {
        // Transfer remaining tokens to owner
        uint256 remainingTokens = tokenBeingSold.balanceOf(address(this));
        bool tokenTransferSuccessful = tokenBeingSold.transfer(crowdsaleAdmin, remainingTokens);
        require(tokenTransferSuccessful, "Error: Failed to transfer remaining tokens");

        // Transfer all collected ETH to owner
        uint256 totalEthRaised = address(this).balance;
        (bool ethTransferSuccessful, ) = crowdsaleAdmin.call{value: totalEthRaised}("");
        require(ethTransferSuccessful, "Error: Failed to transfer ETH to owner");

        // Emit finalization event
        emit CrowdsaleFinalized(tokensSoldSoFar, totalEthRaised);
        emit Finalize(tokensSoldSoFar, totalEthRaised); // For test compatibility
    }

    // @notice Getter functions for test compatibility
    function price() public view returns (uint256) {
        return pricePerToken;
    }

    function token() public view returns (address) {
        return address(tokenBeingSold);
    }

    function tokensSold() public view returns (uint256) {
        return tokensSoldSoFar;
    }
}