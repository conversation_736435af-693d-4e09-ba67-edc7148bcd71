{"name": "amm", "version": "1.0.0", "description": "", "dependencies": {"@reduxjs/toolkit": "^1.8.4", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.2.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-blockies": "^1.4.1", "react-bootstrap": "^2.4.0", "react-dom": "^18.2.0", "react-redux": "^8.0.2", "react-router-bootstrap": "^0.26.2", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "redux-thunk": "^2.4.1", "reselect": "^4.1.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<EMAIL>", "license": "ISC", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^1.0.2", "hardhat": "^2.10.1"}}