// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import "hardhat/console.sol";
import "./Token.sol";

/**
 * @title Automated Market Maker (AMM) Contract
 * @dev Implements a constant product AMM (x * y = k) for token swapping
 * @notice This contract allows users to swap tokens and provide liquidity
 * Used by: React frontend components (Swap.js, Deposit.js, Withdraw.js)
 * Interacts with: Token.sol contracts for DAPP and USD tokens
 */
contract AMM {
    // Token contract references - these are the two tokens in the trading pair
    Token public firstToken;        // Reference to first token contract (DAPP)
    Token public secondToken;       // Reference to second token contract (USD)

    // AMM state variables - track the liquidity pool
    uint256 public firstTokenReserves;   // How many of token1 are in the pool
    uint256 public secondTokenReserves;  // How many of token2 are in the pool
    uint256 public constantProduct;      // K value in x*y=k formula (for price calculation)

    // Liquidity provider tracking
    uint256 public totalLiquidityShares;                    // Total shares issued to all liquidity providers
    mapping(address => uint256) public liquidityProviderShares; // How many shares each address owns
    uint256 constant CALCULATION_PRECISION = 10**18;       // Precision for mathematical calculations

    /**
     * @dev Event emitted when tokens are swapped
     * @notice Frontend listens to this event to update charts and transaction history
     * Used by: Charts.js component to display swap history
     */
    event Swap(
        address swapperAddress,        // Who performed the swap 
        address tokenGivenAddress,     // Contract address of token being sold
        uint256 tokenGivenAmount,      // Amount of token being sold
        address tokenReceivedAddress,  // Contract address of token being bought
        uint256 tokenReceivedAmount,   // Amount of token being bought
        uint256 newFirstTokenBalance,  // Pool balance of token1 after swap
        uint256 newSecondTokenBalance, // Pool balance of token2 after swap
        uint256 swapTimestamp         // When the swap occurred
    );

    /**
     * @dev Constructor - sets up the AMM with two token contracts
     * @param _firstToken Address of the first token contract (DAPP)
     * @param _secondToken Address of the second token contract (USD)
     * Called by: deploy.js script during initial setup
     */
    constructor(Token _firstToken, Token _secondToken) {
        firstToken = _firstToken;
        secondToken = _secondToken;
    }

    /**
     * @dev Add liquidity to the AMM pool and receive liquidity provider shares
     * @param firstTokenAmount Amount of first token to deposit
     * @param secondTokenAmount Amount of second token to deposit
     * @notice Users must approve this contract to spend their tokens first
     * Called by: Deposit.js component when users want to provide liquidity
     * Requires: Prior token approval from user's wallet
     */
    function addLiquidity(uint256 firstTokenAmount, uint256 secondTokenAmount) external {
        // Transfer tokens from user to this contract (requires prior approval)
        require(
            firstToken.transferFrom(msg.sender, address(this), firstTokenAmount),
            "Failed to transfer first token - check approval"
        );
        require(
            secondToken.transferFrom(msg.sender, address(this), secondTokenAmount),
            "Failed to transfer second token - check approval"
        );

        // Calculate how many liquidity shares to give the user
        uint256 liquiditySharesEarned;

        // If this is the first liquidity deposit, give a fixed amount of shares
        if (totalLiquidityShares == 0) {
            liquiditySharesEarned = 100 * CALCULATION_PRECISION; // 100 shares for first depositor
        } else {
            // Calculate shares proportional to existing pool
            uint256 sharesFromFirstToken = (totalLiquidityShares * firstTokenAmount) / firstTokenReserves;
            uint256 sharesFromSecondToken = (totalLiquidityShares * secondTokenAmount) / secondTokenReserves;

            // Ensure user is providing tokens in the correct ratio (within 0.1% tolerance)
            require(
                (sharesFromFirstToken / 10**3) == (sharesFromSecondToken / 10**3),
                "Token amounts must maintain current pool ratio"
            );
            liquiditySharesEarned = sharesFromFirstToken;
        }

        // Update pool reserves and constant product
        firstTokenReserves += firstTokenAmount;
        secondTokenReserves += secondTokenAmount;
        constantProduct = firstTokenReserves * secondTokenReserves; // Update K for price calculations

        // Update user's share balance and total shares
        totalLiquidityShares += liquiditySharesEarned;
        liquidityProviderShares[msg.sender] += liquiditySharesEarned;
    }

    /**
     * @dev Calculate how many second tokens needed when depositing first tokens
     * @param firstTokenDepositAmount Amount of first token user wants to deposit
     * @return secondTokenRequiredAmount Amount of second token needed to maintain ratio
     * Called by: Deposit.js component to show users the required token amounts
     * Purpose: Maintains the constant ratio in the liquidity pool
     */
    function calculateToken2Deposit(uint256 firstTokenDepositAmount)
        public
        view
        returns (uint256 secondTokenRequiredAmount)
    {
        // Calculate proportional amount: (pool_token2 * deposit_token1) / pool_token1
        secondTokenRequiredAmount = (secondTokenReserves * firstTokenDepositAmount) / firstTokenReserves;
    }

    /**
     * @dev Calculate how many first tokens needed when depositing second tokens
     * @param secondTokenDepositAmount Amount of second token user wants to deposit
     * @return firstTokenRequiredAmount Amount of first token needed to maintain ratio
     * Called by: Deposit.js component to show users the required token amounts
     * Purpose: Maintains the constant ratio in the liquidity pool
     */
    function calculateToken1Deposit(uint256 secondTokenDepositAmount)
        public
        view
        returns (uint256 firstTokenRequiredAmount)
    {
        // Calculate proportional amount: (pool_token1 * deposit_token2) / pool_token2
        firstTokenRequiredAmount = (firstTokenReserves * secondTokenDepositAmount) / secondTokenReserves;
    }

    /**
     * @dev Calculate how many second tokens received when swapping first tokens
     * @param firstTokenSwapAmount Amount of first token to swap
     * @return secondTokenReceived Amount of second token user will receive
     * @notice Uses constant product formula: x * y = k
     * Called by: Swap.js component to show swap preview
     */
    function calculateToken1Swap(uint256 firstTokenSwapAmount)
        public
        view
        returns (uint256 secondTokenReceived)
    {
        // Calculate new pool state after adding first tokens
        uint256 firstTokenAfterSwap = firstTokenReserves + firstTokenSwapAmount;
        uint256 secondTokenAfterSwap = constantProduct / firstTokenAfterSwap;
        secondTokenReceived = secondTokenReserves - secondTokenAfterSwap;

        // Prevent pool from being completely drained
        if (secondTokenReceived == secondTokenReserves) {
            secondTokenReceived--;
        }

        require(secondTokenReceived < secondTokenReserves, "Swap amount too large");
    }

    /**
     * @dev Swap first tokens for second tokens
     * @param firstTokenSwapAmount Amount of first token to swap
     * @return secondTokenReceived Amount of second token received
     * @notice User must approve this contract to spend their first tokens
     * Called by: Swap.js component when user confirms swap
     */
    function swapToken1(uint256 firstTokenSwapAmount)
        external
        returns(uint256 secondTokenReceived)
    {
        // Calculate how many second tokens user will receive
        secondTokenReceived = calculateToken1Swap(firstTokenSwapAmount);

        // Execute the swap: take first tokens, give second tokens
        firstToken.transferFrom(msg.sender, address(this), firstTokenSwapAmount);
        firstTokenReserves += firstTokenSwapAmount;
        secondTokenReserves -= secondTokenReceived;
        secondToken.transfer(msg.sender, secondTokenReceived);

        // Emit event for frontend to detect and update charts
        emit Swap(
            msg.sender,
            address(firstToken),
            firstTokenSwapAmount,
            address(secondToken),
            secondTokenReceived,
            firstTokenReserves,
            secondTokenReserves,
            block.timestamp
        );
    }

    /**
     * @dev Calculate how many first tokens received when swapping second tokens
     * @param secondTokenSwapAmount Amount of second token to swap
     * @return firstTokenReceived Amount of first token user will receive
     * @notice Uses constant product formula: x * y = k
     * Called by: Swap.js component to show swap preview
     */
    function calculateToken2Swap(uint256 secondTokenSwapAmount)
        public
        view
        returns (uint256 firstTokenReceived)
    {
        // Calculate new pool state after adding second tokens
        uint256 secondTokenAfterSwap = secondTokenReserves + secondTokenSwapAmount;
        uint256 firstTokenAfterSwap = constantProduct / secondTokenAfterSwap;
        firstTokenReceived = firstTokenReserves - firstTokenAfterSwap;

        // Prevent pool from being completely drained
        if (firstTokenReceived == firstTokenReserves) {
            firstTokenReceived--;
        }

        require(firstTokenReceived < firstTokenReserves, "Swap amount too large");
    }

    /**
     * @dev Swap second tokens for first tokens
     * @param secondTokenSwapAmount Amount of second token to swap
     * @return firstTokenReceived Amount of first token received
     * @notice User must approve this contract to spend their second tokens
     * Called by: Swap.js component when user confirms swap
     */
    function swapToken2(uint256 secondTokenSwapAmount)
        external
        returns(uint256 firstTokenReceived)
    {
        // Calculate how many first tokens user will receive
        firstTokenReceived = calculateToken2Swap(secondTokenSwapAmount);

        // Execute the swap: take second tokens, give first tokens
        secondToken.transferFrom(msg.sender, address(this), secondTokenSwapAmount);
        secondTokenReserves += secondTokenSwapAmount;
        firstTokenReserves -= firstTokenReceived;
        firstToken.transfer(msg.sender, firstTokenReceived);

        // Emit event for frontend to detect and update charts
        emit Swap(
            msg.sender,
            address(secondToken),
            secondTokenSwapAmount,
            address(firstToken),
            firstTokenReceived,
            firstTokenReserves,
            secondTokenReserves,
            block.timestamp
        );
    }

    /**
     * @dev Calculate how many tokens will be withdrawn for given shares
     * @param sharesToWithdraw Number of liquidity shares to withdraw
     * @return firstTokenWithdrawn Amount of first token to be withdrawn
     * @return secondTokenWithdrawn Amount of second token to be withdrawn
     * @notice Withdrawal amounts are proportional to share ownership
     * Called by: Withdraw.js component to show withdrawal preview
     */
    function calculateWithdrawAmount(uint256 sharesToWithdraw)
        public
        view
        returns (uint256 firstTokenWithdrawn, uint256 secondTokenWithdrawn)
    {
        require(sharesToWithdraw <= totalLiquidityShares, "Shares exceed total supply");

        // Calculate proportional amounts based on share ownership
        firstTokenWithdrawn = (sharesToWithdraw * firstTokenReserves) / totalLiquidityShares;
        secondTokenWithdrawn = (sharesToWithdraw * secondTokenReserves) / totalLiquidityShares;
    }

    /**
     * @dev Remove liquidity from the pool and return tokens to user
     * @param sharesToWithdraw Number of liquidity shares to withdraw
     * @return firstTokenWithdrawn Amount of first token withdrawn
     * @return secondTokenWithdrawn Amount of second token withdrawn
     * @notice User must own the shares they're trying to withdraw
     * Called by: Withdraw.js component when user confirms withdrawal
     */
    function removeLiquidity(uint256 sharesToWithdraw)
        external
        returns(uint256 firstTokenWithdrawn, uint256 secondTokenWithdrawn)
    {
        require(
            sharesToWithdraw <= liquidityProviderShares[msg.sender],
            "Cannot withdraw more shares than you own"
        );

        // Calculate how many tokens to return
        (firstTokenWithdrawn, secondTokenWithdrawn) = calculateWithdrawAmount(sharesToWithdraw);

        // Update user's shares and total shares
        liquidityProviderShares[msg.sender] -= sharesToWithdraw;
        totalLiquidityShares -= sharesToWithdraw;

        // Update pool reserves and constant product
        firstTokenReserves -= firstTokenWithdrawn;
        secondTokenReserves -= secondTokenWithdrawn;
        constantProduct = firstTokenReserves * secondTokenReserves;

        // Transfer tokens back to user
        firstToken.transfer(msg.sender, firstTokenWithdrawn);
        secondToken.transfer(msg.sender, secondTokenWithdrawn);
    }

    // Legacy compatibility functions for frontend - these map to the new variable names
    function token1() public view returns (Token) { return firstToken; }
    function token2() public view returns (Token) { return secondToken; }
    function token1Balance() public view returns (uint256) { return firstTokenReserves; }
    function token2Balance() public view returns (uint256) { return secondTokenReserves; }
    function K() public view returns (uint256) { return constantProduct; }
    function totalShares() public view returns (uint256) { return totalLiquidityShares; }
    function shares(address account) public view returns (uint256) { return liquidityProviderShares[account]; }
}
