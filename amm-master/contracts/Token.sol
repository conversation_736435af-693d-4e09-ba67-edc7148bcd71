//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

import "hardhat/console.sol";

/**
 * @title Token Contract
 * @dev ERC-20 compatible token contract for the AMM system
 * @notice This contract creates tokens that can be traded in the AMM
 * Used by: AMM.sol (for token1 and token2), React frontend (via ethers.js)
 */
contract Token {
    // Token metadata - visible to users and other contracts
    string public tokenName;           // Full name of the token (e.g., "Dapp Token")
    string public tokenSymbol;         // Short symbol (e.g., "DAPP", "USD")
    uint256 public tokenDecimals = 18; // Number of decimal places (18 = same as ETH)
    uint256 public totalTokenSupply;   // Total number of tokens that exist

    // Storage mappings - these store the blockchain state
    mapping(address => uint256) public userBalances;        // How many tokens each address owns
    mapping(address => mapping(address => uint256)) public tokenAllowances; // Approval system for spending

    // Events - these are logged on the blockchain and can be read by the frontend
    event Transfer(
        address indexed fromAddress,    // Who sent the tokens
        address indexed to<PERSON><PERSON><PERSON>,      // Who received the tokens
        uint256 tokenAmount            // How many tokens were transferred
    );

    event Approval(
        address indexed tokenOwner,     // Who owns the tokens
        address indexed approvedSpender, // Who is allowed to spend them
        uint256 approvedAmount         // How many tokens they can spend
    );

    /**
     * @dev Constructor - runs once when contract is deployed
     * @param _tokenName The full name of the token
     * @param _tokenSymbol The symbol/ticker of the token
     * @param _initialSupply How many tokens to create (before decimals)
     * Called by: deploy.js script when setting up the AMM system
     */
    constructor(
        string memory _tokenName,
        string memory _tokenSymbol,
        uint256 _initialSupply
    ) {
        tokenName = _tokenName;
        tokenSymbol = _tokenSymbol;
        // Multiply by 10^18 to account for decimals (1 token = 1000000000000000000 wei)
        totalTokenSupply = _initialSupply * (10**tokenDecimals);
        // Give all tokens to the deployer initially
        userBalances[msg.sender] = totalTokenSupply;
    }

    /**
     * @dev Transfer tokens from your account to another address
     * @param recipientAddress Where to send the tokens
     * @param transferAmount How many tokens to send (in wei units)
     * @return success True if transfer succeeded
     * Called by: React frontend when users want to send tokens directly
     */
    function transfer(address recipientAddress, uint256 transferAmount)
        public
        returns (bool success)
    {
        // Check that sender has enough tokens
        require(userBalances[msg.sender] >= transferAmount, "Insufficient balance");

        // Use internal function to do the actual transfer
        _executeTransfer(msg.sender, recipientAddress, transferAmount);

        return true;
    }

    /**
     * @dev Internal function that handles the actual token transfer logic
     * @param senderAddress Who is sending the tokens
     * @param recipientAddress Who is receiving the tokens
     * @param transferAmount How many tokens to transfer
     * Used by: transfer() and transferFrom() functions
     */
    function _executeTransfer(
        address senderAddress,
        address recipientAddress,
        uint256 transferAmount
    ) internal {
        // Prevent sending to the zero address (burning tokens accidentally)
        require(recipientAddress != address(0), "Cannot transfer to zero address");

        // Update balances: subtract from sender, add to recipient
        userBalances[senderAddress] = userBalances[senderAddress] - transferAmount;
        userBalances[recipientAddress] = userBalances[recipientAddress] + transferAmount;

        // Emit event so frontend can detect the transfer
        emit Transfer(senderAddress, recipientAddress, transferAmount);
    }

    /**
     * @dev Approve another address to spend your tokens (needed for AMM)
     * @param approvedSpenderAddress Who you're allowing to spend your tokens
     * @param approvedAmount How many tokens they can spend
     * @return success True if approval succeeded
     * Called by: React frontend before swapping/depositing in AMM
     * Used by: AMM contract to transfer tokens during swaps/deposits
     */
    function approve(address approvedSpenderAddress, uint256 approvedAmount)
        public
        returns(bool success)
    {
        // Prevent approving the zero address
        require(approvedSpenderAddress != address(0), "Cannot approve zero address");

        // Set the allowance amount
        tokenAllowances[msg.sender][approvedSpenderAddress] = approvedAmount;

        // Emit event so frontend can detect the approval
        emit Approval(msg.sender, approvedSpenderAddress, approvedAmount);
        return true;
    }

    /**
     * @dev Transfer tokens on behalf of someone else (used by AMM)
     * @param tokenOwnerAddress Who owns the tokens being transferred
     * @param recipientAddress Who will receive the tokens
     * @param transferAmount How many tokens to transfer
     * @return success True if transfer succeeded
     * Called by: AMM contract during swaps and liquidity operations
     * Requires: Prior approval from tokenOwnerAddress
     */
    function transferFrom(
        address tokenOwnerAddress,
        address recipientAddress,
        uint256 transferAmount
    )
        public
        returns (bool success)
    {
        // Check that owner has enough tokens
        require(transferAmount <= userBalances[tokenOwnerAddress], "Owner has insufficient balance");
        // Check that spender is approved for this amount
        require(transferAmount <= tokenAllowances[tokenOwnerAddress][msg.sender], "Insufficient allowance");

        // Reduce the allowance by the amount being spent
        tokenAllowances[tokenOwnerAddress][msg.sender] = tokenAllowances[tokenOwnerAddress][msg.sender] - transferAmount;

        // Execute the transfer
        _executeTransfer(tokenOwnerAddress, recipientAddress, transferAmount);

        return true;
    }

    // Legacy function names for compatibility with frontend
    function name() public view returns (string memory) { return tokenName; }
    function symbol() public view returns (string memory) { return tokenSymbol; }
    function decimals() public view returns (uint256) { return tokenDecimals; }
    function totalSupply() public view returns (uint256) { return totalTokenSupply; }
    function balanceOf(address account) public view returns (uint256) { return userBalances[account]; }
    function allowance(address owner, address spender) public view returns (uint256) { return tokenAllowances[owner][spender]; }

}
