import { useEffect, useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { HashRouter, Routes, Route } from 'react-router-dom'
import { Container } from 'react-bootstrap'
import { ethers } from 'ethers'

// Import React components for different pages of the AMM interface
import Navigation from './Navigation';    // Top navigation bar with wallet connection
import Tabs from './Tabs';               // Tab navigation between different AMM functions
import Swap from './Swap';               // Token swapping interface
import Deposit from './Deposit';         // Liquidity provision interface
import Withdraw from './Withdraw';       // Liquidity withdrawal interface
import Charts from './Charts';           // Price charts and swap history

// Import blockchain interaction functions from Redux store
import {
  loadProvider,    // Connects to MetaMask/Web3 provider
  loadNetwork,     // Gets current blockchain network (Hardhat local, mainnet, etc.)
  loadAccount,     // Gets current user's wallet address
  loadTokens,      // Loads DAPP and USD token contracts
  loadAMM          // Loads the AMM contract for swapping/liquidity
} from '../store/interactions'

/**
 * @component App
 * @description Main application component that sets up routing and blockchain connections
 * @notice This is the root component that initializes the entire AMM interface
 *
 * Key responsibilities:
 * 1. Connect to blockchain provider (MetaMask)
 * 2. Load smart contracts (Token contracts + AMM contract)
 * 3. Set up routing between different pages (Swap, Deposit, Withdraw, Charts)
 * 4. Handle network/account changes from MetaMask
 */
function App() {
  // Redux dispatch function - used to update global state
  // Connected to: store/store.js which manages provider, tokens, and amm state
  const reduxDispatch = useDispatch()

  /**
   * @function initializeBlockchainConnection
   * @description Connects to blockchain and loads all necessary contracts
   * @notice This runs once when the app starts and sets up the entire blockchain connection
   *
   * Flow:
   * 1. Connect to MetaMask → get provider
   * 2. Get network info → determine which contracts to load
   * 3. Load token contracts → DAPP and USD tokens from config.json
   * 4. Load AMM contract → the main trading contract
   * 5. Set up event listeners → handle network/account changes
   */
  const initializeBlockchainConnection = useCallback(async () => {
    try {
      // Step 1: Connect to MetaMask and get Web3 provider
      // This creates the connection to the blockchain (local Hardhat or live network)
      const blockchainProvider = await loadProvider(reduxDispatch)

      // Step 2: Get current network information
      // chainId determines which contracts to load (31337 = Hardhat local)
      // See config.json for contract addresses per network
      const currentChainId = await loadNetwork(blockchainProvider, reduxDispatch)

      // Step 3: Set up MetaMask event listeners for better UX
      // When user switches networks in MetaMask, reload the page to get new contracts
      window.ethereum.on('chainChanged', () => {
        console.log('Network changed, reloading page...')
        window.location.reload()
      })

      // When user switches accounts in MetaMask, update the current account
      // This triggers balance updates and other account-specific data
      window.ethereum.on('accountsChanged', async () => {
        console.log('Account changed, updating...')
        await loadAccount(reduxDispatch)
      })

      // Step 4: Load smart contracts
      // Load both token contracts (DAPP and USD) from addresses in config.json
      await loadTokens(blockchainProvider, currentChainId, reduxDispatch)

      // Load the main AMM contract that handles swapping and liquidity
      await loadAMM(blockchainProvider, currentChainId, reduxDispatch)

      console.log('Blockchain connection initialized successfully')
    } catch (error) {
      console.error('Failed to initialize blockchain connection:', error)
      // In a production app, you might want to show an error message to the user
    }
  }, [reduxDispatch])

  // Run blockchain initialization when component mounts
  // Empty dependency array means this only runs once
  useEffect(() => {
    initializeBlockchainConnection()
  }, [initializeBlockchainConnection]);

  return(
    <Container>
      {/* HashRouter enables client-side routing between different AMM functions */}
      <HashRouter>
        {/* Navigation bar - shows wallet connection, network selector, account info */}
        <Navigation />

        <hr />

        {/* Tab navigation - lets users switch between Swap, Deposit, Withdraw, Charts */}
        <Tabs />

        {/* Route definitions - each path renders a different component */}
        <Routes>
          <Route exact path="/" element={<Swap />} />           {/* Default: Token swapping */}
          <Route path="/deposit" element={<Deposit />} />       {/* Add liquidity to pool */}
          <Route path="/withdraw" element={<Withdraw />} />     {/* Remove liquidity from pool */}
          <Route path="/charts" element={<Charts />} />         {/* View price history and stats */}
        </Routes>
      </HashRouter>
    </Container>
  )
}

export default App;
