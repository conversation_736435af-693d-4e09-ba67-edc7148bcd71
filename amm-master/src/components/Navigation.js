import { useSelector, useDispatch } from 'react-redux'
import Navbar from 'react-bootstrap/Navbar';
import Form from 'react-bootstrap/Form'
import Button from 'react-bootstrap/Button'
import Blockies from 'react-blockies'

import logo from '../logo.png';

// Import blockchain interaction functions
import { loadAccount, loadBalances } from '../store/interactions'

// Import network configuration (contract addresses per network)
import config from '../config.json'

/**
 * @component Navigation
 * @description Top navigation bar with wallet connection and network selection
 * @notice This component handles user wallet connection and network switching
 *
 * Key features:
 * 1. Connect/disconnect wallet button
 * 2. Network selector dropdown
 * 3. Display connected account address with identicon
 * 4. Show account balances when connected
 */
const Navigation = () => {
  // Get blockchain connection state from Redux store
  // These values come from: store/reducers/provider.js
  const currentNetworkId = useSelector(state => state.provider.chainId)      // Current blockchain network
  const connectedWalletAddress = useSelector(state => state.provider.account) // User's wallet address

  // Get contract instances from Redux store
  // These values come from: store/reducers/tokens.js and store/reducers/amm.js
  const tokenContracts = useSelector(state => state.tokens.contracts)        // [DAPP, USD] token contracts
  const ammContract = useSelector(state => state.amm.contract)               // AMM contract instance

  // Redux dispatch function for updating global state
  const reduxDispatch = useDispatch()

  /**
   * @function handleWalletConnection
   * @description Connects user's wallet and loads their token balances
   * @notice This function is called when user clicks "Connect" button
   *
   * Flow:
   * 1. Request wallet connection from MetaMask
   * 2. Get user's account address
   * 3. Load user's token balances (DAPP and USD)
   * 4. Update Redux state with account info
   */
  const handleWalletConnection = async () => {
    try {
      // Connect to MetaMask and get user's account
      const userAccount = await loadAccount(reduxDispatch)

      // Load user's token balances for display in UI
      // This gets balances for both DAPP and USD tokens
      await loadBalances(ammContract, tokenContracts, userAccount, reduxDispatch)

      console.log('Wallet connected successfully:', userAccount)
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      // In production, you might want to show an error message to the user
    }
  }

  /**
   * @function handleNetworkSwitch
   * @description Switches to a different blockchain network
   * @param {Event} event Select dropdown change event
   * @notice This triggers MetaMask to switch networks
   *
   * Available networks:
   * - 0x7A69 (31337): Hardhat Local Network
   * - 0x5 (5): Goerli Testnet
   */
  const handleNetworkSwitch = async (event) => {
    const selectedNetworkId = event.target.value

    try {
      // Request MetaMask to switch to selected network
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: selectedNetworkId }],
      })

      console.log('Network switched to:', selectedNetworkId)
    } catch (error) {
      console.error('Failed to switch network:', error)
      // MetaMask will show its own error message to the user
    }
  }

  return (
    <Navbar className='my-3' expand="lg">
      {/* AMM Logo and Brand */}
      <img
        alt="AMM Logo"
        src={logo}
        width="40"
        height="40"
        className="d-inline-block align-top mx-3"
      />
      <Navbar.Brand href="#">Dapp University AMM</Navbar.Brand>

      {/* Mobile menu toggle button */}
      <Navbar.Toggle aria-controls="nav" />

      {/* Right side of navbar - network selector and wallet connection */}
      <Navbar.Collapse id="nav" className="justify-content-end">
        <div className="d-flex justify-content-end mt-3">

          {/* Network Selector Dropdown */}
          <Form.Select
            aria-label="Blockchain Network Selector"
            value={config[currentNetworkId] ? `0x${currentNetworkId.toString(16)}` : `0`}
            onChange={handleNetworkSwitch}
            style={{ maxWidth: '200px', marginRight: '20px' }}
          >
            <option value="0" disabled>Select Network</option>
            <option value="0x7A69">Localhost (Hardhat)</option>
            <option value="0x5">Goerli Testnet</option>
          </Form.Select>

          {/* Wallet Connection Status */}
          {connectedWalletAddress ? (
            // Show connected account with identicon
            <Navbar.Text className='d-flex align-items-center'>
              {/* Truncated wallet address (0x123...abcd format) */}
              {connectedWalletAddress.slice(0, 5) + '...' + connectedWalletAddress.slice(38, 42)}

              {/* Blockies identicon - unique visual representation of wallet address */}
              <Blockies
                seed={connectedWalletAddress}  // Use wallet address as seed for unique pattern
                size={10}                      // Size of each block in the identicon
                scale={3}                      // Scale factor for the identicon
                color="#2187D0"               // Primary color (blue)
                bgColor="#F1F2F9"             // Background color (light gray)
                spotColor="#767F92"           // Accent color (dark gray)
                className="identicon mx-2"
              />
            </Navbar.Text>
          ) : (
            // Show connect button when wallet not connected
            <Button
              onClick={handleWalletConnection}
              variant="primary"
            >
              Connect Wallet
            </Button>
          )}

        </div>
      </Navbar.Collapse>
    </Navbar>
  );
}

export default Navigation;
