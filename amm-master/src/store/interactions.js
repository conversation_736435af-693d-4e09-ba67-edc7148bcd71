import { ethers } from 'ethers'

// Import Redux action creators for updating global state
// These functions update different parts of the app's state
import {
  setProvider,    // Stores the Web3 provider (MetaMask connection)
  setNetwork,     // Stores current blockchain network ID
  setAccount      // Stores current user's wallet address
} from './reducers/provider'

import {
  setContracts,   // Stores token contract instances (DAPP, USD)
  setSymbols,     // Stores token symbols for UI display
  balancesLoaded  // Stores user's token balances
} from './reducers/tokens'

import {
  setContract,      // Stores AMM contract instance
  sharesLoaded,     // Stores user's liquidity provider shares
  swapsLoaded,      // Stores historical swap data for charts
  depositRequest,   // Sets loading state when depositing liquidity
  depositSuccess,   // Sets success state after deposit
  depositFail,      // Sets error state if deposit fails
  withdrawRequest,  // Sets loading state when withdrawing liquidity
  withdrawSuccess,  // Sets success state after withdrawal
  withdrawFail,     // Sets error state if withdrawal fails
  swapRequest,      // Sets loading state when swapping tokens
  swapSuccess,      // Sets success state after swap
  swapFail          // Sets error state if swap fails
} from './reducers/amm'

// Import contract ABIs (Application Binary Interfaces)
// These define how to interact with the smart contracts
import TOKEN_ABI from '../abis/Token.json';  // Token contract interface
import AMM_ABI from '../abis/AMM.json';      // AMM contract interface
import config from '../config.json';         // Contract addresses per network

/**
 * @function loadProvider
 * @description Connects to MetaMask and creates Web3 provider
 * @param {Function} dispatch Redux dispatch function
 * @returns {Object} ethers provider instance
 * @notice This is the first function called to establish blockchain connection
 * Used by: App.js during initialization
 */
export const loadProvider = (dispatch) => {
  // Create provider that connects to MetaMask
  // window.ethereum is injected by MetaMask browser extension
  const web3Provider = new ethers.providers.Web3Provider(window.ethereum)

  // Store provider in Redux state so other components can use it
  dispatch(setProvider(web3Provider))

  return web3Provider
}

/**
 * @function loadNetwork
 * @description Gets current blockchain network information
 * @param {Object} provider ethers provider instance
 * @param {Function} dispatch Redux dispatch function
 * @returns {Number} chainId of current network
 * @notice chainId determines which contracts to load (31337 = Hardhat local)
 * Used by: App.js to determine which contract addresses to use
 */
export const loadNetwork = async (provider, dispatch) => {
  // Get network info from provider
  const { chainId } = await provider.getNetwork()

  // Store network ID in Redux state
  dispatch(setNetwork(chainId))

  return chainId
}

/**
 * @function loadAccount
 * @description Gets current user's wallet address from MetaMask
 * @param {Function} dispatch Redux dispatch function
 * @returns {String} user's wallet address
 * @notice This triggers MetaMask to show account selection if not connected
 * Used by: App.js during initialization and Navigation.js when connecting wallet
 */
export const loadAccount = async (dispatch) => {
  // Request account access from MetaMask (shows popup if not connected)
  const walletAccounts = await window.ethereum.request({ method: 'eth_requestAccounts' })

  // Get the first account and ensure it's properly formatted
  const userWalletAddress = ethers.utils.getAddress(walletAccounts[0])

  // Store account address in Redux state
  dispatch(setAccount(userWalletAddress))

  return userWalletAddress
}

// ==============================================================================
// CONTRACT LOADING FUNCTIONS
// These functions create contract instances that the frontend can interact with
// ==============================================================================

/**
 * @function loadTokens
 * @description Loads both token contracts (DAPP and USD) for the AMM
 * @param {Object} provider ethers provider instance
 * @param {Number} chainId current network ID
 * @param {Function} dispatch Redux dispatch function
 * @notice Contract addresses come from config.json based on chainId
 * Used by: App.js during initialization
 */
export const loadTokens = async (provider, chainId, dispatch) => {
  // Create contract instances for both tokens in the trading pair
  // config[chainId] gets the right addresses for current network (local/mainnet/etc)
  const dappTokenContract = new ethers.Contract(
    config[chainId].dapp.address,  // DAPP token address from config.json
    TOKEN_ABI,                     // Token contract interface
    provider                       // Connection to blockchain
  )

  const usdTokenContract = new ethers.Contract(
    config[chainId].usd.address,   // USD token address from config.json
    TOKEN_ABI,                     // Token contract interface
    provider                       // Connection to blockchain
  )

  // Store contract instances in Redux state for use by other components
  dispatch(setContracts([dappTokenContract, usdTokenContract]))

  // Get and store token symbols (DAPP, USD) for UI display
  const tokenSymbols = [
    await dappTokenContract.symbol(),  // Gets "DAPP" from contract
    await usdTokenContract.symbol()    // Gets "USD" from contract
  ]
  dispatch(setSymbols(tokenSymbols))
}

/**
 * @function loadAMM
 * @description Loads the main AMM contract for swapping and liquidity operations
 * @param {Object} provider ethers provider instance
 * @param {Number} chainId current network ID
 * @param {Function} dispatch Redux dispatch function
 * @returns {Object} AMM contract instance
 * @notice This contract handles all swapping and liquidity operations
 * Used by: App.js during initialization
 */
export const loadAMM = async (provider, chainId, dispatch) => {
  // Create AMM contract instance
  const ammContract = new ethers.Contract(
    config[chainId].amm.address,   // AMM contract address from config.json
    AMM_ABI,                       // AMM contract interface
    provider                       // Connection to blockchain
  )

  // Store AMM contract in Redux state for use by Swap, Deposit, Withdraw components
  dispatch(setContract(ammContract))

  return ammContract
}


// ------------------------------------------------------------------------------
// LOAD BALANCES & SHARES
export const loadBalances = async (amm, tokens, account, dispatch) => {
  const balance1 = await tokens[0].balanceOf(account)
  const balance2 = await tokens[1].balanceOf(account)

  dispatch(balancesLoaded([
    ethers.utils.formatUnits(balance1.toString(), 'ether'),
    ethers.utils.formatUnits(balance2.toString(), 'ether')
  ]))

  const shares = await amm.shares(account)
  dispatch(sharesLoaded(ethers.utils.formatUnits(shares.toString(), 'ether')))
}


// ------------------------------------------------------------------------------
// ADD LIQUDITY
export const addLiquidity = async (provider, amm, tokens, amounts, dispatch) => {
  try {
    dispatch(depositRequest())

    const signer = await provider.getSigner()

    let transaction

    transaction = await tokens[0].connect(signer).approve(amm.address, amounts[0])
    await transaction.wait()

    transaction = await tokens[1].connect(signer).approve(amm.address, amounts[1])
    await transaction.wait()

    transaction = await amm.connect(signer).addLiquidity(amounts[0], amounts[1])
    await transaction.wait()

    dispatch(depositSuccess(transaction.hash))
  } catch (error) {
    dispatch(depositFail())
  }
}

// ------------------------------------------------------------------------------
// REMOVE LIQUDITY
export const removeLiquidity = async (provider, amm, shares, dispatch) => {
  try {
    dispatch(withdrawRequest())

    const signer = await provider.getSigner()

    let transaction = await amm.connect(signer).removeLiquidity(shares)
    await transaction.wait()

    dispatch(withdrawSuccess(transaction.hash))
  } catch (error) {
    dispatch(withdrawFail())
  }
}

// ------------------------------------------------------------------------------
// SWAP

export const swap = async (provider, amm, token, symbol, amount, dispatch) => {
  try {

    dispatch(swapRequest())

    let transaction

    const signer = await provider.getSigner()

    transaction = await token.connect(signer).approve(amm.address, amount)
    await transaction.wait()

    if (symbol === "DAPP") {
      transaction = await amm.connect(signer).swapToken1(amount)
    } else {
      transaction = await amm.connect(signer).swapToken2(amount)
    }

    await transaction.wait()

    dispatch(swapSuccess(transaction.hash))

  } catch (error) {
    dispatch(swapFail())
  }
}


// ------------------------------------------------------------------------------
// LOAD ALL SWAPS

export const loadAllSwaps = async (provider, amm, dispatch) => {
  const block = await provider.getBlockNumber()

  const swapStream = await amm.queryFilter('Swap', 0, block)
  const swaps = swapStream.map(event => {
    return { hash: event.transactionHash, args: event.args }
  })

  dispatch(swapsLoaded(swaps))
}
