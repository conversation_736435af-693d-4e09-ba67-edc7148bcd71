/* Golden Heart Innovations - Main Stylesheet */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #1e40af;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --font-family: 'Inter', sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #ffffff;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.display-4, .display-5, .display-6 {
    font-weight: 700;
}

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    padding-top: 80px;
    padding-bottom: 60px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: auto;
    display: flex;
    align-items: center;
}

.hero-video-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.video-placeholder {
    background: var(--primary-color);
    color: white;
    padding: 2.5rem 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.video-placeholder:hover {
    background: #1e3a8a;
    transform: translateY(-2px);
}

.video-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.video-placeholder p {
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

/* Button Styles */
.btn {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1e3a8a;
    border-color: #1e3a8a;
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid #e2e8f0;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 1rem;
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #d1d5db;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Alert Styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Loading Spinner */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Search Results */
.search-result-item {
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.search-result-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow);
}

.patent-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color) !important;
}

footer a:hover {
    color: var(--secondary-color) !important;
}

.social-links a {
    transition: var(--transition);
}

.social-links a:hover {
    transform: translateY(-2px);
    color: var(--secondary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 80px;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .video-placeholder {
        padding: 2rem 1rem;
    }
    
    .video-placeholder i {
        font-size: 3rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e3a8a;
}
