/* Golden Heart Innovations - Portal Specific Styles */

/* Main Content Spacing */
.main-content {
    padding-top: 100px;
    min-height: calc(100vh - 200px);
}

/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e2e8f0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0 1rem;
    min-width: 120px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e2e8f0;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    text-align: center;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step.active .step-title {
    color: var(--primary-color);
}

.step.completed .step-number {
    background: var(--success-color);
    color: white;
}

.step.completed .step-title {
    color: var(--success-color);
}

/* Form Steps */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* File Upload Area */
.file-upload-area {
    position: relative;
    border: 2px dashed #d1d5db;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f8fafc;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: #eff6ff;
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    pointer-events: none;
}

/* File List */
.file-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background: #f8fafc;
}

.file-info {
    flex: 1;
    display: flex;
    align-items: center;
}

.file-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.75rem;
    color: var(--primary-color);
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: #64748b;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-remove {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.file-remove:hover {
    background: #fee2e2;
}

/* Search Results */
.search-result-item {
    background: white;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
}

.patent-meta {
    text-align: right;
}

.patent-number {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Business Portal Styles */
.subscription-card {
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.subscription-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.subscription-card.featured {
    border-color: var(--primary-color);
    position: relative;
}

.subscription-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.price-display {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-period {
    font-size: 1rem;
    color: #64748b;
    font-weight: normal;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li i {
    color: var(--success-color);
    margin-right: 0.75rem;
    width: 16px;
}

/* Dashboard Styles */
.dashboard-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e3a8a 100%);
    color: white;
    border: none;
}

.dashboard-stat {
    text-align: center;
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Login/Register Forms */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.auth-card {
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    border: none;
}

.auth-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
}

.auth-logo {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Patent Database Styles */
.patent-card {
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background: white;
}

.patent-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.patent-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.patent-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.patent-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.patent-meta-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: #f8fafc;
    border-radius: var(--border-radius);
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.meta-value {
    font-weight: 500;
    color: var(--dark-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding-top: 80px;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .progress-steps::before {
        display: none;
    }
    
    .step {
        min-width: auto;
        margin-bottom: 1rem;
    }
    
    .file-upload-area {
        padding: 1.5rem 1rem;
    }
    
    .patent-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .patent-badges {
        margin-top: 1rem;
    }
    
    .patent-meta-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .auth-card {
        margin: 1rem;
    }
    
    .auth-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .price-display {
        font-size: 2rem;
    }
    
    .dashboard-stat {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    width: 60%;
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* Success/Error States */
.success-state {
    text-align: center;
    padding: 3rem 2rem;
}

.error-state {
    text-align: center;
    padding: 3rem 2rem;
}

.state-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.state-description {
    color: #64748b;
    margin-bottom: 2rem;
}

/* Custom Scrollbar for Portal Areas */
.portal-content::-webkit-scrollbar {
    width: 6px;
}

.portal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.portal-content::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.portal-content::-webkit-scrollbar-thumb:hover {
    background: #1e3a8a;
}
