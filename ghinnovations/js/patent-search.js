/**
 * Golden Heart Innovations - Patent Search Functionality
 * Handles USPTO API integration and search interface
 */

// Patent Search Configuration
const PATENT_SEARCH_CONFIG = {
    USPTO_API_BASE: 'https://developer.uspto.gov/api/v1/',
    PATENTS_VIEW_API: 'https://api.patentsview.org/patents/query',
    RESULTS_PER_PAGE: 10,
    MAX_RESULTS: 100,
    DEBOUNCE_DELAY: 500
};

// Patent Search Application
const PatentSearchApp = {
    currentPage: 1,
    totalResults: 0,
    currentQuery: '',
    searchHistory: [],

    /**
     * Initialize the patent search application
     */
    init: function() {
        this.setupEventListeners();
        this.loadSearchHistory();
        this.setupAutoComplete();
        console.log('Patent Search App initialized');
    },

    /**
     * Setup event listeners for search functionality
     */
    setupEventListeners: function() {
        // Main search form
        const searchForm = document.getElementById('patent-search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }

        // Search input with debounced suggestions
        const searchInput = document.getElementById('search-query');
        if (searchInput) {
            searchInput.addEventListener('input', 
                Utils.debounce(this.handleSearchInput.bind(this), PATENT_SEARCH_CONFIG.DEBOUNCE_DELAY)
            );
        }

        // Sort dropdown
        document.querySelectorAll('[data-sort]').forEach(item => {
            item.addEventListener('click', this.handleSort.bind(this));
        });

        // Clear search button
        window.clearSearch = this.clearSearch.bind(this);
    },

    /**
     * Handle search form submission
     */
    handleSearch: function(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const searchParams = {
            query: formData.get('query').trim(),
            patent_type: formData.get('patent_type'),
            status: formData.get('status'),
            date_from: formData.get('date_from'),
            date_to: formData.get('date_to'),
            industry: formData.get('industry'),
            inventor: formData.get('inventor')
        };

        if (!searchParams.query) {
            Utils.showAlert('Please enter a search query', 'warning');
            return;
        }

        this.currentQuery = searchParams.query;
        this.currentPage = 1;
        this.performSearch(searchParams);
        this.addToSearchHistory(searchParams.query);
    },

    /**
     * Handle search input for suggestions
     */
    handleSearchInput: function(e) {
        const query = e.target.value.trim();
        if (query.length >= 3) {
            this.generateSearchSuggestions(query);
        } else {
            this.hideSuggestions();
        }
    },

    /**
     * Perform the actual patent search
     */
    performSearch: async function(searchParams) {
        this.showLoading();
        this.hideResults();
        this.hideNoResults();

        try {
            // Try real API first, fallback to mock if needed
            let results;
            try {
                results = await this.searchPatentsView(searchParams);
            } catch (apiError) {
                console.warn('API failed, using mock data:', apiError);
                results = this.getMockResults(searchParams.query);
            }

            console.log('Full API results:', results);

            if (results && results.patents && results.patents.length > 0) {
                this.displayResults(results.patents, results.total_patent_count || results.patents.length);
            } else if (results && results.error) {
                console.error('API returned error:', results.error);
                Utils.showAlert(`Search error: ${results.error}`, 'danger');
                this.showNoResults();
            } else {
                console.log('No results found for query');
                this.showNoResults();
            }
        } catch (error) {
            console.error('Search error:', error);

            // Show more detailed error information
            let errorMessage = 'Search failed. ';
            if (error.message.includes('Failed to fetch')) {
                errorMessage += 'Please check your internet connection.';
            } else if (error.message.includes('API request failed')) {
                errorMessage += 'The patent database is temporarily unavailable.';
            } else {
                errorMessage += 'Please try again with different search terms.';
            }

            Utils.showAlert(errorMessage, 'danger');
            this.showNoResults();
        } finally {
            this.hideLoading();
        }
    },

    /**
     * Search using PatentsView API
     */
    searchPatentsView: async function(searchParams) {
        const queryParams = this.buildPatentsViewQuery(searchParams);

        const requestBody = {
            q: queryParams,
            f: [
                "patent_number",
                "patent_title",
                "patent_abstract",
                "patent_date",
                "patent_type",
                "inventor_first_name",
                "inventor_last_name",
                "assignee_organization",
                "assignee_type",
                "cpc_category",
                "cpc_subcategory",
                "uspc_class",
                "uspc_subclass",
                "patent_processing_time",
                "patent_kind",
                "patent_num_cited_by_us_patents",
                "patent_num_foreign_citations",
                "patent_num_us_patent_citations"
            ],
            o: {
                page: this.currentPage,
                per_page: PATENT_SEARCH_CONFIG.RESULTS_PER_PAGE
            }
        };

        // Debug logging
        console.log('Search params:', searchParams);
        console.log('Query params:', queryParams);
        console.log('Request body:', requestBody);

        const response = await fetch(PATENT_SEARCH_CONFIG.PATENTS_VIEW_API, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            console.error('API response not OK:', response.status, response.statusText);
            throw new Error(`API request failed: ${response.status}`);
        }

        const result = await response.json();
        console.log('API response:', result);
        return result;
    },

    /**
     * Build query for PatentsView API
     */
    buildPatentsViewQuery: function(searchParams) {
        let query = {};

        // Main search query - simplified for better results
        if (searchParams.query) {
            // Check if it's a patent number
            if (/^US\d+/.test(searchParams.query.toUpperCase())) {
                query.patent_number = searchParams.query.toUpperCase();
            } else {
                // Simple text search in title and abstract
                query._text_any = searchParams.query;
            }
        }

        // Date filters
        if (searchParams.date_from || searchParams.date_to) {
            query.patent_date = {};
            if (searchParams.date_from) {
                query.patent_date.gte = searchParams.date_from;
            }
            if (searchParams.date_to) {
                query.patent_date.lte = searchParams.date_to;
            }
        }

        // Patent type filter
        if (searchParams.patent_type) {
            query.patent_type = searchParams.patent_type;
        }

        // Inventor filter - simplified
        if (searchParams.inventor) {
            query._text_any = query._text_any ?
                `${query._text_any} ${searchParams.inventor}` :
                searchParams.inventor;
        }

        return query;
    },

    /**
     * Display search results
     */
    displayResults: function(patents, totalCount) {
        this.totalResults = totalCount;
        
        const resultsContainer = document.getElementById('results-container');
        const resultsCount = document.getElementById('results-count');
        
        if (resultsCount) {
            resultsCount.textContent = `${totalCount.toLocaleString()} results found`;
        }

        if (resultsContainer) {
            resultsContainer.innerHTML = patents.map(patent => this.createPatentCard(patent)).join('');
        }

        this.showResults();
        this.updatePagination();
    },

    /**
     * Create HTML for a patent result card
     */
    createPatentCard: function(patent) {
        const inventors = this.formatInventors(patent.inventors || []);
        const assignee = patent.assignees && patent.assignees[0] ? patent.assignees[0].assignee_organization : 'N/A';
        const assigneeType = patent.assignees && patent.assignees[0] ? patent.assignees[0].assignee_type : '';
        const patentDate = patent.patent_date ? Utils.formatDate(patent.patent_date) : 'N/A';
        const citationCount = patent.patent_num_cited_by_us_patents || 0;
        const cpcCategory = patent.cpcs && patent.cpcs[0] ? patent.cpcs[0].cpc_category : '';
        const processingTime = patent.patent_processing_time ? `${patent.patent_processing_time} days` : 'N/A';

        return `
            <div class="search-result-item">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="mb-2">
                            <span class="patent-number">${patent.patent_number}</span>
                            ${patent.patent_title || 'No title available'}
                        </h5>
                        <p class="text-muted mb-2">
                            <strong>Inventors:</strong> ${inventors}<br>
                            <strong>Assignee:</strong> ${assignee} ${assigneeType ? `(${assigneeType})` : ''}<br>
                            <strong>Date:</strong> ${patentDate} | <strong>Citations:</strong> ${citationCount}
                            ${cpcCategory ? `<br><strong>Category:</strong> ${cpcCategory}` : ''}
                        </p>
                        <p class="mb-3">${this.truncateText(patent.patent_abstract || 'No abstract available', 200)}</p>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-sm btn-outline-primary" onclick="PatentSearchApp.viewPatentDetails('${patent.patent_number}')">
                                <i class="fas fa-eye me-1"></i>View Details
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="PatentSearchApp.savePatent('${patent.patent_number}')">
                                <i class="fas fa-bookmark me-1"></i>Save
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="PatentSearchApp.showPatentAnalysis('${patent.patent_number}')">
                                <i class="fas fa-chart-line me-1"></i>Analysis
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="patent-meta">
                            <span class="badge bg-primary mb-2">${patent.patent_type || 'Utility'}</span>
                            ${citationCount > 10 ? '<span class="badge bg-success mb-2 ms-1">High Impact</span>' : ''}
                            ${citationCount > 50 ? '<span class="badge bg-warning mb-2 ms-1">Very Popular</span>' : ''}
                            <br>
                            <small class="text-muted">Patent #${patent.patent_number}</small>
                            ${processingTime !== 'N/A' ? `<br><small class="text-muted">Processing: ${processingTime}</small>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * Format inventors list
     */
    formatInventors: function(inventors) {
        if (!inventors || inventors.length === 0) return 'N/A';
        
        return inventors.slice(0, 3).map(inv => 
            `${inv.inventor_first_name || ''} ${inv.inventor_last_name || ''}`.trim()
        ).join(', ') + (inventors.length > 3 ? ` and ${inventors.length - 3} more` : '');
    },

    /**
     * Truncate text to specified length
     */
    truncateText: function(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    },

    /**
     * Generate AI-powered search suggestions
     */
    generateSearchSuggestions: function(query) {
        // Simulate AI suggestions based on common patent search patterns
        const suggestions = this.getSearchSuggestions(query);
        
        if (suggestions.length > 0) {
            this.displaySuggestions(suggestions);
        } else {
            this.hideSuggestions();
        }
    },

    /**
     * Get search suggestions based on query
     */
    getSearchSuggestions: function(query) {
        const suggestions = [];
        const lowerQuery = query.toLowerCase();

        // Enhanced technology-based suggestions
        const techKeywords = {
            'ai': ['artificial intelligence', 'machine learning', 'neural network', 'deep learning', 'computer vision'],
            'wireless': ['wireless communication', 'bluetooth', 'wifi', '5G', 'antenna design', 'radio frequency'],
            'battery': ['lithium battery', 'battery management', 'energy storage', 'charging system', 'fuel cell'],
            'medical': ['medical device', 'pharmaceutical', 'diagnostic', 'surgical instrument', 'biomedical'],
            'automotive': ['vehicle', 'autonomous driving', 'electric vehicle', 'engine', 'transmission'],
            'software': ['algorithm', 'data processing', 'user interface', 'database', 'encryption'],
            'manufacturing': ['assembly line', 'quality control', 'automation', 'robotics', 'material handling'],
            'energy': ['solar panel', 'wind turbine', 'power generation', 'renewable energy', 'grid system'],
            'semiconductor': ['microprocessor', 'integrated circuit', 'chip design', 'memory device', 'sensor'],
            'biotechnology': ['gene therapy', 'protein synthesis', 'DNA sequencing', 'cell culture', 'vaccine'],
            'aerospace': ['aircraft', 'satellite', 'propulsion', 'navigation', 'flight control'],
            'telecommunications': ['network protocol', 'data transmission', 'signal processing', 'fiber optic', 'switching']
        };

        // Add related terms based on query
        Object.keys(techKeywords).forEach(key => {
            if (lowerQuery.includes(key)) {
                suggestions.push(...techKeywords[key]);
            }
        });

        // Add common patent search patterns
        if (lowerQuery.includes('method')) {
            suggestions.push('method for', 'process for', 'technique for', 'system and method');
        }

        if (lowerQuery.includes('system')) {
            suggestions.push('system for', 'apparatus for', 'device for', 'system and method');
        }

        if (lowerQuery.includes('device')) {
            suggestions.push('portable device', 'electronic device', 'medical device', 'communication device');
        }

        return [...new Set(suggestions)].slice(0, 8);
    },

    /**
     * Display search suggestions
     */
    displaySuggestions: function(suggestions) {
        const suggestionsContainer = document.getElementById('suggestions-content');
        const suggestionsSection = document.getElementById('search-suggestions');
        
        if (suggestionsContainer && suggestionsSection) {
            suggestionsContainer.innerHTML = suggestions.map(suggestion => 
                `<button class="btn btn-sm btn-outline-info me-2 mb-2" onclick="PatentSearchApp.applySuggestion('${suggestion}')">${suggestion}</button>`
            ).join('');
            
            suggestionsSection.style.display = 'block';
        }
    },

    /**
     * Hide suggestions
     */
    hideSuggestions: function() {
        const suggestionsSection = document.getElementById('search-suggestions');
        if (suggestionsSection) {
            suggestionsSection.style.display = 'none';
        }
    },

    /**
     * Apply a suggestion to the search
     */
    applySuggestion: function(suggestion) {
        const searchInput = document.getElementById('search-query');
        if (searchInput) {
            searchInput.value = suggestion;
            this.hideSuggestions();
        }
    },

    /**
     * View patent details
     */
    viewPatentDetails: function(patentNumber) {
        // Open USPTO patent page in new tab
        const url = `https://patents.uspto.gov/patent/search?query=${patentNumber}`;
        window.open(url, '_blank');
    },

    /**
     * Save patent for later
     */
    savePatent: function(patentNumber) {
        // Add to local storage for now (could be enhanced with user accounts)
        let savedPatents = JSON.parse(localStorage.getItem('savedPatents') || '[]');

        if (!savedPatents.includes(patentNumber)) {
            savedPatents.push(patentNumber);
            localStorage.setItem('savedPatents', JSON.stringify(savedPatents));
            Utils.showAlert(`Patent ${patentNumber} saved successfully!`, 'success');
        } else {
            Utils.showAlert(`Patent ${patentNumber} is already saved.`, 'info');
        }
    },

    /**
     * Show patent analysis modal
     */
    showPatentAnalysis: function(patentNumber) {
        // Create modal for patent analysis
        const modalHtml = `
            <div class="modal fade" id="patentAnalysisModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Patent Analysis - ${patentNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="analysis-loading" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2">Analyzing patent data...</p>
                            </div>
                            <div id="analysis-content" style="display: none;"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="PatentSearchApp.generateReport('${patentNumber}')">
                                <i class="fas fa-download me-1"></i>Download Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('patentAnalysisModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('patentAnalysisModal'));
        modal.show();

        // Load analysis data
        this.loadPatentAnalysis(patentNumber);
    },

    /**
     * Load patent analysis data
     */
    loadPatentAnalysis: async function(patentNumber) {
        try {
            // Simulate analysis (in real implementation, this would call your backend)
            await new Promise(resolve => setTimeout(resolve, 2000));

            const analysisData = {
                citationTrend: 'Increasing',
                marketPotential: 'High',
                competitorCount: 15,
                licensingOpportunity: 'Excellent',
                technicalComplexity: 'Medium',
                patentStrength: 85
            };

            const analysisHtml = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Patent Strength</h6>
                                <div class="display-6 text-primary">${analysisData.patentStrength}%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Market Potential</h6>
                                <div class="h4 text-success">${analysisData.marketPotential}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Citation Trend</h6>
                                <div class="h5 text-info">${analysisData.citationTrend}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">Competitors</h6>
                                <div class="h5 text-warning">${analysisData.competitorCount}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>Licensing Recommendation</h6>
                            <p class="mb-0">This patent shows ${analysisData.licensingOpportunity.toLowerCase()} licensing potential based on citation patterns, market demand, and competitive landscape analysis.</p>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('analysis-loading').style.display = 'none';
            document.getElementById('analysis-content').style.display = 'block';
            document.getElementById('analysis-content').innerHTML = analysisHtml;

        } catch (error) {
            console.error('Analysis error:', error);
            document.getElementById('analysis-loading').innerHTML = '<p class="text-danger">Analysis failed. Please try again.</p>';
        }
    },

    /**
     * Generate analysis report
     */
    generateReport: function(patentNumber) {
        Utils.showAlert('Report generation feature coming soon!', 'info');
    },

    /**
     * Handle sorting
     */
    handleSort: function(e) {
        e.preventDefault();
        const sortBy = e.target.getAttribute('data-sort');
        // Implement sorting logic here
        console.log('Sort by:', sortBy);
    },

    /**
     * Update pagination
     */
    updatePagination: function() {
        const totalPages = Math.ceil(this.totalResults / PATENT_SEARCH_CONFIG.RESULTS_PER_PAGE);
        const paginationContainer = document.getElementById('pagination-container');
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        // Create pagination HTML
        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="PatentSearchApp.goToPage(${this.currentPage - 1})">Previous</a></li>`;
        }

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="PatentSearchApp.goToPage(${i})">${i}</a></li>`;
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="PatentSearchApp.goToPage(${this.currentPage + 1})">Next</a></li>`;
        }

        paginationContainer.querySelector('.pagination').innerHTML = paginationHTML;
        paginationContainer.style.display = 'block';
    },

    /**
     * Go to specific page
     */
    goToPage: function(page) {
        this.currentPage = page;
        const searchForm = document.getElementById('patent-search-form');
        if (searchForm) {
            const formData = new FormData(searchForm);
            const searchParams = {
                query: formData.get('query').trim(),
                patent_type: formData.get('patent_type'),
                status: formData.get('status'),
                date_from: formData.get('date_from'),
                date_to: formData.get('date_to'),
                industry: formData.get('industry'),
                inventor: formData.get('inventor')
            };
            this.performSearch(searchParams);
        }
    },

    /**
     * Add search to history
     */
    addToSearchHistory: function(query) {
        if (!this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query);
            this.searchHistory = this.searchHistory.slice(0, 10); // Keep last 10 searches
            localStorage.setItem('patentSearchHistory', JSON.stringify(this.searchHistory));
            this.displaySearchHistory();
        }
    },

    /**
     * Load search history from localStorage
     */
    loadSearchHistory: function() {
        this.searchHistory = JSON.parse(localStorage.getItem('patentSearchHistory') || '[]');
        if (this.searchHistory.length > 0) {
            this.displaySearchHistory();
        }
    },

    /**
     * Display search history
     */
    displaySearchHistory: function() {
        const historyContainer = document.getElementById('history-container');
        const historySection = document.getElementById('search-history');
        
        if (historyContainer && this.searchHistory.length > 0) {
            historyContainer.innerHTML = this.searchHistory.map(query => 
                `<button class="btn btn-sm btn-outline-secondary" onclick="PatentSearchApp.repeatSearch('${query}')">${query}</button>`
            ).join('');
            
            if (historySection) {
                historySection.style.display = 'block';
            }
        }
    },

    /**
     * Repeat a previous search
     */
    repeatSearch: function(query) {
        const searchInput = document.getElementById('search-query');
        if (searchInput) {
            searchInput.value = query;
            document.getElementById('patent-search-form').dispatchEvent(new Event('submit'));
        }
    },

    /**
     * Clear search and reset form
     */
    clearSearch: function() {
        document.getElementById('patent-search-form').reset();
        this.hideResults();
        this.hideNoResults();
        this.hideSuggestions();
        this.currentPage = 1;
        this.totalResults = 0;
        this.currentQuery = '';
    },

    /**
     * Setup autocomplete functionality
     */
    setupAutoComplete: function() {
        // Could be enhanced with more sophisticated autocomplete
        console.log('Autocomplete setup complete');
    },

    /**
     * Get mock results for testing
     */
    getMockResults: function(query) {
        const mockPatents = [
            {
                patent_number: "US10123456",
                patent_title: `Automated ${query} door system with smart sensors`,
                patent_abstract: `An innovative system for automatically controlling ${query} doors using advanced sensor technology and machine learning algorithms.`,
                patent_date: "2020-01-15",
                patent_type: "utility",
                inventors: [
                    {inventor_first_name: "John", inventor_last_name: "Smith"},
                    {inventor_first_name: "Jane", inventor_last_name: "Doe"}
                ],
                assignees: [
                    {assignee_organization: "Smart Home Technologies Inc.", assignee_type: "company"}
                ],
                patent_num_cited_by_us_patents: 25
            },
            {
                patent_number: "US10234567",
                patent_title: `Improved ${query} ventilation system`,
                patent_abstract: `A novel approach to ${query} ventilation that reduces energy consumption while maintaining optimal air quality.`,
                patent_date: "2019-08-22",
                patent_type: "utility",
                inventors: [
                    {inventor_first_name: "Michael", inventor_last_name: "Johnson"}
                ],
                assignees: [
                    {assignee_organization: "EcoVent Solutions", assignee_type: "company"}
                ],
                patent_num_cited_by_us_patents: 12
            },
            {
                patent_number: "US10345678",
                patent_title: `Smart ${query} storage organization system`,
                patent_abstract: `An intelligent storage system for ${query} spaces that automatically organizes items and provides inventory tracking.`,
                patent_date: "2021-03-10",
                patent_type: "utility",
                inventors: [
                    {inventor_first_name: "Sarah", inventor_last_name: "Wilson"},
                    {inventor_first_name: "David", inventor_last_name: "Brown"}
                ],
                assignees: [
                    {assignee_organization: "OrganizeTech LLC", assignee_type: "company"}
                ],
                patent_num_cited_by_us_patents: 8
            }
        ];

        return {
            patents: mockPatents,
            total_patent_count: mockPatents.length
        };
    },

    // UI Helper Methods
    showLoading: function() {
        document.getElementById('loading-indicator').style.display = 'block';
    },

    hideLoading: function() {
        document.getElementById('loading-indicator').style.display = 'none';
    },

    showResults: function() {
        document.getElementById('search-results').style.display = 'block';
    },

    hideResults: function() {
        document.getElementById('search-results').style.display = 'none';
    },

    showNoResults: function() {
        document.getElementById('no-results').style.display = 'block';
    },

    hideNoResults: function() {
        document.getElementById('no-results').style.display = 'none';
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    PatentSearchApp.init();
});

// Export for global access
window.PatentSearchApp = PatentSearchApp;
