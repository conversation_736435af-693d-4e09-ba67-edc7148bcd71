/**
 * Golden Heart Innovations - Main JavaScript File
 * Handles core functionality, animations, and user interactions
 */

// Global configuration
const CONFIG = {
    API_BASE_URL: '/php/',
    YOUTUBE_VIDEO_ID: '', // To be configured with actual video ID
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 500
};

// Utility functions
const Utils = {
    /**
     * Debounce function to limit API calls
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Show loading spinner
     */
    showLoading: function(element) {
        if (element) {
            element.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
        }
    },

    /**
     * Hide loading spinner
     */
    hideLoading: function(element) {
        if (element) {
            element.innerHTML = '';
        }
    },

    /**
     * Show alert message
     */
    showAlert: function(message, type = 'info', container = null) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        if (container) {
            container.innerHTML = alertHtml;
        } else {
            // Create a temporary container at the top of the page
            const tempContainer = document.createElement('div');
            tempContainer.innerHTML = alertHtml;
            tempContainer.style.position = 'fixed';
            tempContainer.style.top = '20px';
            tempContainer.style.left = '50%';
            tempContainer.style.transform = 'translateX(-50%)';
            tempContainer.style.zIndex = '9999';
            tempContainer.style.width = '90%';
            tempContainer.style.maxWidth = '500px';
            document.body.appendChild(tempContainer);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (tempContainer.parentNode) {
                    tempContainer.parentNode.removeChild(tempContainer);
                }
            }, 5000);
        }
    },

    /**
     * Validate email format
     */
    validateEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * Format date for display
     */
    formatDate: function(dateString) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    },

    /**
     * Sanitize HTML to prevent XSS
     */
    sanitizeHtml: function(str) {
        const temp = document.createElement('div');
        temp.textContent = str;
        return temp.innerHTML;
    }
};

// Main application object
const GoldenHeartApp = {
    /**
     * Initialize the application
     */
    init: function() {
        this.setupEventListeners();
        this.setupAnimations();
        this.setupYouTubePlayer();
        this.setupFormValidation();
        console.log('Golden Heart Innovations app initialized');
    },

    /**
     * Setup event listeners
     */
    setupEventListeners: function() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Video placeholder click handler
        const videoPlaceholder = document.querySelector('.video-placeholder');
        if (videoPlaceholder) {
            videoPlaceholder.addEventListener('click', this.playYouTubeVideo.bind(this));
        }

        // Form submission handlers
        document.addEventListener('submit', this.handleFormSubmission.bind(this));

        // Window scroll handler for navbar
        window.addEventListener('scroll', this.handleScroll.bind(this));
    },

    /**
     * Setup animations
     */
    setupAnimations: function() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe all cards and sections
        document.querySelectorAll('.card, section').forEach(el => {
            observer.observe(el);
        });
    },

    /**
     * Setup YouTube player
     */
    setupYouTubePlayer: function() {
        if (CONFIG.YOUTUBE_VIDEO_ID) {
            const iframe = document.querySelector('#youtube-player iframe');
            if (iframe) {
                iframe.src = `https://www.youtube.com/embed/${CONFIG.YOUTUBE_VIDEO_ID}?autoplay=1&rel=0`;
            }
        }
    },

    /**
     * Play YouTube video
     */
    playYouTubeVideo: function() {
        if (!CONFIG.YOUTUBE_VIDEO_ID) {
            Utils.showAlert('Video will be available soon. Please check back later.', 'info');
            return;
        }

        const placeholder = document.querySelector('.video-placeholder');
        const player = document.querySelector('#youtube-player');
        
        if (placeholder && player) {
            placeholder.style.display = 'none';
            player.classList.remove('d-none');
            this.setupYouTubePlayer();
        }
    },

    /**
     * Handle form submissions
     */
    handleFormSubmission: function(e) {
        const form = e.target;

        // Only handle forms with our custom class, but exclude patent search
        if (!form.classList.contains('ghi-form') || form.id === 'patent-search-form') {
            return;
        }

        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn ? submitBtn.innerHTML : '';
        
        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';
        }

        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }
            
            Utils.showAlert('Form submitted successfully! We will contact you soon.', 'success');
            form.reset();
        }, 2000);
    },

    /**
     * Handle window scroll
     */
    handleScroll: function() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('shadow');
            } else {
                navbar.classList.remove('shadow');
            }
        }
    },

    /**
     * Setup form validation
     */
    setupFormValidation: function() {
        // Real-time validation for email fields
        document.querySelectorAll('input[type="email"]').forEach(input => {
            input.addEventListener('blur', function() {
                const email = this.value.trim();
                if (email && !Utils.validateEmail(email)) {
                    this.classList.add('is-invalid');
                    this.setCustomValidity('Please enter a valid email address');
                } else {
                    this.classList.remove('is-invalid');
                    this.setCustomValidity('');
                }
            });
        });

        // Real-time validation for required fields
        document.querySelectorAll('input[required], textarea[required]').forEach(input => {
            input.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        });
    },

    /**
     * API helper function
     */
    apiCall: async function(endpoint, data = null, method = 'GET') {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(CONFIG.API_BASE_URL + endpoint, options);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            Utils.showAlert('An error occurred. Please try again later.', 'danger');
            throw error;
        }
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    GoldenHeartApp.init();
});

// Export for use in other modules
window.GoldenHeartApp = GoldenHeartApp;
window.Utils = Utils;
