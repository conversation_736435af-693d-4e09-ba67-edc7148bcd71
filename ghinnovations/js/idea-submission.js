/**
 * Golden Heart Innovations - Idea Submission Functionality
 * Handles multi-step form, file uploads, and form validation
 */

// Idea Submission Configuration
const SUBMISSION_CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'],
    MAX_FILES: 10,
    MIN_DESCRIPTION_LENGTH: 100
};

// Idea Submission Application
const IdeaSubmissionApp = {
    currentStep: 1,
    totalSteps: 4,
    uploadedFiles: [],
    formData: {},

    /**
     * Initialize the idea submission application
     */
    init: function() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.setupFormValidation();
        this.updateStepDisplay();
        console.log('Idea Submission App initialized');
    },

    /**
     * Setup event listeners
     */
    setupEventListeners: function() {
        // Navigation buttons
        const nextBtn = document.getElementById('next-btn');
        const prevBtn = document.getElementById('prev-btn');
        const submitBtn = document.getElementById('submit-btn');

        if (nextBtn) {
            nextBtn.addEventListener('click', this.nextStep.bind(this));
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', this.prevStep.bind(this));
        }

        // Form submission
        const form = document.getElementById('idea-submission-form');
        if (form) {
            form.addEventListener('submit', this.handleSubmission.bind(this));
        }

        // Patent status change handler
        const patentStatus = document.getElementById('patent-status');
        if (patentStatus) {
            patentStatus.addEventListener('change', this.handlePatentStatusChange.bind(this));
        }

        // Description character counter
        const description = document.getElementById('idea-description');
        if (description) {
            description.addEventListener('input', this.updateCharacterCount.bind(this));
        }
    },

    /**
     * Setup file upload functionality
     */
    setupFileUpload: function() {
        const fileInput = document.getElementById('file-upload');
        const uploadArea = document.getElementById('file-upload-area');

        if (!fileInput || !uploadArea) return;

        // File input change handler
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleFileDrop.bind(this));

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
    },

    /**
     * Handle file selection
     */
    handleFileSelect: function(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    },

    /**
     * Handle drag over
     */
    handleDragOver: function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    },

    /**
     * Handle drag leave
     */
    handleDragLeave: function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
    },

    /**
     * Handle file drop
     */
    handleFileDrop: function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    },

    /**
     * Process uploaded files
     */
    processFiles: function(files) {
        files.forEach(file => {
            if (this.validateFile(file)) {
                this.addFile(file);
            }
        });
        this.updateFileList();
    },

    /**
     * Validate file
     */
    validateFile: function(file) {
        // Check file size
        if (file.size > SUBMISSION_CONFIG.MAX_FILE_SIZE) {
            Utils.showAlert(`File "${file.name}" is too large. Maximum size is 10MB.`, 'warning');
            return false;
        }

        // Check file type
        const extension = file.name.split('.').pop().toLowerCase();
        if (!SUBMISSION_CONFIG.ALLOWED_FILE_TYPES.includes(extension)) {
            Utils.showAlert(`File type "${extension}" is not allowed.`, 'warning');
            return false;
        }

        // Check total files
        if (this.uploadedFiles.length >= SUBMISSION_CONFIG.MAX_FILES) {
            Utils.showAlert(`Maximum ${SUBMISSION_CONFIG.MAX_FILES} files allowed.`, 'warning');
            return false;
        }

        return true;
    },

    /**
     * Add file to uploaded files list
     */
    addFile: function(file) {
        const fileObj = {
            id: Date.now() + Math.random(),
            file: file,
            name: file.name,
            size: file.size,
            type: file.type
        };
        
        this.uploadedFiles.push(fileObj);
    },

    /**
     * Remove file from uploaded files list
     */
    removeFile: function(fileId) {
        this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
        this.updateFileList();
    },

    /**
     * Update file list display
     */
    updateFileList: function() {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        if (this.uploadedFiles.length === 0) {
            fileList.innerHTML = '';
            return;
        }

        fileList.innerHTML = this.uploadedFiles.map(file => `
            <div class="file-item">
                <div class="file-info">
                    <i class="fas ${this.getFileIcon(file.name)} file-icon"></i>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${this.formatFileSize(file.size)}</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button type="button" class="file-remove" onclick="IdeaSubmissionApp.removeFile(${file.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    },

    /**
     * Get file icon based on file type
     */
    getFileIcon: function(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': 'fa-file-pdf',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'jpg': 'fa-file-image',
            'jpeg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image'
        };
        return iconMap[extension] || 'fa-file';
    },

    /**
     * Format file size for display
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Handle patent status change
     */
    handlePatentStatusChange: function(e) {
        const patentNumberField = document.getElementById('patent-number-field');
        const value = e.target.value;
        
        if (value === 'pending' || value === 'granted') {
            patentNumberField.style.display = 'block';
            document.getElementById('patent-number').required = true;
        } else {
            patentNumberField.style.display = 'none';
            document.getElementById('patent-number').required = false;
        }
    },

    /**
     * Update character count for description
     */
    updateCharacterCount: function(e) {
        const text = e.target.value;
        const minLength = SUBMISSION_CONFIG.MIN_DESCRIPTION_LENGTH;
        const currentLength = text.length;
        
        // Find or create character counter
        let counter = e.target.parentNode.querySelector('.char-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'char-counter form-text';
            e.target.parentNode.appendChild(counter);
        }
        
        if (currentLength < minLength) {
            counter.textContent = `${currentLength}/${minLength} characters (minimum required)`;
            counter.className = 'char-counter form-text text-warning';
        } else {
            counter.textContent = `${currentLength} characters`;
            counter.className = 'char-counter form-text text-success';
        }
    },

    /**
     * Move to next step
     */
    nextStep: function() {
        if (this.validateCurrentStep()) {
            this.saveCurrentStepData();
            
            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.updateStepDisplay();
                
                if (this.currentStep === this.totalSteps) {
                    this.generateSubmissionSummary();
                }
            }
        }
    },

    /**
     * Move to previous step
     */
    prevStep: function() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    },

    /**
     * Validate current step
     */
    validateCurrentStep: function() {
        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (!currentStepElement) return false;

        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
                
                // Special validation for email
                if (field.type === 'email' && !Utils.validateEmail(field.value)) {
                    field.classList.add('is-invalid');
                    isValid = false;
                }
                
                // Special validation for description length
                if (field.id === 'idea-description' && field.value.length < SUBMISSION_CONFIG.MIN_DESCRIPTION_LENGTH) {
                    field.classList.add('is-invalid');
                    Utils.showAlert(`Description must be at least ${SUBMISSION_CONFIG.MIN_DESCRIPTION_LENGTH} characters.`, 'warning');
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            Utils.showAlert('Please fill in all required fields correctly.', 'warning');
        }

        return isValid;
    },

    /**
     * Save current step data
     */
    saveCurrentStepData: function() {
        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (!currentStepElement) return;

        const inputs = currentStepElement.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                this.formData[input.name] = input.checked;
            } else {
                this.formData[input.name] = input.value;
            }
        });
    },

    /**
     * Update step display
     */
    updateStepDisplay: function() {
        // Update progress steps
        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber === this.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            }
        });

        // Update form steps
        document.querySelectorAll('.form-step').forEach(step => {
            step.classList.remove('active');
        });
        
        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (currentStepElement) {
            currentStepElement.classList.add('active');
        }

        // Update navigation buttons
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const submitBtn = document.getElementById('submit-btn');

        if (prevBtn) {
            prevBtn.style.display = this.currentStep > 1 ? 'block' : 'none';
        }

        if (nextBtn) {
            nextBtn.style.display = this.currentStep < this.totalSteps ? 'block' : 'none';
        }

        if (submitBtn) {
            submitBtn.style.display = this.currentStep === this.totalSteps ? 'block' : 'none';
        }

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    },

    /**
     * Generate submission summary
     */
    generateSubmissionSummary: function() {
        const summaryContent = document.getElementById('summary-content');
        if (!summaryContent) return;

        const summary = `
            <div class="row g-3">
                <div class="col-md-6">
                    <strong>Name:</strong> ${this.formData.first_name || ''} ${this.formData.last_name || ''}
                </div>
                <div class="col-md-6">
                    <strong>Email:</strong> ${this.formData.email || ''}
                </div>
                <div class="col-12">
                    <strong>Innovation Title:</strong> ${this.formData.idea_title || ''}
                </div>
                <div class="col-md-6">
                    <strong>Industry:</strong> ${this.formData.industry_category || ''}
                </div>
                <div class="col-md-6">
                    <strong>Patent Status:</strong> ${this.formData.patent_status || ''}
                </div>
                <div class="col-12">
                    <strong>Description:</strong> ${(this.formData.idea_description || '').substring(0, 200)}${(this.formData.idea_description || '').length > 200 ? '...' : ''}
                </div>
                <div class="col-12">
                    <strong>Files Uploaded:</strong> ${this.uploadedFiles.length} file(s)
                </div>
            </div>
        `;

        summaryContent.innerHTML = summary;
    },

    /**
     * Handle form submission
     */
    handleSubmission: async function(e) {
        e.preventDefault();

        if (!this.validateCurrentStep()) {
            return;
        }

        const submitBtn = document.getElementById('submit-btn');
        const originalText = submitBtn.innerHTML;
        
        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';

            // Prepare form data
            const formData = new FormData();
            
            // Add form fields
            Object.keys(this.formData).forEach(key => {
                formData.append(key, this.formData[key]);
            });

            // Add files
            this.uploadedFiles.forEach((fileObj, index) => {
                formData.append(`files[${index}]`, fileObj.file);
            });

            // Add metadata
            formData.append('submission_type', 'idea');
            formData.append('ip_address', await this.getClientIP());
            formData.append('user_agent', navigator.userAgent);

            // Submit to server (simulate for now)
            await this.submitToServer(formData);

            // Show success
            this.showSuccessMessage();

        } catch (error) {
            console.error('Submission error:', error);
            Utils.showAlert('Submission failed. Please try again.', 'danger');
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    },

    /**
     * Submit to server
     */
    submitToServer: async function(formData) {
        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('Form submitted successfully');
                resolve({ success: true, id: 'SUB-' + Date.now() });
            }, 2000);
        });

        // Actual implementation would be:
        // const response = await fetch('/php/submit-idea.php', {
        //     method: 'POST',
        //     body: formData
        // });
        // return await response.json();
    },

    /**
     * Get client IP (for logging purposes)
     */
    getClientIP: async function() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            return 'unknown';
        }
    },

    /**
     * Show success message
     */
    showSuccessMessage: function() {
        // Hide form
        document.querySelector('.card').style.display = 'none';
        
        // Show success message
        const successMessage = document.getElementById('success-message');
        if (successMessage) {
            successMessage.style.display = 'block';
            successMessage.scrollIntoView({ behavior: 'smooth' });
        }

        // Clear form data
        this.formData = {};
        this.uploadedFiles = [];
        this.currentStep = 1;
    },

    /**
     * Setup form validation
     */
    setupFormValidation: function() {
        // Real-time validation for all inputs
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim()) {
                    this.classList.remove('is-invalid');
                }
            });
        });
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    IdeaSubmissionApp.init();
});

// Export for global access
window.IdeaSubmissionApp = IdeaSubmissionApp;
