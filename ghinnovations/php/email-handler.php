<?php
/**
 * Golden Heart Innovations - Email Handler
 * Handles email sending using PHPMailer for SMTP
 */

// Prevent direct access
if (!defined('GHI_ACCESS')) {
    die('Direct access not permitted');
}

// Include PHPMailer (you'll need to install this via Composer or download manually)
// For Hostinger, you can use their built-in mail() function or configure SMTP

/**
 * Email Handler Class
 */
class EmailHandler {
    private $mailer;
    private $usePhpMailer;
    
    public function __construct() {
        // Check if PHPMailer is available
        $this->usePhpMailer = class_exists('PHPMailer\PHPMailer\PHPMailer');
        
        if ($this->usePhpMailer) {
            $this->setupPhpMailer();
        }
    }
    
    /**
     * Setup PHPMailer with SMTP configuration
     */
    private function setupPhpMailer() {
        require_once 'vendor/autoload.php'; // If using Composer
        // Or include PHPMailer files manually:
        // require_once 'PHPMailer/src/PHPMailer.php';
        // require_once 'PHPMailer/src/SMTP.php';
        // require_once 'PHPMailer/src/Exception.php';
        
        $this->mailer = new PHPMailer\PHPMailer\PHPMailer(true);
        
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = SMTP_ENCRYPTION;
            $this->mailer->Port = SMTP_PORT;
            
            // Default sender
            $this->mailer->setFrom(SMTP_USERNAME, SITE_NAME);
            
        } catch (Exception $e) {
            logActivity("PHPMailer setup failed: " . $e->getMessage(), 'ERROR');
            $this->usePhpMailer = false;
        }
    }
    
    /**
     * Send email
     */
    public function sendEmail($to, $toName, $subject, $body, $isHtml = true) {
        if ($this->usePhpMailer) {
            return $this->sendWithPhpMailer($to, $toName, $subject, $body, $isHtml);
        } else {
            return $this->sendWithPhpMail($to, $toName, $subject, $body, $isHtml);
        }
    }
    
    /**
     * Send email using PHPMailer
     */
    private function sendWithPhpMailer($to, $toName, $subject, $body, $isHtml = true) {
        try {
            // Recipients
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to, $toName);
            
            // Content
            $this->mailer->isHTML($isHtml);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            if ($isHtml) {
                $this->mailer->AltBody = strip_tags($body);
            }
            
            $this->mailer->send();
            logActivity("Email sent successfully to: {$to}", 'INFO');
            return true;
            
        } catch (Exception $e) {
            logActivity("Email sending failed: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Send email using PHP's built-in mail() function
     */
    private function sendWithPhpMail($to, $toName, $subject, $body, $isHtml = true) {
        try {
            // Headers
            $headers = [];
            $headers[] = 'From: ' . SITE_NAME . ' <' . CONTACT_EMAIL . '>';
            $headers[] = 'Reply-To: ' . CONTACT_EMAIL;
            $headers[] = 'X-Mailer: PHP/' . phpversion();
            
            if ($isHtml) {
                $headers[] = 'MIME-Version: 1.0';
                $headers[] = 'Content-type: text/html; charset=UTF-8';
            } else {
                $headers[] = 'Content-type: text/plain; charset=UTF-8';
            }
            
            // Send email
            $success = mail($to, $subject, $body, implode("\r\n", $headers));
            
            if ($success) {
                logActivity("Email sent successfully to: {$to}", 'INFO');
                return true;
            } else {
                logActivity("Email sending failed to: {$to}", 'ERROR');
                return false;
            }
            
        } catch (Exception $e) {
            logActivity("Email sending error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Send contact form email
     */
    public function sendContactFormEmail($formData) {
        $subject = 'New Contact Form Submission - ' . $formData['subject'];
        
        $body = $this->generateContactFormEmail($formData);
        
        return $this->sendEmail(ADMIN_EMAIL, 'Admin', $subject, $body);
    }
    
    /**
     * Generate contact form email template
     */
    private function generateContactFormEmail($data) {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #1e40af;'>New Contact Form Submission</h2>
                
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='margin-top: 0; color: #1e40af;'>Contact Details:</h3>
                    <p><strong>Name:</strong> {$data['name']}</p>
                    <p><strong>Email:</strong> {$data['email']}</p>
                    <p><strong>Phone:</strong> " . ($data['phone'] ?? 'Not provided') . "</p>
                    <p><strong>Subject:</strong> {$data['subject']}</p>
                    <p><strong>Type:</strong> " . ($data['submission_type'] ?? 'General') . "</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A') . "</p>
                </div>
                
                <h3 style='color: #1e40af;'>Message:</h3>
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px;'>
                    <p>" . nl2br(htmlspecialchars($data['message'])) . "</p>
                </div>
                
                <p style='margin-top: 20px;'><strong>Action Required:</strong> Please respond to this inquiry promptly.</p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Send welcome email to new users
     */
    public function sendWelcomeEmail($userEmail, $userName, $userType = 'inventor') {
        $subject = 'Welcome to Golden Heart Innovations';
        $body = $this->generateWelcomeEmail($userName, $userType);
        
        return $this->sendEmail($userEmail, $userName, $subject, $body);
    }
    
    /**
     * Generate welcome email template
     */
    private function generateWelcomeEmail($userName, $userType) {
        $portalLink = $userType === 'business' ? 'business-portal.html' : 'patent-search.html';
        
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #1e40af;'>Welcome to Golden Heart Innovations!</h2>
                
                <p>Dear {$userName},</p>
                
                <p>Thank you for joining Golden Heart Innovations! We're excited to have you as part of our community of innovators and businesses.</p>
                
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='margin-top: 0; color: #1e40af;'>Getting Started:</h3>
                    <ul>
                        <li>Explore our patent database</li>
                        <li>Use our AI-powered search tools</li>
                        <li>Submit your innovations for evaluation</li>
                        <li>Connect with licensing opportunities</li>
                    </ul>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='" . SITE_URL . "/portals/{$portalLink}' style='background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;'>Access Your Portal</a>
                </div>
                
                <p>If you have any questions or need assistance, please don't hesitate to contact our support team at " . CONTACT_EMAIL . ".</p>
                
                <p>Best regards,<br>
                The Golden Heart Innovations Team</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e2e8f0;'>
                <p style='font-size: 12px; color: #64748b;'>
                    This email was sent from Golden Heart Innovations. If you didn't create an account, please ignore this email.
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($userEmail, $userName, $resetToken) {
        $subject = 'Password Reset Request - Golden Heart Innovations';
        $body = $this->generatePasswordResetEmail($userName, $resetToken);
        
        return $this->sendEmail($userEmail, $userName, $subject, $body);
    }
    
    /**
     * Generate password reset email template
     */
    private function generatePasswordResetEmail($userName, $resetToken) {
        $resetLink = SITE_URL . "/reset-password.html?token=" . $resetToken;
        
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #1e40af;'>Password Reset Request</h2>
                
                <p>Dear {$userName},</p>
                
                <p>We received a request to reset your password for your Golden Heart Innovations account.</p>
                
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <p><strong>If you requested this password reset:</strong></p>
                    <p>Click the button below to reset your password. This link will expire in 1 hour for security reasons.</p>
                </div>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' style='background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;'>Reset Password</a>
                </div>
                
                <div style='background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <p><strong>If you didn't request this password reset:</strong></p>
                    <p>Please ignore this email. Your password will remain unchanged.</p>
                </div>
                
                <p>For security reasons, this link will expire in 1 hour. If you need to reset your password after that, please request a new reset link.</p>
                
                <p>If you have any questions, please contact our support team at " . CONTACT_EMAIL . ".</p>
                
                <p>Best regards,<br>
                The Golden Heart Innovations Team</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e2e8f0;'>
                <p style='font-size: 12px; color: #64748b;'>
                    This email was sent from Golden Heart Innovations. Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Send notification email for new licensing inquiry
     */
    public function sendLicensingInquiryEmail($inquiryData) {
        $subject = 'New Licensing Inquiry - ' . $inquiryData['patent_title'];
        $body = $this->generateLicensingInquiryEmail($inquiryData);
        
        return $this->sendEmail(ADMIN_EMAIL, 'Admin', $subject, $body);
    }
    
    /**
     * Generate licensing inquiry email template
     */
    private function generateLicensingInquiryEmail($data) {
        return "
        <html>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #1e40af;'>New Licensing Inquiry</h2>
                
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='margin-top: 0; color: #1e40af;'>Inquiry Details:</h3>
                    <p><strong>Company:</strong> {$data['company_name']}</p>
                    <p><strong>Contact:</strong> {$data['contact_name']}</p>
                    <p><strong>Email:</strong> {$data['contact_email']}</p>
                    <p><strong>Phone:</strong> " . ($data['contact_phone'] ?? 'Not provided') . "</p>
                    <p><strong>Patent:</strong> {$data['patent_title']} ({$data['patent_number']})</p>
                    <p><strong>License Type:</strong> {$data['license_type']}</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A') . "</p>
                </div>
                
                <h3 style='color: #1e40af;'>Message:</h3>
                <div style='background: #f8fafc; padding: 15px; border-radius: 8px;'>
                    <p>" . nl2br(htmlspecialchars($data['message'])) . "</p>
                </div>
                
                <p style='margin-top: 20px;'><strong>Action Required:</strong> Please follow up with this licensing inquiry within 24 hours.</p>
            </div>
        </body>
        </html>
        ";
    }
}
?>
