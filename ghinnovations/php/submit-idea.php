<?php
/**
 * Golden Heart Innovations - Idea Submission Handler
 * Processes idea submissions, handles file uploads, and sends notifications
 */

// Define access constant
define('GHI_ACCESS', true);

// Include configuration
require_once 'config.php';
require_once 'email-handler.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable CORS for development (remove in production)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Rate limiting
    $clientIP = getClientIP();
    if (!RateLimit::checkLimit($clientIP, 5, 3600)) { // 5 submissions per hour
        http_response_code(429);
        echo json_encode(['error' => 'Too many submissions. Please try again later.']);
        exit();
    }

    // Validate CSRF token if present
    if (isset($_POST['csrf_token'])) {
        if (!Security::verifyCSRFToken($_POST['csrf_token'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Invalid security token']);
            exit();
        }
    }

    // Sanitize and validate input data
    $submissionData = validateSubmissionData($_POST);
    
    // Handle file uploads
    $uploadedFiles = handleFileUploads();
    
    // Save submission to database
    $submissionId = saveSubmission($submissionData, $uploadedFiles);
    
    // Send email notifications
    sendNotifications($submissionData, $submissionId);
    
    // Log the submission
    logActivity("New idea submission received: ID {$submissionId}", 'INFO');
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Submission received successfully',
        'submission_id' => $submissionId
    ]);

} catch (Exception $e) {
    // Log error
    logActivity("Submission error: " . $e->getMessage(), 'ERROR');
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'error' => 'Submission failed',
        'message' => 'Please try again later'
    ]);
}

/**
 * Validate submission data
 */
function validateSubmissionData($data) {
    $errors = [];
    
    // Required fields
    $requiredFields = [
        'first_name' => 'First name',
        'last_name' => 'Last name', 
        'email' => 'Email address',
        'idea_title' => 'Innovation title',
        'idea_description' => 'Innovation description',
        'industry_category' => 'Industry category',
        'patent_status' => 'Patent status'
    ];
    
    foreach ($requiredFields as $field => $label) {
        if (empty($data[$field])) {
            $errors[] = "{$label} is required";
        }
    }
    
    // Validate email
    if (!empty($data['email']) && !Security::validateEmail($data['email'])) {
        $errors[] = 'Invalid email address';
    }
    
    // Validate description length
    if (!empty($data['idea_description']) && strlen($data['idea_description']) < 100) {
        $errors[] = 'Description must be at least 100 characters';
    }
    
    // Validate patent number if status requires it
    if (in_array($data['patent_status'], ['pending', 'granted']) && empty($data['patent_number'])) {
        $errors[] = 'Patent number is required for pending/granted patents';
    }
    
    // Check for required agreements
    if (empty($data['nda_agreement']) || $data['nda_agreement'] !== 'on') {
        $errors[] = 'NDA agreement is required';
    }
    
    if (empty($data['final_agreement']) || $data['final_agreement'] !== 'on') {
        $errors[] = 'Final agreement is required';
    }
    
    if (!empty($errors)) {
        throw new Exception('Validation failed: ' . implode(', ', $errors));
    }
    
    // Sanitize data
    $sanitizedData = [];
    foreach ($data as $key => $value) {
        $sanitizedData[$key] = Security::sanitizeInput($value);
    }
    
    return $sanitizedData;
}

/**
 * Handle file uploads
 */
function handleFileUploads() {
    $uploadedFiles = [];
    
    if (empty($_FILES['files'])) {
        return $uploadedFiles;
    }
    
    $files = $_FILES['files'];
    $fileCount = count($files['name']);
    
    // Create upload directory if it doesn't exist
    $uploadDir = UPLOAD_PATH . date('Y/m/');
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    for ($i = 0; $i < $fileCount; $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $files['name'][$i],
                'tmp_name' => $files['tmp_name'][$i],
                'size' => $files['size'][$i],
                'type' => $files['type'][$i]
            ];
            
            $uploadedFile = processFileUpload($file, $uploadDir);
            if ($uploadedFile) {
                $uploadedFiles[] = $uploadedFile;
            }
        }
    }
    
    return $uploadedFiles;
}

/**
 * Process individual file upload
 */
function processFileUpload($file, $uploadDir) {
    // Validate file
    if (!validateFile($file)) {
        return null;
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return [
            'original_name' => $file['name'],
            'filename' => $filename,
            'filepath' => $filepath,
            'size' => $file['size'],
            'type' => $file['type']
        ];
    }
    
    return null;
}

/**
 * Validate uploaded file
 */
function validateFile($file) {
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // Check file type
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_FILE_TYPES)) {
        return false;
    }
    
    // Check MIME type
    $allowedMimes = [
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];
    
    if (isset($allowedMimes[$extension]) && $file['type'] !== $allowedMimes[$extension]) {
        // Additional MIME type check
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if ($mimeType !== $allowedMimes[$extension]) {
            return false;
        }
    }
    
    return true;
}

/**
 * Save submission to database
 */
function saveSubmission($data, $files) {
    $db = Database::getInstance()->getConnection();
    
    $sql = "INSERT INTO patent_submissions (
        inventor_name, inventor_email, inventor_phone, idea_title, idea_description,
        industry_category, patent_number, patent_status, market_potential,
        competitive_advantage, additional_notes, file_attachments,
        ip_address, user_agent, created_at
    ) VALUES (
        :inventor_name, :inventor_email, :inventor_phone, :idea_title, :idea_description,
        :industry_category, :patent_number, :patent_status, :market_potential,
        :competitive_advantage, :additional_notes, :file_attachments,
        :ip_address, :user_agent, NOW()
    )";
    
    $stmt = $db->prepare($sql);
    
    $inventorName = trim($data['first_name'] . ' ' . $data['last_name']);
    $fileAttachments = !empty($files) ? json_encode($files) : null;
    
    $params = [
        ':inventor_name' => $inventorName,
        ':inventor_email' => $data['email'],
        ':inventor_phone' => $data['phone'] ?? null,
        ':idea_title' => $data['idea_title'],
        ':idea_description' => $data['idea_description'],
        ':industry_category' => $data['industry_category'],
        ':patent_number' => $data['patent_number'] ?? null,
        ':patent_status' => $data['patent_status'],
        ':market_potential' => $data['market_potential'] ?? null,
        ':competitive_advantage' => $data['competitive_advantage'] ?? null,
        ':additional_notes' => $data['additional_notes'] ?? null,
        ':file_attachments' => $fileAttachments,
        ':ip_address' => getClientIP(),
        ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];
    
    if (!$stmt->execute($params)) {
        throw new Exception('Failed to save submission to database');
    }
    
    return $db->lastInsertId();
}

/**
 * Send email notifications
 */
function sendNotifications($data, $submissionId) {
    $emailHandler = new EmailHandler();
    
    // Send confirmation email to inventor
    $inventorSubject = 'Submission Confirmation - Golden Heart Innovations';
    $inventorMessage = generateInventorEmail($data, $submissionId);
    
    $emailHandler->sendEmail(
        $data['email'],
        trim($data['first_name'] . ' ' . $data['last_name']),
        $inventorSubject,
        $inventorMessage
    );
    
    // Send notification email to admin
    $adminSubject = 'New Idea Submission - ' . $data['idea_title'];
    $adminMessage = generateAdminEmail($data, $submissionId);
    
    $emailHandler->sendEmail(
        ADMIN_EMAIL,
        'Admin',
        $adminSubject,
        $adminMessage
    );
}

/**
 * Generate confirmation email for inventor
 */
function generateInventorEmail($data, $submissionId) {
    $inventorName = trim($data['first_name'] . ' ' . $data['last_name']);
    
    return "
    <html>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #1e40af;'>Thank You for Your Submission!</h2>
            
            <p>Dear {$inventorName},</p>
            
            <p>We have successfully received your innovation submission titled <strong>\"{$data['idea_title']}\"</strong>.</p>
            
            <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #1e40af;'>Submission Details:</h3>
                <p><strong>Submission ID:</strong> SUB-{$submissionId}</p>
                <p><strong>Innovation Title:</strong> {$data['idea_title']}</p>
                <p><strong>Industry:</strong> {$data['industry_category']}</p>
                <p><strong>Patent Status:</strong> {$data['patent_status']}</p>
                <p><strong>Submission Date:</strong> " . date('F j, Y') . "</p>
            </div>
            
            <h3 style='color: #1e40af;'>What Happens Next?</h3>
            <ul>
                <li>Our expert team will review your submission within 5-10 business days</li>
                <li>We'll evaluate the innovation potential and market viability</li>
                <li>If your innovation shows promise, we'll contact you to discuss next steps</li>
                <li>All communications will be kept strictly confidential</li>
            </ul>
            
            <p>Please keep your submission ID (SUB-{$submissionId}) for your records.</p>
            
            <p>If you have any questions, please don't hesitate to contact us at " . CONTACT_EMAIL . ".</p>
            
            <p>Best regards,<br>
            The Golden Heart Innovations Team</p>
            
            <hr style='margin: 30px 0; border: none; border-top: 1px solid #e2e8f0;'>
            <p style='font-size: 12px; color: #64748b;'>
                This email was sent from Golden Heart Innovations. Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    ";
}

/**
 * Generate notification email for admin
 */
function generateAdminEmail($data, $submissionId) {
    $inventorName = trim($data['first_name'] . ' ' . $data['last_name']);
    
    return "
    <html>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #1e40af;'>New Idea Submission Received</h2>
            
            <div style='background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #1e40af;'>Submission Details:</h3>
                <p><strong>Submission ID:</strong> SUB-{$submissionId}</p>
                <p><strong>Inventor:</strong> {$inventorName}</p>
                <p><strong>Email:</strong> {$data['email']}</p>
                <p><strong>Phone:</strong> " . ($data['phone'] ?? 'Not provided') . "</p>
                <p><strong>Innovation Title:</strong> {$data['idea_title']}</p>
                <p><strong>Industry:</strong> {$data['industry_category']}</p>
                <p><strong>Patent Status:</strong> {$data['patent_status']}</p>
                " . (!empty($data['patent_number']) ? "<p><strong>Patent Number:</strong> {$data['patent_number']}</p>" : "") . "
                <p><strong>Submission Date:</strong> " . date('F j, Y g:i A') . "</p>
            </div>
            
            <h3 style='color: #1e40af;'>Description:</h3>
            <p style='background: #f8fafc; padding: 15px; border-radius: 8px;'>{$data['idea_description']}</p>
            
            " . (!empty($data['market_potential']) ? "
            <h3 style='color: #1e40af;'>Market Potential:</h3>
            <p style='background: #f8fafc; padding: 15px; border-radius: 8px;'>{$data['market_potential']}</p>
            " : "") . "
            
            " . (!empty($data['competitive_advantage']) ? "
            <h3 style='color: #1e40af;'>Competitive Advantage:</h3>
            <p style='background: #f8fafc; padding: 15px; border-radius: 8px;'>{$data['competitive_advantage']}</p>
            " : "") . "
            
            <p><strong>Action Required:</strong> Please review this submission and respond within 5-10 business days.</p>
        </div>
    </body>
    </html>
    ";
}
?>
