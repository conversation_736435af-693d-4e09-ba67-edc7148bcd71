<?php
/**
 * Golden Heart Innovations - Configuration File
 * Contains database settings, API keys, and security configurations
 * 
 * IMPORTANT: Update all settings before deploying to production
 */

// Prevent direct access
if (!defined('GHI_ACCESS')) {
    die('Direct access not permitted');
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Security settings
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'golden_heart_innovations');
define('DB_USER', 'your_db_username');
define('DB_PASS', 'your_db_password');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration
define('SITE_NAME', 'Golden Heart Innovations');
define('SITE_URL', 'https://yourdomain.com');
define('ADMIN_EMAIL', '<EMAIL>');
define('CONTACT_EMAIL', '<EMAIL>');

// Security Configuration
define('ENCRYPTION_KEY', 'your-32-character-secret-key-here'); // Change this!
define('JWT_SECRET', 'your-jwt-secret-key-here'); // Change this!
define('CSRF_TOKEN_NAME', 'ghi_csrf_token');
define('SESSION_NAME', 'GHI_SESSION');

// API Keys (get these from respective services)
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_your_stripe_publishable_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('USPTO_API_KEY', 'your_uspto_api_key'); // If required
define('RECAPTCHA_SITE_KEY', 'your_recaptcha_site_key');
define('RECAPTCHA_SECRET_KEY', 'your_recaptcha_secret_key');

// File Upload Configuration
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', dirname(__DIR__) . '/uploads/');
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif']);

// Email Configuration (for PHPMailer)
define('SMTP_HOST', 'smtp.gmail.com'); // or your hosting provider's SMTP
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');
define('SMTP_ENCRYPTION', 'tls');

// Business Logic Configuration
define('DEFAULT_INVENTOR_ROYALTY_RATE', 60); // 60% to inventor
define('DEFAULT_COMPANY_ROYALTY_RATE', 40);  // 40% to company
define('SUBSCRIPTION_PLANS', [
    'basic' => [
        'name' => 'Basic',
        'price' => 29.99,
        'features' => ['Access to patent database', 'Basic search filters', 'Email support']
    ],
    'professional' => [
        'name' => 'Professional', 
        'price' => 99.99,
        'features' => ['All Basic features', 'Advanced search', 'Priority support', 'Licensing tools']
    ],
    'enterprise' => [
        'name' => 'Enterprise',
        'price' => 299.99,
        'features' => ['All Professional features', 'Custom integrations', 'Dedicated account manager']
    ]
]);

// Rate Limiting Configuration
define('RATE_LIMIT_REQUESTS', 100); // requests per hour
define('RATE_LIMIT_WINDOW', 3600); // 1 hour in seconds

// Pagination Configuration
define('PATENTS_PER_PAGE', 20);
define('SEARCH_RESULTS_PER_PAGE', 10);

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed. Please try again later.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning and unserialization
    private function __clone() {}
    public function __wakeup() {}
}

/**
 * Security Helper Functions
 */
class Security {
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION[CSRF_TOKEN_NAME] = $token;
        return $token;
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION[CSRF_TOKEN_NAME]) && 
               hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate secure random string
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
}

/**
 * Rate Limiting Class
 */
class RateLimit {
    private static $db;
    
    public static function init() {
        self::$db = Database::getInstance()->getConnection();
    }
    
    public static function checkLimit($identifier, $limit = RATE_LIMIT_REQUESTS, $window = RATE_LIMIT_WINDOW) {
        if (!self::$db) {
            self::init();
        }
        
        $now = time();
        $window_start = $now - $window;
        
        // Clean old entries
        $stmt = self::$db->prepare("DELETE FROM rate_limits WHERE created_at < ?");
        $stmt->execute([$window_start]);
        
        // Count current requests
        $stmt = self::$db->prepare("SELECT COUNT(*) FROM rate_limits WHERE identifier = ? AND created_at > ?");
        $stmt->execute([$identifier, $window_start]);
        $count = $stmt->fetchColumn();
        
        if ($count >= $limit) {
            return false;
        }
        
        // Record this request
        $stmt = self::$db->prepare("INSERT INTO rate_limits (identifier, created_at) VALUES (?, ?)");
        $stmt->execute([$identifier, $now]);
        
        return true;
    }
}

// Create rate_limits table if it doesn't exist
try {
    $db = Database::getInstance()->getConnection();
    $db->exec("CREATE TABLE IF NOT EXISTS rate_limits (
        id INT AUTO_INCREMENT PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL,
        created_at INT NOT NULL,
        INDEX idx_identifier_time (identifier, created_at)
    ) ENGINE=InnoDB");
} catch (Exception $e) {
    error_log("Failed to create rate_limits table: " . $e->getMessage());
}

/**
 * Utility Functions
 */
function getClientIP() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

function logActivity($message, $level = 'INFO') {
    $log_file = dirname(__DIR__) . '/logs/app.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = getClientIP();
    $log_entry = "[{$timestamp}] [{$level}] [{$ip}] {$message}" . PHP_EOL;
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Set timezone
date_default_timezone_set('America/New_York');

// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}
?>
