# Golden Heart Innovations - Deployment Guide

## Overview
This guide will help you deploy the Golden Heart Innovations website to Hostinger hosting.

## Prerequisites
- Hostinger hosting account with PHP and MySQL support
- FTP/SFTP access to your hosting account
- MySQL database access
- Domain name configured

## Step 1: Database Setup

### 1.1 Create MySQL Database
1. Log into your Hostinger control panel
2. Go to "Databases" → "MySQL Databases"
3. Create a new database named `golden_heart_innovations`
4. Create a database user with full privileges
5. Note down the database credentials

### 1.2 Import Database Schema
1. Access phpMyAdmin from your Hostinger control panel
2. Select your database
3. Go to "Import" tab
4. Upload and execute the `database.sql` file

## Step 2: File Upload

### 2.1 Upload Website Files
1. Connect to your hosting account via FTP/SFTP
2. Upload all files from the `ghinnovations` folder to your domain's public_html directory
3. Ensure proper file permissions:
   - Files: 644
   - Directories: 755
   - uploads directory: 755 (writable)

### 2.2 Directory Structure
```
public_html/
├── index.html
├── contact.html
├── .htaccess
├── database.sql (remove after import)
├── css/
├── js/
├── php/
├── images/
├── uploads/ (ensure writable)
├── portals/
└── admin/
```

## Step 3: Configuration

### 3.1 Update Database Configuration
Edit `php/config.php` and update:

```php
// Database Configuration
define('DB_HOST', 'localhost'); // Usually localhost for Hostinger
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_database_username');
define('DB_PASS', 'your_database_password');
```

### 3.2 Update Site Configuration
In `php/config.php`, update:

```php
// Site Configuration
define('SITE_URL', 'https://yourdomain.com');
define('ADMIN_EMAIL', '<EMAIL>');
define('CONTACT_EMAIL', '<EMAIL>');
```

### 3.3 Security Keys
Generate and update security keys in `php/config.php`:

```php
// Security Configuration
define('ENCRYPTION_KEY', 'your-32-character-secret-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');
```

Use a random string generator for these keys.

## Step 4: Email Configuration

### 4.1 SMTP Settings
Update email settings in `php/config.php`:

```php
// Email Configuration
define('SMTP_HOST', 'smtp.hostinger.com'); // Hostinger SMTP
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_email_password');
define('SMTP_ENCRYPTION', 'tls');
```

### 4.2 Create Email Accounts
1. In Hostinger control panel, go to "Email Accounts"
2. Create email accounts:
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>

## Step 5: API Keys Setup

### 5.1 USPTO API (Optional)
If you need USPTO API access:
1. Register at developer.uspto.gov
2. Get API key
3. Update `USPTO_API_KEY` in config.php

### 5.2 Stripe Integration
For payment processing:
1. Create Stripe account
2. Get API keys from Stripe dashboard
3. Update in config.php:
```php
define('STRIPE_PUBLISHABLE_KEY', 'pk_live_your_key');
define('STRIPE_SECRET_KEY', 'sk_live_your_key');
```

### 5.3 reCAPTCHA (Recommended)
1. Get reCAPTCHA keys from Google
2. Update in config.php:
```php
define('RECAPTCHA_SITE_KEY', 'your_site_key');
define('RECAPTCHA_SECRET_KEY', 'your_secret_key');
```

## Step 6: SSL Certificate

### 6.1 Enable SSL
1. In Hostinger control panel, go to "SSL"
2. Enable SSL certificate for your domain
3. Force HTTPS by uncommenting lines in .htaccess:
```apache
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## Step 7: Testing

### 7.1 Basic Functionality Test
1. Visit your website homepage
2. Test navigation between pages
3. Try the patent search (should work with sample data)
4. Submit a test idea through the submission form
5. Check contact form functionality

### 7.2 Email Testing
1. Submit a test idea submission
2. Check if confirmation emails are sent
3. Verify admin notification emails

### 7.3 Database Testing
1. Check if submissions are saved to database
2. Verify user registration works (when implemented)
3. Test search functionality

## Step 8: Security Hardening

### 8.1 File Permissions
Ensure proper permissions:
```bash
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 755 uploads/
```

### 8.2 Remove Sensitive Files
1. Delete `database.sql` from public directory
2. Remove any backup files
3. Ensure `.htaccess` is properly configured

### 8.3 Admin Access
1. Create strong admin password
2. Consider IP restrictions for admin area
3. Enable two-factor authentication if available

## Step 9: Performance Optimization

### 9.1 Enable Caching
Add to .htaccess if not already present:
```apache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
</IfModule>
```

### 9.2 Optimize Images
1. Compress images before uploading
2. Use appropriate image formats
3. Consider implementing lazy loading

## Step 10: Monitoring and Maintenance

### 10.1 Log Files
Monitor log files in `/logs/` directory:
- app.log - Application logs
- error.log - PHP errors

### 10.2 Backup Strategy
1. Set up automated database backups
2. Regular file backups
3. Test restore procedures

### 10.3 Updates
1. Regularly update PHP version
2. Monitor security patches
3. Update dependencies

## Troubleshooting

### Common Issues

**Database Connection Failed**
- Check database credentials in config.php
- Verify database server is running
- Check database user permissions

**Email Not Sending**
- Verify SMTP settings
- Check email account credentials
- Test with simple mail() function first

**File Upload Issues**
- Check uploads directory permissions (755)
- Verify PHP upload limits
- Check .htaccess file upload restrictions

**404 Errors**
- Verify .htaccess file is uploaded
- Check mod_rewrite is enabled
- Verify file paths are correct

### Support
For additional support:
- Check Hostinger documentation
- Contact Hostinger support for hosting issues
- Review application logs for errors

## Security Checklist

- [ ] Database credentials updated
- [ ] Security keys generated and updated
- [ ] SSL certificate enabled
- [ ] File permissions set correctly
- [ ] Sensitive files removed from public access
- [ ] Admin area secured
- [ ] Email configuration tested
- [ ] Backup strategy implemented
- [ ] Error reporting disabled in production
- [ ] .htaccess security rules active

## Go Live Checklist

- [ ] All configuration files updated
- [ ] Database imported and tested
- [ ] Email functionality working
- [ ] SSL certificate active
- [ ] All forms tested
- [ ] Search functionality working
- [ ] File uploads working
- [ ] Admin access secured
- [ ] Monitoring in place
- [ ] Backup system active

Your Golden Heart Innovations website should now be live and fully functional!
