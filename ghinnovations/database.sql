-- Golden Heart Innovations Database Setup
-- Compatible with MySQL 5.7+ and MariaDB 10.2+

-- Create database (uncomment if needed)
-- CREATE DATABASE golden_heart_innovations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE golden_heart_innovations;

-- Users table for authentication and user management
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    user_type ENUM('inventor', 'business', 'admin') NOT NULL DEFAULT 'inventor',
    subscription_status ENUM('active', 'inactive', 'trial', 'expired') DEFAULT 'inactive',
    subscription_plan VARCHAR(50) NULL,
    stripe_customer_id VARCHAR(100) NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255) NULL,
    reset_token VARCHAR(255) NULL,
    reset_token_expires DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_subscription_status (subscription_status),
    INDEX idx_verification_token (verification_token),
    INDEX idx_reset_token (reset_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Patent submissions from inventors
CREATE TABLE patent_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    inventor_name VARCHAR(200) NOT NULL,
    inventor_email VARCHAR(255) NOT NULL,
    inventor_phone VARCHAR(50) NULL,
    idea_title VARCHAR(500) NOT NULL,
    idea_description TEXT NOT NULL,
    industry_category VARCHAR(100) NOT NULL,
    patent_number VARCHAR(50) NULL,
    patent_status ENUM('idea', 'provisional', 'pending', 'granted') DEFAULT 'idea',
    submission_status ENUM('submitted', 'under_review', 'approved', 'rejected', 'acquired') DEFAULT 'submitted',
    review_notes TEXT NULL,
    estimated_value DECIMAL(15,2) NULL,
    acquisition_offer DECIMAL(15,2) NULL,
    file_attachments JSON NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by INT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_inventor_email (inventor_email),
    INDEX idx_submission_status (submission_status),
    INDEX idx_patent_status (patent_status),
    INDEX idx_industry_category (industry_category),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Patents in our portfolio
CREATE TABLE patents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patent_number VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(500) NOT NULL,
    abstract TEXT NOT NULL,
    description LONGTEXT NULL,
    inventors TEXT NOT NULL,
    assignee VARCHAR(300) NULL,
    filing_date DATE NOT NULL,
    grant_date DATE NULL,
    expiration_date DATE NULL,
    patent_type ENUM('utility', 'design', 'plant', 'provisional') NOT NULL,
    status ENUM('pending', 'granted', 'expired', 'abandoned') NOT NULL,
    industry_categories JSON NOT NULL,
    technology_keywords JSON NULL,
    licensing_status ENUM('available', 'licensed', 'exclusive', 'expired') DEFAULT 'available',
    acquisition_cost DECIMAL(15,2) NULL,
    acquisition_date DATE NULL,
    original_inventor_id INT NULL,
    royalty_rate DECIMAL(5,2) NULL COMMENT 'Percentage rate for royalties',
    minimum_license_fee DECIMAL(15,2) NULL,
    exclusive_license_fee DECIMAL(15,2) NULL,
    geographic_scope JSON NULL COMMENT 'Countries where patent is valid',
    file_attachments JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (original_inventor_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_patent_number (patent_number),
    INDEX idx_status (status),
    INDEX idx_licensing_status (licensing_status),
    INDEX idx_filing_date (filing_date),
    INDEX idx_grant_date (grant_date),
    INDEX idx_expiration_date (expiration_date),
    FULLTEXT idx_title_abstract (title, abstract)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Patent licenses to businesses
CREATE TABLE patent_licenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patent_id INT NOT NULL,
    licensee_user_id INT NOT NULL,
    license_type ENUM('non_exclusive', 'exclusive', 'sole') NOT NULL,
    license_fee DECIMAL(15,2) NOT NULL,
    royalty_rate DECIMAL(5,2) NOT NULL,
    minimum_royalty DECIMAL(15,2) NULL,
    geographic_scope JSON NOT NULL,
    field_of_use TEXT NULL,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    status ENUM('active', 'expired', 'terminated', 'suspended') DEFAULT 'active',
    contract_file VARCHAR(500) NULL,
    payment_terms TEXT NULL,
    reporting_requirements TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (patent_id) REFERENCES patents(id) ON DELETE CASCADE,
    FOREIGN KEY (licensee_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_patent_id (patent_id),
    INDEX idx_licensee_user_id (licensee_user_id),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Royalty payments tracking
CREATE TABLE royalty_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_id INT NOT NULL,
    patent_id INT NOT NULL,
    inventor_user_id INT NULL,
    licensee_user_id INT NOT NULL,
    payment_period_start DATE NOT NULL,
    payment_period_end DATE NOT NULL,
    gross_revenue DECIMAL(15,2) NOT NULL,
    royalty_rate DECIMAL(5,2) NOT NULL,
    royalty_amount DECIMAL(15,2) NOT NULL,
    inventor_share DECIMAL(15,2) NOT NULL,
    company_share DECIMAL(15,2) NOT NULL,
    payment_status ENUM('pending', 'paid', 'overdue', 'disputed') DEFAULT 'pending',
    payment_date DATE NULL,
    payment_method VARCHAR(50) NULL,
    transaction_id VARCHAR(100) NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (license_id) REFERENCES patent_licenses(id) ON DELETE CASCADE,
    FOREIGN KEY (patent_id) REFERENCES patents(id) ON DELETE CASCADE,
    FOREIGN KEY (inventor_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (licensee_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_license_id (license_id),
    INDEX idx_patent_id (patent_id),
    INDEX idx_inventor_user_id (inventor_user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_payment_period (payment_period_start, payment_period_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Patent search history
CREATE TABLE patent_searches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    search_query VARCHAR(1000) NOT NULL,
    search_filters JSON NULL,
    results_count INT NOT NULL DEFAULT 0,
    search_source ENUM('uspto', 'internal', 'combined') NOT NULL DEFAULT 'uspto',
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_search_source (search_source),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contact form submissions
CREATE TABLE contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NULL,
    subject VARCHAR(300) NOT NULL,
    message TEXT NOT NULL,
    submission_type ENUM('general', 'licensing', 'support', 'partnership') DEFAULT 'general',
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    assigned_to INT NULL,
    response_notes TEXT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_submission_type (submission_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings and configuration
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'Golden Heart Innovations', 'string', 'Website name', TRUE),
('contact_email', '<EMAIL>', 'string', 'Main contact email', TRUE),
('contact_phone', '+****************', 'string', 'Main contact phone', TRUE),
('youtube_video_id', '', 'string', 'Homepage YouTube video ID', FALSE),
('stripe_publishable_key', '', 'string', 'Stripe publishable key', FALSE),
('stripe_secret_key', '', 'string', 'Stripe secret key', FALSE),
('uspto_api_key', '', 'string', 'USPTO API key', FALSE),
('max_file_upload_size', '10485760', 'number', 'Maximum file upload size in bytes (10MB)', FALSE),
('allowed_file_types', '["pdf","doc","docx","jpg","jpeg","png","gif"]', 'json', 'Allowed file upload types', FALSE),
('royalty_split_inventor', '60', 'number', 'Default inventor royalty percentage', FALSE),
('royalty_split_company', '40', 'number', 'Default company royalty percentage', FALSE);

-- Create admin user (password: admin123 - CHANGE THIS!)
INSERT INTO users (email, password_hash, first_name, last_name, user_type, email_verified, is_active) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', TRUE, TRUE);

-- Create indexes for better performance
CREATE INDEX idx_users_email_verified ON users(email_verified);
CREATE INDEX idx_patents_technology_keywords ON patents((CAST(technology_keywords AS CHAR(255))));
CREATE INDEX idx_patent_submissions_created_date ON patent_submissions(DATE(created_at));
