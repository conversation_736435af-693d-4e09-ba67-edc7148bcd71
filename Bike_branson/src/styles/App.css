/**
 * Main CSS file for Bike Branson Rental System
 * Custom styles and Bootstrap overrides
 */

/* CSS Variables - Bike Branson Orange Theme */
:root {
    --primary-color: #f15808;
    --primary-light: #ff7043;
    --primary-dark: #d84315;
    --secondary-color: #ff9800;
    --secondary-light: #ffb74d;
    --secondary-dark: #f57c00;
    --accent-color: #ff5722;
    --text-dark: #1c0d0a;
    --text-light: #6C757D;
    --link-color: #1c0d0a;
    --link-hover-color: #f15808;
    --background-light: #F8F9FA;
    --white: #FFFFFF;
    --border-color: #e2e2e2;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --font-family-primary: 'Inter', sans-serif;
    --font-family-heading: 'Poppins', sans-serif;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

/* Link Styles */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover, a:focus {
    color: var(--link-hover-color);
    text-decoration: none;
}

/* App Layout */
.app-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
}

/* Navigation Styles */
.navbar {
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.navbar-brand {
    font-size: 1.5rem;
    text-decoration: none !important;
    color: var(--text-dark) !important;
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
}

.navbar-brand:hover {
    color: var(--primary-color) !important;
}

.navbar-brand img {
    transition: var(--transition);
}

.nav-link {
    color: var(--text-dark) !important;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

/* Button Overrides */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition);
    color: white;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    font-weight: 500;
    color: white;
}

.btn-secondary:hover,
.btn-secondary:focus {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-warning {
    background-color: #ff9800;
    border-color: #ff9800;
    color: white;
    font-weight: 500;
}

.btn-warning:hover,
.btn-warning:focus {
    background-color: #f57c00;
    border-color: #f57c00;
    color: white;
}

/* Hero Section with Video Background */
.hero-section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.video-background {
    overflow: hidden;
    clip-path: inset(0);
}

.video-background iframe {
    border: none;
    outline: none;
    filter: brightness(1.1) contrast(1.1);
}

.hero-background-overlay {
    transition: opacity 2s ease-in-out;
}

.hero-background-overlay.fade-out {
    opacity: 0;
}

.video-background {
    transition: opacity 2s ease-in-out;
}

.video-background.fade-in {
    opacity: 1 !important;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-content h1 {
    font-size: 4rem;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.hero-content p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.hero-buttons .btn {
    margin: 0.25rem;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    text-shadow: none;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Video Background Responsive */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }

    /* Responsive navbar logo */
    .navbar-brand img {
        height: 60px !important;
        max-height: 60px !important;
    }

    .navbar {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.trail-card .card-img-top,
.bike-card .card-img-top {
    transition: var(--transition);
}

.trail-card:hover .card-img-top,
.bike-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Feature Icons */
.feature-icon {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Weather Widget */
.weather-widget {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow);
    text-align: center;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner.large {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.loading-container.fullscreen {
    min-height: 100vh;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Badge Styles */
.badge.bg-success {
    background-color: var(--primary-color) !important;
}

/* Footer Styles */
.footer {
    background-color: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
    margin-top: auto;
}

.footer h5 {
    color: var(--primary-light);
    margin-bottom: 1rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: var(--transition);
}

.footer a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid #495057;
    padding-top: 1rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.text-orange {
    color: var(--primary-color) !important;
}

.bg-orange {
    background-color: var(--primary-color) !important;
}

.border-orange {
    border-color: var(--border-color) !important;
}

.shadow-sm {
    box-shadow: var(--shadow) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.rounded-lg {
    border-radius: var(--border-radius-lg) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Hero Content Animation */
.hero-content {
    animation: heroContentFadeIn 1s ease-out 0.5s both;
}

@keyframes heroContentFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Smooth backdrop blur effect */
.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Video loading placeholder */
.video-placeholder {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}
