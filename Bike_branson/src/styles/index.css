/**
 * Base CSS for Bike Branson Rental System
 * Global styles and resets
 */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    color: #212529;
    background-color: #ffffff;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
        monospace;
}

/* Remove default margins and paddings */
h1, h2, h3, h4, h5, h6,
p, ul, ol, li,
blockquote, figure {
    margin: 0;
    padding: 0;
}

/* List styles */
ul, ol {
    list-style: none;
}

/* Link styles */
a {
    color: inherit;
    text-decoration: none;
}

/* Image styles */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Button reset */
button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
}

/* Form element styles */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
}

/* Focus styles for accessibility */
:focus {
    outline: 2px solid #2E7D32;
    outline-offset: 2px;
}

/* Skip to main content link for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #2E7D32;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Print styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .no-print {
        display: none !important;
    }
}
