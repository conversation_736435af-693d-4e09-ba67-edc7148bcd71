/**
 * ContactPage Component
 * Contact information and contact form
 */

import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';

const ContactPage = () => {
    return (
        <div className="contact-page py-5">
            <Container>
                <Row>
                    <Col lg={8} className="mx-auto text-center mb-5">
                        <h1 className="display-4 fw-bold text-primary mb-3">Contact Us</h1>
                        <p className="lead text-muted">
                            Get in touch with us for questions, bookings, or support!
                        </p>
                    </Col>
                </Row>
                <Row>
                    <Col lg={6} className="mb-4">
                        <Card className="h-100">
                            <Card.Body className="p-4">
                                <h4 className="text-primary mb-3">Get In Touch</h4>
                                <div className="contact-info">
                                    <div className="mb-3">
                                        <i className="bi bi-geo-alt text-primary me-3"></i>
                                        <strong>Address:</strong><br/>
                                        5377 Historic Hwy 165, Branson, MO 65616
                                    </div>
                                    <div className="mb-3">
                                        <i className="bi bi-telephone text-primary me-3"></i>
                                        <strong>Phone:</strong><br/>
                                        <a href="tel:+14174209373">(417)420-9373</a>
                                    </div>
                                    <div className="mb-3">
                                        <i className="bi bi-envelope text-primary me-3"></i>
                                        <strong>Email:</strong><br/>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                    <div className="mb-3">
                                        <i className="bi bi-clock text-primary me-3"></i>
                                        <strong>Hours:</strong><br/>
                                        Mon-Thu: 9:30 AM - 5:00 PM<br/>
                                        Fri-Sat: 9:00 AM - 6:00 PM<br/>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                    <Col lg={6}>
                        <Card>
                            <Card.Body className="p-4 text-center">
                                <i className="bi bi-envelope display-4 text-primary mb-3"></i>
                                <h4 className="text-primary mb-3">Contact Form</h4>
                                <p className="text-muted">
                                    A comprehensive contact form will be implemented here with:
                                </p>
                                <ul className="list-unstyled text-start">
                                    <li className="mb-2">✓ Name and contact information</li>
                                    <li className="mb-2">✓ Subject and message fields</li>
                                    <li className="mb-2">✓ Inquiry type selection</li>
                                    <li className="mb-2">✓ Email validation</li>
                                    <li className="mb-2">✓ Automated responses</li>
                                    <li className="mb-2">✓ Admin notification system</li>
                                </ul>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default ContactPage;
