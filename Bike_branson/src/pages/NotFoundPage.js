/**
 * NotFoundPage Component
 * 404 error page
 */

import React from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const NotFoundPage = () => {
    return (
        <div className="not-found-page py-5">
            <Container>
                <Row>
                    <Col lg={6} className="mx-auto text-center">
                        <div className="py-5">
                            <i className="bi bi-exclamation-triangle display-1 text-warning mb-4"></i>
                            <h1 className="display-4 fw-bold text-primary mb-3">404</h1>
                            <h2 className="h3 mb-3">Page Not Found</h2>
                            <p className="lead text-muted mb-4">
                                Oops! The page you're looking for seems to have taken a detour. 
                                Maybe it's out exploring the trails?
                            </p>
                            <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                                <Button as={Link} to="/" variant="primary" size="lg">
                                    <i className="bi bi-house me-2"></i>
                                    Go Home
                                </Button>
                                <Button as={Link} to="/trails" variant="outline-primary" size="lg">
                                    <i className="bi bi-map me-2"></i>
                                    Explore Trails
                                </Button>
                                <Button as={Link} to="/book" variant="outline-secondary" size="lg">
                                    <i className="bi bi-calendar-check me-2"></i>
                                    Book a Ride
                                </Button>
                            </div>
                        </div>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default NotFoundPage;
