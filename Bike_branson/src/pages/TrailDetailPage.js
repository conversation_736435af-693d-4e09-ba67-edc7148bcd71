/**
 * TrailDetailPage Component
 * Detailed view of a specific trail
 */

import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge, Carousel } from 'react-bootstrap';
import { useParams, Link } from 'react-router-dom';
import { apiService } from '../services/apiService';
import LoadingSpinner from '../components/LoadingSpinner';

const TrailDetailPage = () => {
    const { slug } = useParams();
    const [trail, setTrail] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchTrail();
    }, [slug]);

    const fetchTrail = async () => {
        try {
            const response = await apiService.getTrail(slug);
            setTrail(response.data);
        } catch (error) {
            console.error('Error fetching trail:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <LoadingSpinner fullScreen message="Loading trail details..." />;
    }

    if (!trail) {
        return (
            <Container className="py-5">
                <div className="text-center">
                    <h2>Trail Not Found</h2>
                    <p>The trail you're looking for doesn't exist.</p>
                    <Button as={Link} to="/trails" variant="primary">
                        View All Trails
                    </Button>
                </div>
            </Container>
        );
    }

    return (
        <div className="trail-detail-page py-5">
            <Container>
                <Row>
                    <Col lg={8}>
                        <h1 className="display-5 fw-bold text-primary mb-3">{trail.name}</h1>
                        <p className="lead text-muted mb-4">{trail.description}</p>
                        
                        {/* Trail Images */}
                        {trail.image_gallery && trail.image_gallery.length > 0 && (
                            <Carousel className="mb-4">
                                {trail.image_gallery.map((image, index) => (
                                    <Carousel.Item key={index}>
                                        <img
                                            className="d-block w-100"
                                            src={image}
                                            alt={`${trail.name} - Image ${index + 1}`}
                                            style={{ height: '400px', objectFit: 'cover' }}
                                        />
                                    </Carousel.Item>
                                ))}
                            </Carousel>
                        )}

                        {/* Trail Features */}
                        {trail.features && (
                            <div className="mb-4">
                                <h4>Trail Features</h4>
                                <div className="d-flex flex-wrap gap-2">
                                    {trail.features.map((feature, index) => (
                                        <Badge key={index} bg="secondary" className="p-2">
                                            {feature.replace('_', ' ')}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Safety Notes */}
                        {trail.safety_notes && (
                            <div className="mb-4">
                                <h4>Safety Information</h4>
                                <div className="alert alert-warning">
                                    <i className="bi bi-exclamation-triangle me-2"></i>
                                    {trail.safety_notes}
                                </div>
                            </div>
                        )}
                    </Col>

                    <Col lg={4}>
                        <Card className="sticky-top" style={{ top: '100px' }}>
                            <Card.Body>
                                <h5 className="text-primary mb-3">Trail Information</h5>
                                
                                <div className="trail-stats mb-4">
                                    <div className="d-flex justify-content-between mb-2">
                                        <span>Difficulty:</span>
                                        <Badge bg={
                                            trail.difficulty_level === 'easy' ? 'success' :
                                            trail.difficulty_level === 'moderate' ? 'warning' : 'danger'
                                        }>
                                            {trail.difficulty_level}
                                        </Badge>
                                    </div>
                                    <div className="d-flex justify-content-between mb-2">
                                        <span>Distance:</span>
                                        <span>{trail.distance_miles} miles</span>
                                    </div>
                                    <div className="d-flex justify-content-between mb-2">
                                        <span>Duration:</span>
                                        <span>{trail.estimated_duration_hours} hours</span>
                                    </div>
                                    <div className="d-flex justify-content-between mb-2">
                                        <span>Trail Type:</span>
                                        <span className="text-capitalize">{trail.trail_type}</span>
                                    </div>
                                    {trail.elevation_gain_feet && (
                                        <div className="d-flex justify-content-between mb-2">
                                            <span>Elevation Gain:</span>
                                            <span>{trail.elevation_gain_feet} ft</span>
                                        </div>
                                    )}
                                </div>

                                {trail.is_delivery_available && (
                                    <div className="alert alert-info mb-3">
                                        <i className="bi bi-truck me-2"></i>
                                        <strong>Delivery Available</strong><br/>
                                        We can deliver bikes to this trail for ${trail.delivery_fee}
                                    </div>
                                )}

                                <Button 
                                    as={Link} 
                                    to={`/book?trail=${trail.slug}`}
                                    variant="primary" 
                                    size="lg" 
                                    className="w-100 mb-2"
                                >
                                    <i className="bi bi-calendar-check me-2"></i>
                                    Book This Trail
                                </Button>

                                <Button 
                                    as={Link} 
                                    to="/trails"
                                    variant="outline-secondary" 
                                    className="w-100"
                                >
                                    <i className="bi bi-arrow-left me-2"></i>
                                    Back to Trails
                                </Button>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default TrailDetailPage;
