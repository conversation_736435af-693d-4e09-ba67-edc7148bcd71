/**
 * CustomerPortal Component
 * Customer account dashboard and booking management
 */

import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';

const CustomerPortal = () => {
    return (
        <div className="customer-portal py-5">
            <Container>
                <Row>
                    <Col lg={8} className="mx-auto">
                        <Card>
                            <Card.Body className="p-5 text-center">
                                <i className="bi bi-person-circle display-1 text-primary mb-4"></i>
                                <h2 className="text-primary mb-3">Customer Portal</h2>
                                <p className="lead text-muted mb-4">
                                    The customer portal will provide comprehensive account management:
                                </p>
                                <ul className="list-unstyled text-start">
                                    <li className="mb-2">✓ View and manage bookings</li>
                                    <li className="mb-2">✓ Booking history and receipts</li>
                                    <li className="mb-2">✓ Profile and contact information</li>
                                    <li className="mb-2">✓ Cancel or modify reservations</li>
                                    <li className="mb-2">✓ Download digital waivers</li>
                                    <li className="mb-2">✓ Loyalty points and discounts</li>
                                    <li className="mb-2">✓ Favorite trails and preferences</li>
                                </ul>
                                <p className="text-muted mt-4">
                                    This will be a full dashboard with tabs for different sections,
                                    integrated with the booking and payment systems.
                                </p>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default CustomerPortal;
