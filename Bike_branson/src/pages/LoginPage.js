/**
 * LoginPage Component
 * User authentication login form
 */

import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [showPassword, setShowPassword] = useState(false);
    const { login, loading, error } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    // Get the intended destination or default to home
    const from = location.state?.from?.pathname || '/';

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await login(formData);
            navigate(from, { replace: true });
        } catch (error) {
            // Error is handled by the auth context
        }
    };

    return (
        <div className="login-page min-vh-100 d-flex align-items-center bg-light">
            <Container>
                <Row className="justify-content-center">
                    <Col md={6} lg={5} xl={4}>
                        <Card className="shadow-sm">
                            <Card.Body className="p-4">
                                {/* Header */}
                                <div className="text-center mb-4">
                                    <img
                                        src="/images/logos/Bike Branson Logo Dark small.png"
                                        alt="Bike Branson"
                                        height="60"
                                        className="mb-3"
                                    />
                                    <h2 className="text-primary fw-bold">Welcome Back</h2>
                                    <p className="text-muted">Sign in to your account</p>
                                </div>

                                {/* Error Alert */}
                                {error && (
                                    <Alert variant="danger" className="mb-3">
                                        {error}
                                    </Alert>
                                )}

                                {/* Login Form */}
                                <Form onSubmit={handleSubmit}>
                                    <Form.Group className="mb-3">
                                        <Form.Label>Email Address</Form.Label>
                                        <Form.Control
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleChange}
                                            placeholder="Enter your email"
                                            required
                                            autoComplete="email"
                                        />
                                    </Form.Group>

                                    <Form.Group className="mb-3">
                                        <Form.Label>Password</Form.Label>
                                        <div className="position-relative">
                                            <Form.Control
                                                type={showPassword ? 'text' : 'password'}
                                                name="password"
                                                value={formData.password}
                                                onChange={handleChange}
                                                placeholder="Enter your password"
                                                required
                                                autoComplete="current-password"
                                            />
                                            <Button
                                                variant="link"
                                                className="position-absolute end-0 top-0 border-0 text-muted"
                                                style={{ zIndex: 10 }}
                                                onClick={() => setShowPassword(!showPassword)}
                                                type="button"
                                            >
                                                <i className={`bi ${showPassword ? 'bi-eye-slash' : 'bi-eye'}`}></i>
                                            </Button>
                                        </div>
                                    </Form.Group>

                                    <div className="d-flex justify-content-between align-items-center mb-3">
                                        <Form.Check
                                            type="checkbox"
                                            id="remember-me"
                                            label="Remember me"
                                        />
                                        <Link to="/forgot-password" className="text-decoration-none">
                                            Forgot password?
                                        </Link>
                                    </div>

                                    <Button
                                        type="submit"
                                        variant="primary"
                                        size="lg"
                                        className="w-100 mb-3"
                                        disabled={loading}
                                    >
                                        {loading ? (
                                            <>
                                                <span className="loading-spinner me-2"></span>
                                                Signing In...
                                            </>
                                        ) : (
                                            'Sign In'
                                        )}
                                    </Button>
                                </Form>

                                {/* Divider */}
                                <div className="text-center mb-3">
                                    <small className="text-muted">Don't have an account?</small>
                                </div>

                                {/* Register Link */}
                                <Button
                                    as={Link}
                                    to="/register"
                                    variant="outline-primary"
                                    size="lg"
                                    className="w-100"
                                >
                                    Create Account
                                </Button>

                                {/* Quick Booking Link */}
                                <div className="text-center mt-4">
                                    <small className="text-muted">
                                        Want to book without an account?{' '}
                                        <Link to="/book" className="text-decoration-none">
                                            Quick Book
                                        </Link>
                                    </small>
                                </div>
                            </Card.Body>
                        </Card>

                        {/* Demo Credentials (remove in production) */}
                        {process.env.NODE_ENV === 'development' && (
                            <Card className="mt-3 border-warning">
                                <Card.Body className="p-3">
                                    <h6 className="text-warning mb-2">
                                        <i className="bi bi-info-circle me-1"></i>
                                        Demo Credentials
                                    </h6>
                                    <small className="text-muted">
                                        <strong>Admin:</strong> <EMAIL> / admin123<br/>
                                        <strong>Customer:</strong> <EMAIL> / password123
                                    </small>
                                </Card.Body>
                            </Card>
                        )}
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default LoginPage;
