/**
 * TrailsPage Component
 * Displays all available trails with filtering and search
 */

import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { apiService } from '../services/apiService';
import LoadingSpinner from '../components/LoadingSpinner';

const TrailsPage = () => {
    const [trails, setTrails] = useState([]);
    const [filteredTrails, setFilteredTrails] = useState([]);
    const [loading, setLoading] = useState(true);
    const [difficultyFilter, setDifficultyFilter] = useState('all');

    useEffect(() => {
        fetchTrails();
    }, []);

    useEffect(() => {
        filterTrails();
    }, [trails, difficultyFilter]);

    const fetchTrails = async () => {
        try {
            // Use mock data with actual trail images
            const mockTrails = [
                {
                    id: 1,
                    name: "Table Rock Lake Trail",
                    slug: "table-rock-lake",
                    description: "A scenic paved trail that follows the beautiful shoreline of Table Rock Lake. Directly accross from our shop! Our most Popular 1.5-2hr trail ride for everyone!",
                    difficulty_level: "easy",
                    distance_miles: 8,
                    estimated_duration_hours: 2,
                    trail_type: "paved",
                    image_gallery: ["/images/trails/table_rock-Lake.jpg"],
                },
                {
                    id: 2,
                    name: "Dogwood Canyon Nature Park",
                    slug: "dogwood-canyon",
                    description: "Experience the natural beauty of Dogwood Canyon with its pristine streams, waterfalls, and diverse wildlife on this spectacular trail through protected wilderness.",
                    difficulty_level: "moderate",
                    distance_miles: 13,
                    estimated_duration_hours: 4.0,
                    trail_type: "mixed",
                    image_gallery: ["/images/trails/Dogwood_Canyon.png"],
                    is_delivery_available: true,
                    delivery_fee: 60
                },
                {
                    id: 3,
                    name: "Roark Creek Trail",
                    slug: "roark-creek",
                    description: "A peaceful trail winding through the Roark Creek area with gentle hills, beautiful forest scenery, and crystal-clear creek views perfect for nature lovers.",
                    difficulty_level: "easy",
                    distance_miles: 2.7,
                    estimated_duration_hours: 1,
                    trail_type: "gravel",
                    image_gallery: ["/images/trails/Roark Creek Trail.png"],
                    is_delivery_available: false,
                    delivery_fee: 18
                },
            ];

            setTrails(mockTrails);
        } catch (error) {
            console.error('Error fetching trails:', error);
        } finally {
            setLoading(false);
        }
    };

    const filterTrails = () => {
        let filtered = trails;

        // Filter by difficulty
        if (difficultyFilter !== 'all') {
            filtered = filtered.filter(trail => trail.difficulty_level === difficultyFilter);
        }

        setFilteredTrails(filtered);
    };

    const getDifficultyBadgeColor = (difficulty) => {
        switch (difficulty) {
            case 'easy': return 'success';
            case 'moderate': return 'warning';
            case 'difficult': return 'danger';
            default: return 'secondary';
        }
    };

    if (loading) {
        return <LoadingSpinner fullScreen message="Loading trails..." />;
    }

    return (
        <div className="trails-page py-3">
            <Container>
                {/* Page Header */}
                <Row>
                    <Col lg={8} className="mx-auto text-center mb-5">
                        <h1 className="display-4 fw-bold text-primary mb-3">
                            Explore Branson Trails
                        </h1>
                        <p className="lead text-muted">
                            Discover the most beautiful e-bike trails in Branson, Missouri. 
                            From scenic lake views to challenging mountain paths, find your perfect adventure.
                        </p>
                    </Col>
                </Row>

                {/* Difficulty Filter */}
                <Row className="mb-4 justify-content-center">
                    <Col md={4} lg={3}>
                        <Form.Select
                            value={difficultyFilter}
                            onChange={(e) => setDifficultyFilter(e.target.value)}
                            className="form-select-lg"
                        >
                            <option value="all">All Difficulties</option>
                            <option value="easy">Easy</option>
                            <option value="moderate">Moderate</option>
                            <option value="difficult">Difficult</option>
                        </Form.Select>
                    </Col>
                    <Col md={4} lg={3} className="text-center">
                        <div className="text-muted mt-2">
                            {filteredTrails.length} trail{filteredTrails.length !== 1 ? 's' : ''} available
                        </div>
                    </Col>
                </Row>

                {/* Trails Grid */}
                <Row>
                    {filteredTrails.length === 0 ? (
                        <Col className="text-center py-5">
                            <i className="bi bi-search display-1 text-muted"></i>
                            <h3 className="mt-3 text-muted">No trails found</h3>
                            <p className="text-muted">Try adjusting your filter criteria.</p>
                        </Col>
                    ) : (
                        filteredTrails.map((trail) => (
                            <Col lg={4} md={6} key={trail.id} className="mb-4">
                                <Card className="trail-card h-100 shadow-sm">
                                    <Card.Img 
                                        variant="top" 
                                        src={trail.image_gallery?.[0] || '/images/trail-placeholder.jpg'}
                                        alt={trail.name}
                                        style={{ height: '250px', objectFit: 'cover' }}
                                    />
                                    <Card.Body className="d-flex flex-column">
                                        <div className="d-flex justify-content-between align-items-start mb-2">
                                            <Card.Title className="text-primary h5">
                                                {trail.name}
                                            </Card.Title>
                                            <Badge bg={getDifficultyBadgeColor(trail.difficulty_level)}>
                                                {trail.difficulty_level}
                                            </Badge>
                                        </div>
                                        
                                        <Card.Text className="text-muted flex-grow-1">
                                            {trail.description.substring(0, 150)}...
                                        </Card.Text>
                                        
                                        <div className="trail-stats mb-3">
                                            <Row className="text-center">
                                                <Col xs={4}>
                                                    <div className="stat">
                                                        <i className="bi bi-geo-alt text-primary"></i>
                                                        <div className="mt-1">
                                                            <small className="text-muted d-block">Distance</small>
                                                            <span className="fw-semibold">{trail.distance_miles} mi</span>
                                                        </div>
                                                    </div>
                                                </Col>
                                                <Col xs={4}>
                                                    <div className="stat">
                                                        <i className="bi bi-clock text-primary"></i>
                                                        <div className="mt-1">
                                                            <small className="text-muted d-block">Duration</small>
                                                            <span className="fw-semibold">{trail.estimated_duration_hours}h</span>
                                                        </div>
                                                    </div>
                                                </Col>
                                                <Col xs={4}>
                                                    <div className="stat">
                                                        <i className="bi bi-mountain text-primary"></i>
                                                        <div className="mt-1">
                                                            <small className="text-muted d-block">Type</small>
                                                            <span className="fw-semibold">{trail.trail_type}</span>
                                                        </div>
                                                    </div>
                                                </Col>
                                            </Row>
                                        </div>
                                        
                                        {trail.is_delivery_available && (
                                            <div className="mb-2">
                                                <small className="text-success">
                                                    <i className="bi bi-truck me-1"></i>
                                                    Delivery available (${trail.delivery_fee})
                                                </small>
                                            </div>
                                        )}
                                        
                                        <div className="d-flex gap-2">
                                            <Button 
                                                as={Link} 
                                                to={`/trails/${trail.slug}`} 
                                                variant="outline-primary"
                                                size="sm"
                                                className="flex-grow-1"
                                            >
                                                Learn More
                                            </Button>
                                            <Button 
                                                as={Link} 
                                                to={`/book?trail=${trail.slug}`} 
                                                variant="primary"
                                                size="sm"
                                                className="flex-grow-1"
                                            >
                                                Book Now
                                            </Button>
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Col>
                        ))
                    )}
                </Row>

                {/* Call to Action */}
                <Row className="mt-5">
                    <Col lg={8} className="mx-auto text-center">
                        <div className="bg-light rounded p-5">
                            <h3 className="text-primary mb-3">Ready to Start Your Adventure?</h3>
                            <p className="text-muted mb-4">
                                Book your e-bike rental today and explore these amazing trails with ease and comfort.
                            </p>
                            <Button as={Link} to="/book" variant="primary" size="lg">
                                <i className="bi bi-calendar-check me-2"></i>
                                Book Your Adventure
                            </Button>
                        </div>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default TrailsPage;
