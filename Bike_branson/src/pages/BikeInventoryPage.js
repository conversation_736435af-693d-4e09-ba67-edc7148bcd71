/**
 * BikeInventoryPage Component
 * Displays available bikes with specifications
 */

import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';

const BikeInventoryPage = () => {
    return (
        <div className="bike-inventory-page py-5">
            <Container>
                <Row>
                    <Col lg={8} className="mx-auto">
                        <Card>
                            <Card.Body className="p-5 text-center">
                                <i className="bi bi-bicycle display-1 text-primary mb-4"></i>
                                <h2 className="text-primary mb-3">Our E-Bike Fleet</h2>
                                <p className="lead text-muted mb-4">
                                    The bike inventory page will showcase our premium e-bike collection:
                                </p>
                                <ul className="list-unstyled text-start">
                                    <li className="mb-2">✓ Real-time availability status</li>
                                    <li className="mb-2">✓ Detailed bike specifications</li>
                                    <li className="mb-2">✓ High-quality bike photos</li>
                                    <li className="mb-2">✓ Pricing information</li>
                                    <li className="mb-2">✓ Filter by bike type and size</li>
                                    <li className="mb-2">✓ Direct booking integration</li>
                                    <li className="mb-2">✓ Maintenance status indicators</li>
                                </ul>
                                <p className="text-muted mt-4">
                                    This will display bikes from the database with filtering, 
                                    search, and real-time availability checking.
                                </p>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </div>
    );
};

export default BikeInventoryPage;
