/**
 * HomePage Component for Bike Branson Rental System
 * Landing page with hero section, featured trails, and booking CTAs
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Carousel } from 'react-bootstrap';
import { apiService } from '../services/apiService';
import LoadingSpinner from '../components/LoadingSpinner';
import WeatherWidget from '../components/WeatherWidget';

const HomePage = () => {
    const [trails, setTrails] = useState([]);
    const [bikes, setBikes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [videoLoaded, setVideoLoaded] = useState(false);
    const [showVideo, setShowVideo] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            try {
                // Use mock data for now since backend isn't set up
                const mockTrails = [
                    {
                        id: 1,
                        name: "Table Rock Lake Trail",
                        slug: "table-rock-lake",
                        description: "A scenic paved trail that follows the beautiful shoreline of Table Rock Lake. Perfect for families and beginners.",
                        difficulty_level: "easy",
                        distance_miles: 10.5,
                        estimated_duration_hours: 2.5,
                        trail_type: "paved",
                        image_gallery: ["/images/trails/table-rock-placeholder.jpg"]
                    },
                    {
                        id: 2,
                        name: "Dogwood Canyon Nature Park",
                        slug: "dogwood-canyon",
                        description: "Experience the natural beauty of Dogwood Canyon with its pristine streams, waterfalls, and diverse wildlife.",
                        difficulty_level: "moderate",
                        distance_miles: 6.2,
                        estimated_duration_hours: 2.0,
                        trail_type: "mixed",
                        image_gallery: ["/images/trails/dogwood-placeholder.jpg"]
                    },
                    {
                        id: 3,
                        name: "Branson Creek Trail",
                        slug: "branson-creek",
                        description: "A peaceful trail winding through Branson Creek area with gentle hills and beautiful forest scenery.",
                        difficulty_level: "moderate",
                        distance_miles: 8.3,
                        estimated_duration_hours: 2.5,
                        trail_type: "gravel",
                        image_gallery: ["/images/trails/branson-creek-placeholder.jpg"]
                    }
                ];

                const mockBikes = [
                    {
                        id: 1,
                        bike_number: 'BB001',
                        model: 'Comfort E-Bike',
                        brand: 'Lectric',
                        bike_type: 'comfort',
                        battery_capacity: '48V 14Ah',
                        max_range_miles: 45,
                        hourly_rate: 22.00,
                        daily_rate: 110.00,
                        image_url: '/images/bikes/Comfort_Lectric.png',
                        specifications: { motor: '750W', top_speed: '20mph', weight: '65lbs' }
                    },
                    {
                        id: 2,
                        bike_number: 'BB002',
                        model: 'Standard E-Bike',
                        brand: 'Lectric',
                        bike_type: 'standard',
                        battery_capacity: '48V 14Ah',
                        max_range_miles: 50,
                        hourly_rate: 25.00,
                        daily_rate: 120.00,
                        image_url: '/images/bikes/Standard_Lectric.png',
                        specifications: { motor: '750W', top_speed: '20mph', weight: '73lbs' }
                    },
                    {
                        id: 3,
                        bike_number: 'BB003',
                        model: 'RadRover 6 Plus',
                        brand: 'Rad Power Bikes',
                        bike_type: 'mountain',
                        battery_capacity: '48V 14Ah',
                        max_range_miles: 45,
                        hourly_rate: 30.00,
                        daily_rate: 150.00,
                        image_url: '/images/bikes/Rad_Bikes.png',
                        specifications: { motor: '750W', top_speed: '20mph', weight: '76lbs' }
                    }
                ];

                setTrails(mockTrails);
                setBikes(mockBikes);
            } catch (error) {
                console.error('Error fetching homepage data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();

        // Start video fade-in after initial load
        const videoTimer = setTimeout(() => {
            setShowVideo(true);
        }, 1500); // Start showing video after 1.5 seconds

        return () => clearTimeout(videoTimer);
    }, []);

    if (loading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="homepage">
            {/* Hero Section with Video Background */}
            <section className="hero-section position-relative text-white overflow-hidden">
                {/* Green Background Overlay */}
                <div
                    className={`hero-background-overlay ${showVideo ? 'fade-out' : ''}`}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(135deg, #f15808 0%, #d84315 100%)',
                        zIndex: 1,
                        transition: 'opacity 2s ease-in-out'
                    }}
                ></div>

                {/* YouTube Video Background */}
                {showVideo && (
                    <div
                        className="video-background fade-in"
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            zIndex: 0,
                            opacity: 1,
                            transition: 'opacity 2s ease-in-out'
                        }}
                    >
                        {/* Video for desktop */}
                        <div className="d-none d-md-block">
                            <iframe
                                width="100%"
                                height="100%"
                                src="https://www.youtube.com/embed/0oEysAC1NOo?autoplay=1&mute=1&loop=1&playlist=0oEysAC1NOo&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&start=0&vq=hd1080&enablejsapi=1"
                                title="Bike Branson Background Video"
                                frameBorder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                                style={{
                                    position: 'absolute',
                                    top: '-10%',
                                    left: '-10%',
                                    width: '120%',
                                    height: '120%',
                                    minWidth: '120%',
                                    minHeight: '120%',
                                    objectFit: 'cover'
                                }}
                                onLoad={() => setVideoLoaded(true)}
                            ></iframe>
                        </div>

                        {/* Fallback image for mobile */}
                        <div className="d-md-none">
                            <img
                                src="/images/hero-trail-background.jpg"
                                alt="Beautiful Branson trail"
                                style={{
                                    position: 'absolute',
                                    top: '50%',
                                    left: '50%',
                                    minWidth: '100%',
                                    minHeight: '100%',
                                    width: 'auto',
                                    height: 'auto',
                                    transform: 'translate(-50%, -50%)',
                                    objectFit: 'cover'
                                }}
                            />
                        </div>

                        {/* Dark overlay for text readability */}
                        <div
                            style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                background: 'rgba(0, 0, 0, 0.4)',
                                zIndex: 1
                            }}
                        ></div>
                    </div>
                )}

                {/* Hero Content */}
                <Container style={{ position: 'relative', zIndex: 2 }}>
                    <Row className="align-items-center min-vh-75">
                        <Col lg={8} className="mx-auto text-center">
                            <div className="hero-content py-5">
                                <h1 className="display-3 fw-bold mb-4 text-shadow">
                                    The Outdoors, <span className="text-warning">Effortlessly!</span>
                                </h1>
                                <p className="lead mb-5 fs-4 text-shadow">
                                    Explore Branson's most beautiful trails with our premium e-bike fleet.
                                    From Table Rock Lake to Dogwood Canyon, discover the natural beauty
                                    of Missouri with ease and comfort.
                                </p>
                                <div className="hero-buttons">
                                    <Button
                                        as={Link}
                                        to="/book"
                                        variant="warning"
                                        size="lg"
                                        className="me-3 mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg"
                                    >
                                        <i className="bi bi-calendar-check me-2"></i>
                                        Book Your Adventure
                                    </Button>
                                    <Button
                                        as={Link}
                                        to="/trails"
                                        variant="outline-light"
                                        size="lg"
                                        className="mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg"
                                        style={{
                                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                            backdropFilter: 'blur(10px)',
                                            border: '2px solid rgba(255, 255, 255, 0.3)'
                                        }}
                                    >
                                        <i className="bi bi-map me-2"></i>
                                        Explore Trails
                                    </Button>
                                </div>

                                {/* Loading indicator */}
                                {!showVideo && (
                                    <div className="mt-4">
                                        <div className="loading-spinner large mx-auto mb-2"></div>
                                        <small className="text-light opacity-75">
                                            Loading your adventure...
                                        </small>
                                    </div>
                                )}

                                {/* Video Credit */}
                                {showVideo && videoLoaded && (
                                    <div className="mt-4 fade-in">
                                        <small className="text-light opacity-75">
                                            <i className="bi bi-camera-video me-1"></i>
                                            Experience the beauty of Branson's trails
                                        </small>
                                    </div>
                                )}
                            </div>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Weather Widget */}
            <section className="weather-section py-3 bg-light">
                <Container>
                    <WeatherWidget />
                </Container>
            </section>

            {/* Featured Trails Section */}
            <section className="featured-trails py-5">
                <Container>
                    <Row>
                        <Col lg={8} className="mx-auto text-center mb-5">
                            <h2 className="display-5 fw-bold text-primary mb-3">
                                Featured Trails
                            </h2>
                            <p className="lead text-muted">
                                Discover Branson's most popular e-bike destinations
                            </p>
                        </Col>
                    </Row>
                    <Row>
                        {trails.map((trail) => (
                            <Col md={4} key={trail.id} className="mb-4">
                                <Card className="trail-card h-100 shadow-sm">
                                    <Card.Img 
                                        variant="top" 
                                        src={trail.image_gallery?.[0] || '/images/trail-placeholder.jpg'}
                                        alt={trail.name}
                                        style={{ height: '200px', objectFit: 'cover' }}
                                    />
                                    <Card.Body className="d-flex flex-column">
                                        <Card.Title className="text-primary">
                                            {trail.name}
                                        </Card.Title>
                                        <Card.Text className="text-muted flex-grow-1">
                                            {trail.description.substring(0, 120)}...
                                        </Card.Text>
                                        <div className="trail-info mb-3">
                                            <small className="text-muted">
                                                <i className="bi bi-geo-alt me-1"></i>
                                                {trail.distance_miles} miles • 
                                                <i className="bi bi-clock ms-2 me-1"></i>
                                                {trail.estimated_duration_hours}h • 
                                                <span className={`badge ms-2 ${
                                                    trail.difficulty_level === 'easy' ? 'bg-success' :
                                                    trail.difficulty_level === 'moderate' ? 'bg-warning' : 'bg-danger'
                                                }`}>
                                                    {trail.difficulty_level}
                                                </span>
                                            </small>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center">
                                            <Button 
                                                as={Link} 
                                                to={`/trails/${trail.slug}`} 
                                                variant="outline-primary"
                                                size="sm"
                                            >
                                                Learn More
                                            </Button>
                                            <Button 
                                                as={Link} 
                                                to={`/book?trail=${trail.slug}`} 
                                                variant="primary"
                                                size="sm"
                                            >
                                                Book Trail
                                            </Button>
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Col>
                        ))}
                    </Row>
                    <Row>
                        <Col className="text-center mt-4">
                            <Button 
                                as={Link} 
                                to="/trails" 
                                variant="outline-primary" 
                                size="lg"
                            >
                                View All Trails
                            </Button>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Our Fleet Section */}
            <section className="our-fleet py-5 bg-light">
                <Container>
                    <Row>
                        <Col lg={8} className="mx-auto text-center mb-5">
                            <h2 className="display-5 fw-bold text-primary mb-3">
                                Our Premium E-Bike Fleet
                            </h2>
                            <p className="lead text-muted">
                                High-quality electric bikes for every adventure
                            </p>
                        </Col>
                    </Row>
                    <Row>
                        {bikes.map((bike) => (
                            <Col md={4} key={bike.id} className="mb-4">
                                <Card className="bike-card h-100 shadow-sm">
                                    <Card.Img
                                        variant="top"
                                        src={bike.image_url}
                                        alt={`${bike.brand} ${bike.model}`}
                                        style={{ height: '250px', objectFit: 'contain', backgroundColor: '#f8f9fa', padding: '20px' }}
                                    />
                                    <Card.Body className="text-center">
                                        <Card.Title className="text-primary">
                                            {bike.brand} {bike.model}
                                        </Card.Title>
                                        <Card.Text className="text-muted">
                                            {bike.bike_type.charAt(0).toUpperCase() + bike.bike_type.slice(1)} E-Bike
                                        </Card.Text>
                                        <div className="bike-specs mb-3">
                                            <small className="text-muted">
                                                <i className="bi bi-battery-charging me-1"></i>
                                                {bike.max_range_miles} miles range<br/>
                                                <i className="bi bi-speedometer2 me-1"></i>
                                                {bike.specifications.top_speed}<br/>
                                                <i className="bi bi-lightning me-1"></i>
                                                {bike.specifications.motor} motor
                                            </small>
                                        </div>
                                        <div className="pricing">
                                            <span className="h5 text-primary fw-bold">
                                                ${bike.hourly_rate}/hour
                                            </span>
                                            <br/>
                                            <small className="text-muted">
                                                ${bike.daily_rate}/day
                                            </small>
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Col>
                        ))}
                    </Row>
                    <Row>
                        <Col className="text-center mt-4">
                            <Button 
                                as={Link} 
                                to="/bikes" 
                                variant="outline-primary" 
                                size="lg"
                            >
                                View All Bikes
                            </Button>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Why Choose Us Section */}
            <section className="why-choose-us py-5">
                <Container>
                    <Row>
                        <Col lg={8} className="mx-auto text-center mb-5">
                            <h2 className="display-5 fw-bold text-primary mb-3">
                                Why Choose Bike Branson?
                            </h2>
                        </Col>
                    </Row>
                    <Row>
                        <Col md={4} className="text-center mb-4">
                            <div className="feature-icon mb-3">
                                <i className="bi bi-bicycle display-4 text-primary"></i>
                            </div>
                            <h4 className="fw-bold">Premium E-Bikes</h4>
                            <p className="text-muted">
                                Top-quality electric bikes from trusted brands, 
                                regularly maintained for optimal performance.
                            </p>
                        </Col>
                        <Col md={4} className="text-center mb-4">
                            <div className="feature-icon mb-3">
                                <i className="bi bi-geo-alt display-4 text-primary"></i>
                            </div>
                            <h4 className="fw-bold">Local Expertise</h4>
                            <p className="text-muted">
                                We know Branson's trails inside and out. Get insider 
                                tips and recommendations for the best experience.
                            </p>
                        </Col>
                        <Col md={4} className="text-center mb-4">
                            <div className="feature-icon mb-3">
                                <i className="bi bi-truck display-4 text-primary"></i>
                            </div>
                            <h4 className="fw-bold">Delivery Available</h4>
                            <p className="text-muted">
                                We'll bring the bikes to you! Delivery service 
                                available to popular trail locations.
                            </p>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Call to Action Section */}
            <section className="cta-section py-5 bg-primary text-white">
                <Container>
                    <Row>
                        <Col lg={8} className="mx-auto text-center">
                            <h2 className="display-5 fw-bold mb-3">
                                Ready for Your Adventure?
                            </h2>
                            <p className="lead mb-4">
                                Book your e-bike rental today and experience the outdoors like never before!
                            </p>
                            <Button 
                                as={Link} 
                                to="/book" 
                                variant="warning" 
                                size="lg"
                                className="me-3"
                            >
                                <i className="bi bi-calendar-check me-2"></i>
                                Book Your Ride
                            </Button>
                            <Button 
                                as={Link} 
                                to="/contact" 
                                variant="outline-light" 
                                size="lg"
                            >
                                <i className="bi bi-telephone me-2"></i>
                                Contact Us
                            </Button>
                        </Col>
                    </Row>
                </Container>
            </section>
        </div>
    );
};

export default HomePage;
