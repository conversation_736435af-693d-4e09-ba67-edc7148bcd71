/**
 * Authentication Context for Bike Branson Rental System
 * Manages user authentication state and provides auth methods
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { apiService } from '../services/apiService';
import { toast } from 'react-toastify';

// Initial state
const initialState = {
    user: null,
    loading: true,
    error: null,
};

// Action types
const AUTH_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_USER: 'SET_USER',
    SET_ERROR: 'SET_ERROR',
    LOGOUT: 'LOGOUT',
    CLEAR_ERROR: 'CLEAR_ERROR',
};

// Auth reducer
const authReducer = (state, action) => {
    switch (action.type) {
        case AUTH_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: action.payload,
            };
        case AUTH_ACTIONS.SET_USER:
            return {
                ...state,
                user: action.payload,
                loading: false,
                error: null,
            };
        case AUTH_ACTIONS.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                loading: false,
            };
        case AUTH_ACTIONS.LOGOUT:
            return {
                ...state,
                user: null,
                loading: false,
                error: null,
            };
        case AUTH_ACTIONS.CLEAR_ERROR:
            return {
                ...state,
                error: null,
            };
        default:
            return state;
    }
};

// Create context
const AuthContext = createContext();

/**
 * Auth Provider Component
 */
export const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);

    // Check if user is authenticated on app load
    useEffect(() => {
        const checkAuth = async () => {
            try {
                const token = apiService.getAuthToken();
                if (token) {
                    // Verify token by fetching user profile
                    const response = await apiService.get('/auth/profile');
                    dispatch({
                        type: AUTH_ACTIONS.SET_USER,
                        payload: response.data,
                    });
                } else {
                    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
                }
            } catch (error) {
                // Token is invalid, remove it
                apiService.removeAuthToken();
                dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            }
        };

        checkAuth();
    }, []);

    /**
     * Login user
     */
    const login = async (credentials) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.login(credentials);
            
            dispatch({
                type: AUTH_ACTIONS.SET_USER,
                payload: response.data.user,
            });

            toast.success('Login successful!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Login failed';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Register user
     */
    const register = async (userData) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.register(userData);
            
            dispatch({
                type: AUTH_ACTIONS.SET_USER,
                payload: response.data.user,
            });

            toast.success('Registration successful!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Registration failed';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Logout user
     */
    const logout = async () => {
        try {
            await apiService.logout();
            dispatch({ type: AUTH_ACTIONS.LOGOUT });
            toast.success('Logged out successfully');
        } catch (error) {
            // Even if logout fails on server, clear local state
            dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
    };

    /**
     * Forgot password
     */
    const forgotPassword = async (email) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.forgotPassword(email);
            
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            toast.success('Password reset email sent!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to send reset email';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Reset password
     */
    const resetPassword = async (token, password) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.resetPassword(token, password);
            
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            toast.success('Password reset successful!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Password reset failed';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Verify email
     */
    const verifyEmail = async (token) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.verifyEmail(token);
            
            // Update user state to reflect email verification
            if (state.user) {
                dispatch({
                    type: AUTH_ACTIONS.SET_USER,
                    payload: {
                        ...state.user,
                        emailVerified: true,
                    },
                });
            }

            toast.success('Email verified successfully!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Email verification failed';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Update user profile
     */
    const updateProfile = async (userData) => {
        try {
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

            const response = await apiService.put('/auth/profile', userData);
            
            dispatch({
                type: AUTH_ACTIONS.SET_USER,
                payload: response.data,
            });

            toast.success('Profile updated successfully!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Profile update failed';
            dispatch({
                type: AUTH_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Clear error
     */
    const clearError = () => {
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
    };

    // Helper functions
    const isAuthenticated = () => !!state.user;
    const isAdmin = () => state.user?.userType === 'admin' || state.user?.userType === 'staff';
    const isEmailVerified = () => state.user?.emailVerified;

    // Context value
    const value = {
        ...state,
        login,
        register,
        logout,
        forgotPassword,
        resetPassword,
        verifyEmail,
        updateProfile,
        clearError,
        isAuthenticated,
        isAdmin,
        isEmailVerified,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

/**
 * Custom hook to use auth context
 */
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
