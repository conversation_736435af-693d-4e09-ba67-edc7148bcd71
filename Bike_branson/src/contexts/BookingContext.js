/**
 * BookingContext for Bike Branson Rental System
 * Manages booking state and provides booking methods
 */

import React, { createContext, useContext, useReducer } from 'react';
import { apiService } from '../services/apiService';
import { toast } from 'react-toastify';

// Initial state
const initialState = {
    currentBooking: null,
    bookings: [],
    loading: false,
    error: null,
    availabilityData: null,
};

// Action types
const BOOKING_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_ERROR: 'SET_ERROR',
    SET_CURRENT_BOOKING: 'SET_CURRENT_BOOKING',
    SET_BOOKINGS: 'SET_BOOKINGS',
    ADD_BOOKING: 'ADD_BOOKING',
    UPDATE_BOOKING: 'UPDATE_BOOKING',
    SET_AVAILABILITY: 'SET_AVAILABILITY',
    CLEAR_ERROR: 'CLEAR_ERROR',
    CLEAR_CURRENT_BOOKING: 'CLEAR_CURRENT_BOOKING',
};

// Booking reducer
const bookingReducer = (state, action) => {
    switch (action.type) {
        case BOOKING_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: action.payload,
            };
        case BOOKING_ACTIONS.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                loading: false,
            };
        case BOOKING_ACTIONS.SET_CURRENT_BOOKING:
            return {
                ...state,
                currentBooking: action.payload,
                loading: false,
                error: null,
            };
        case BOOKING_ACTIONS.SET_BOOKINGS:
            return {
                ...state,
                bookings: action.payload,
                loading: false,
                error: null,
            };
        case BOOKING_ACTIONS.ADD_BOOKING:
            return {
                ...state,
                bookings: [action.payload, ...state.bookings],
                currentBooking: action.payload,
                loading: false,
                error: null,
            };
        case BOOKING_ACTIONS.UPDATE_BOOKING:
            return {
                ...state,
                bookings: state.bookings.map(booking =>
                    booking.id === action.payload.id ? action.payload : booking
                ),
                currentBooking: state.currentBooking?.id === action.payload.id 
                    ? action.payload 
                    : state.currentBooking,
                loading: false,
                error: null,
            };
        case BOOKING_ACTIONS.SET_AVAILABILITY:
            return {
                ...state,
                availabilityData: action.payload,
                loading: false,
                error: null,
            };
        case BOOKING_ACTIONS.CLEAR_ERROR:
            return {
                ...state,
                error: null,
            };
        case BOOKING_ACTIONS.CLEAR_CURRENT_BOOKING:
            return {
                ...state,
                currentBooking: null,
            };
        default:
            return state;
    }
};

// Create context
const BookingContext = createContext();

/**
 * Booking Provider Component
 */
export const BookingProvider = ({ children }) => {
    const [state, dispatch] = useReducer(bookingReducer, initialState);

    /**
     * Create a new booking
     */
    const createBooking = async (bookingData) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.createBooking(bookingData);
            
            dispatch({
                type: BOOKING_ACTIONS.ADD_BOOKING,
                payload: response.data,
            });

            toast.success('Booking created successfully!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to create booking';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Get user's bookings
     */
    const fetchBookings = async (params = {}) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.getBookings(params);
            
            dispatch({
                type: BOOKING_ACTIONS.SET_BOOKINGS,
                payload: response.data || [],
            });

            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to fetch bookings';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Get a specific booking
     */
    const fetchBooking = async (bookingId) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.getBooking(bookingId);
            
            dispatch({
                type: BOOKING_ACTIONS.SET_CURRENT_BOOKING,
                payload: response.data,
            });

            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to fetch booking';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Update a booking
     */
    const updateBooking = async (bookingId, updateData) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.updateBooking(bookingId, updateData);
            
            dispatch({
                type: BOOKING_ACTIONS.UPDATE_BOOKING,
                payload: response.data,
            });

            toast.success('Booking updated successfully!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to update booking';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Cancel a booking
     */
    const cancelBooking = async (bookingId, reason) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.cancelBooking(bookingId, reason);
            
            dispatch({
                type: BOOKING_ACTIONS.UPDATE_BOOKING,
                payload: response.data,
            });

            toast.success('Booking cancelled successfully!');
            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to cancel booking';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Check availability for a specific date/time
     */
    const checkAvailability = async (date, timeSlot, duration) => {
        try {
            dispatch({ type: BOOKING_ACTIONS.SET_LOADING, payload: true });
            dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });

            const response = await apiService.checkAvailability(date, timeSlot, duration);
            
            dispatch({
                type: BOOKING_ACTIONS.SET_AVAILABILITY,
                payload: response.data,
            });

            return response;
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to check availability';
            dispatch({
                type: BOOKING_ACTIONS.SET_ERROR,
                payload: errorMessage,
            });
            throw error;
        }
    };

    /**
     * Clear error
     */
    const clearError = () => {
        dispatch({ type: BOOKING_ACTIONS.CLEAR_ERROR });
    };

    /**
     * Clear current booking
     */
    const clearCurrentBooking = () => {
        dispatch({ type: BOOKING_ACTIONS.CLEAR_CURRENT_BOOKING });
    };

    // Context value
    const value = {
        ...state,
        createBooking,
        fetchBookings,
        fetchBooking,
        updateBooking,
        cancelBooking,
        checkAvailability,
        clearError,
        clearCurrentBooking,
    };

    return (
        <BookingContext.Provider value={value}>
            {children}
        </BookingContext.Provider>
    );
};

/**
 * Custom hook to use booking context
 */
export const useBooking = () => {
    const context = useContext(BookingContext);
    if (!context) {
        throw new Error('useBooking must be used within a BookingProvider');
    }
    return context;
};
