/**
 * Main App Component for Bike Branson Rental System
 * React Router setup with authentication context and global state management
 */

import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ToastContainer } from 'react-toastify';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

// Import components
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import TrailsPage from './pages/TrailsPage';
import TrailDetailPage from './pages/TrailDetailPage';
import BookingPage from './pages/BookingPage';
import BikeInventoryPage from './pages/BikeInventoryPage';
import CustomerPortal from './pages/CustomerPortal';
import AdminDashboard from './pages/AdminDashboard';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ContactPage from './pages/ContactPage';
import AboutPage from './pages/AboutPage';
import PrivacyPage from './pages/PrivacyPage';
import TermsPage from './pages/TermsPage';
import NotFoundPage from './pages/NotFoundPage';

// Import context providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { BookingProvider } from './contexts/BookingContext';

// Import utilities
import { apiService } from './services/apiService';
import LoadingSpinner from './components/LoadingSpinner';

// Import styles
import 'react-toastify/dist/ReactToastify.css';
import './styles/App.css';

// Initialize Stripe (disabled for preview)
const stripePromise = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY
    ? loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY)
    : Promise.resolve(null);

// Initialize React Query client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 1,
            refetchOnWindowFocus: false,
            staleTime: 5 * 60 * 1000, // 5 minutes
        },
    },
});

/**
 * Protected Route Component
 * Redirects to login if user is not authenticated
 */
const ProtectedRoute = ({ children, adminOnly = false }) => {
    const { user, loading } = useAuth();

    if (loading) {
        return <LoadingSpinner />;
    }

    if (!user) {
        return <Navigate to="/login" replace />;
    }

    if (adminOnly && user.userType !== 'admin' && user.userType !== 'staff') {
        return <Navigate to="/" replace />;
    }

    return children;
};

/**
 * Public Route Component
 * Redirects authenticated users away from auth pages
 */
const PublicRoute = ({ children }) => {
    const { user, loading } = useAuth();

    if (loading) {
        return <LoadingSpinner />;
    }

    if (user) {
        return <Navigate to="/" replace />;
    }

    return children;
};

/**
 * Main App Layout Component
 */
const AppLayout = ({ children }) => {
    return (
        <div className="app-layout">
            <Navigation />
            <main className="main-content">
                {children}
            </main>
            <Footer />
        </div>
    );
};

/**
 * App Routes Component
 */
const AppRoutes = () => {
    return (
        <Routes>
            {/* Public Routes */}
            <Route path="/" element={
                <AppLayout>
                    <HomePage />
                </AppLayout>
            } />
            
            <Route path="/trails" element={
                <AppLayout>
                    <TrailsPage />
                </AppLayout>
            } />
            
            <Route path="/trails/:slug" element={
                <AppLayout>
                    <TrailDetailPage />
                </AppLayout>
            } />
            
            <Route path="/bikes" element={
                <AppLayout>
                    <BikeInventoryPage />
                </AppLayout>
            } />
            
            <Route path="/book" element={
                <AppLayout>
                    <BookingPage />
                </AppLayout>
            } />
            
            <Route path="/contact" element={
                <AppLayout>
                    <ContactPage />
                </AppLayout>
            } />
            
            <Route path="/about" element={
                <AppLayout>
                    <AboutPage />
                </AppLayout>
            } />
            
            <Route path="/privacy" element={
                <AppLayout>
                    <PrivacyPage />
                </AppLayout>
            } />
            
            <Route path="/terms" element={
                <AppLayout>
                    <TermsPage />
                </AppLayout>
            } />

            {/* Authentication Routes */}
            <Route path="/login" element={
                <PublicRoute>
                    <LoginPage />
                </PublicRoute>
            } />
            
            <Route path="/register" element={
                <PublicRoute>
                    <RegisterPage />
                </PublicRoute>
            } />

            {/* Protected Customer Routes */}
            <Route path="/portal" element={
                <ProtectedRoute>
                    <AppLayout>
                        <CustomerPortal />
                    </AppLayout>
                </ProtectedRoute>
            } />

            {/* Protected Admin Routes */}
            <Route path="/admin/*" element={
                <ProtectedRoute adminOnly={true}>
                    <AdminDashboard />
                </ProtectedRoute>
            } />

            {/* 404 Route */}
            <Route path="*" element={
                <AppLayout>
                    <NotFoundPage />
                </AppLayout>
            } />
        </Routes>
    );
};

/**
 * Main App Component
 */
const App = () => {
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // Initialize app
        const initializeApp = async () => {
            try {
                // Check if API is available
                await apiService.get('/health');
                
                // Any other initialization logic
                
            } catch (error) {
                console.error('Failed to initialize app:', error);
            } finally {
                setIsLoading(false);
            }
        };

        initializeApp();
    }, []);

    if (isLoading) {
        return <LoadingSpinner fullScreen />;
    }

    return (
        <QueryClientProvider client={queryClient}>
            {stripePromise ? (
                <Elements stripe={stripePromise}>
                    <AuthProvider>
                        <BookingProvider>
                            <Router>
                                <div className="App">
                                    <AppRoutes />

                                    {/* Toast Notifications */}
                                    <ToastContainer
                                        position="top-right"
                                        autoClose={5000}
                                        hideProgressBar={false}
                                        newestOnTop={false}
                                        closeOnClick
                                        rtl={false}
                                        pauseOnFocusLoss
                                        draggable
                                        pauseOnHover
                                        theme="light"
                                    />
                                </div>
                            </Router>
                        </BookingProvider>
                    </AuthProvider>
                </Elements>
            ) : (
                <AuthProvider>
                    <BookingProvider>
                        <Router>
                            <div className="App">
                                <AppRoutes />

                                {/* Toast Notifications */}
                                <ToastContainer
                                    position="top-right"
                                    autoClose={5000}
                                    hideProgressBar={false}
                                    newestOnTop={false}
                                    closeOnClick
                                    rtl={false}
                                    pauseOnFocusLoss
                                    draggable
                                    pauseOnHover
                                    theme="light"
                                />
                            </div>
                        </Router>
                    </BookingProvider>
                </AuthProvider>
            )}
        </QueryClientProvider>
    );
};

export default App;
