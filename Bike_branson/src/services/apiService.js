/**
 * API Service for Bike Branson Rental System
 * Centralized HTTP client with authentication and error handling
 */

import axios from 'axios';
import { toast } from 'react-toastify';

// Create axios instance with base configuration
const api = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Token management
const getToken = () => localStorage.getItem('bikeBransonToken');
const setToken = (token) => localStorage.setItem('bikeBransonToken', token);
const removeToken = () => localStorage.removeItem('bikeBransonToken');

// Request interceptor to add auth token
api.interceptors.request.use(
    (config) => {
        const token = getToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        const { response } = error;
        
        if (response) {
            const { status, data } = response;
            
            // Handle different error status codes
            switch (status) {
                case 401:
                    // Unauthorized - remove token and redirect to login
                    removeToken();
                    if (window.location.pathname !== '/login') {
                        toast.error('Your session has expired. Please log in again.');
                        window.location.href = '/login';
                    }
                    break;
                    
                case 403:
                    toast.error('You do not have permission to perform this action.');
                    break;
                    
                case 404:
                    toast.error('The requested resource was not found.');
                    break;
                    
                case 422:
                    // Validation errors
                    if (data.errors && Array.isArray(data.errors)) {
                        data.errors.forEach(err => {
                            toast.error(`${err.field}: ${err.message}`);
                        });
                    } else {
                        toast.error(data.message || 'Validation error occurred.');
                    }
                    break;
                    
                case 429:
                    toast.error('Too many requests. Please try again later.');
                    break;
                    
                case 500:
                    toast.error('Server error. Please try again later.');
                    break;
                    
                default:
                    toast.error(data.message || 'An unexpected error occurred.');
            }
        } else if (error.request) {
            // Network error
            toast.error('Network error. Please check your connection.');
        } else {
            // Other error
            toast.error('An unexpected error occurred.');
        }
        
        return Promise.reject(error);
    }
);

/**
 * API Service Class
 */
class ApiService {
    // Generic HTTP methods
    async get(url, config = {}) {
        const response = await api.get(url, config);
        return response.data;
    }

    async post(url, data = {}, config = {}) {
        const response = await api.post(url, data, config);
        return response.data;
    }

    async put(url, data = {}, config = {}) {
        const response = await api.put(url, data, config);
        return response.data;
    }

    async patch(url, data = {}, config = {}) {
        const response = await api.patch(url, data, config);
        return response.data;
    }

    async delete(url, config = {}) {
        const response = await api.delete(url, config);
        return response.data;
    }

    // Authentication methods
    async login(credentials) {
        const response = await this.post('/auth/login', credentials);
        if (response.data.token) {
            setToken(response.data.token);
        }
        return response;
    }

    async register(userData) {
        const response = await this.post('/auth/register', userData);
        if (response.data.token) {
            setToken(response.data.token);
        }
        return response;
    }

    async logout() {
        removeToken();
        // Could also call a logout endpoint if needed
        return Promise.resolve();
    }

    async forgotPassword(email) {
        return await this.post('/auth/forgot-password', { email });
    }

    async resetPassword(token, password) {
        return await this.post('/auth/reset-password', { token, password });
    }

    async verifyEmail(token) {
        return await this.post('/auth/verify-email', { token });
    }

    // Booking methods
    async getBookings(params = {}) {
        return await this.get('/bookings', { params });
    }

    async getBooking(id) {
        return await this.get(`/bookings/${id}`);
    }

    async createBooking(bookingData) {
        return await this.post('/bookings', bookingData);
    }

    async updateBooking(id, bookingData) {
        return await this.put(`/bookings/${id}`, bookingData);
    }

    async cancelBooking(id, reason) {
        return await this.patch(`/bookings/${id}/cancel`, { reason });
    }

    async checkAvailability(date, timeSlot, duration) {
        return await this.get('/bookings/availability', {
            params: { date, timeSlot, duration }
        });
    }

    // Bike methods
    async getBikes(params = {}) {
        return await this.get('/bikes', { params });
    }

    async getBike(id) {
        return await this.get(`/bikes/${id}`);
    }

    // Trail methods
    async getTrails() {
        return await this.get('/trails');
    }

    async getTrail(slug) {
        return await this.get(`/trails/${slug}`);
    }

    // Payment methods
    async createPaymentIntent(bookingId, amount) {
        return await this.post('/payments/create-intent', { bookingId, amount });
    }

    async confirmPayment(paymentIntentId) {
        return await this.post('/payments/confirm', { paymentIntentId });
    }

    async processRefund(paymentId, amount, reason) {
        return await this.post('/payments/refund', { paymentId, amount, reason });
    }

    // Contact methods
    async submitContactForm(formData) {
        return await this.post('/contact', formData);
    }

    // Admin methods
    async getAdminStats() {
        return await this.get('/admin/stats');
    }

    async getAdminBookings(params = {}) {
        return await this.get('/admin/bookings', { params });
    }

    async updateBookingStatus(id, status) {
        return await this.patch(`/admin/bookings/${id}/status`, { status });
    }

    // File upload method
    async uploadFile(file, type = 'general') {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        
        return await this.post('/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    }

    // Utility methods
    isAuthenticated() {
        return !!getToken();
    }

    getAuthToken() {
        return getToken();
    }

    setAuthToken(token) {
        setToken(token);
    }

    removeAuthToken() {
        removeToken();
    }
}

// Create and export service instance
export const apiService = new ApiService();

// Export token management functions
export { getToken, setToken, removeToken };

// Export axios instance for direct use if needed
export { api };
