/**
 * Image Mapper Utility
 * Automatically maps image files to their appropriate usage in the application
 */

// Image mapping configuration
export const imageConfig = {
    // Logo mappings
    logos: {
        'Bike Branson Logo Dark small.png': {
            usage: ['navigation', 'login', 'register'],
            description: 'Main dark logo for light backgrounds'
        },
        'Bike Branson Logo Light.png': {
            usage: ['footer', 'dark-backgrounds'],
            description: 'Light logo for dark backgrounds'
        }
    },
    
    // Bike image mappings
    bikes: {
        'Comfort_Lectric.png': {
            model: 'Comfort E-Bike',
            brand: 'Lectric',
            type: 'comfort',
            usage: ['homepage', 'inventory', 'booking']
        },
        'Standard_Lectric.png': {
            model: 'Standard E-Bike', 
            brand: 'Lectric',
            type: 'standard',
            usage: ['homepage', 'inventory', 'booking']
        },
        'Rad_Bikes.png': {
            model: 'RadRover 6 Plus',
            brand: 'Rad Power Bikes',
            type: 'mountain',
            usage: ['homepage', 'inventory', 'booking']
        }
    },
    
    // Trail image mappings
    trails: {
        'table_rock-Lake.jpg': {
            name: 'Table Rock Lake Trail',
            slug: 'table-rock-lake',
            difficulty: 'easy',
            usage: ['homepage', 'trails-page', 'trail-detail']
        },
        'Dogwood_Canyon.png': {
            name: 'Dogwood Canyon Nature Park',
            slug: 'dogwood-canyon', 
            difficulty: 'moderate',
            usage: ['homepage', 'trails-page', 'trail-detail']
        },
        'Roark Creek Trail.png': {
            name: 'Roark Creek Trail',
            slug: 'roark-creek',
            difficulty: 'moderate', 
            usage: ['homepage', 'trails-page', 'trail-detail']
        }
    }
};

/**
 * Get image path for a specific usage
 */
export const getImagePath = (category, filename) => {
    return `/images/${category}/${filename}`;
};

/**
 * Get all images for a category
 */
export const getImagesForCategory = (category) => {
    return Object.keys(imageConfig[category] || {}).map(filename => ({
        filename,
        path: getImagePath(category, filename),
        ...imageConfig[category][filename]
    }));
};

/**
 * Auto-detect new images and suggest integration points
 */
export const detectNewImages = (existingImages, newImages) => {
    const suggestions = [];
    
    newImages.forEach(image => {
        const { category, filename } = image;
        
        if (!imageConfig[category]?.[filename]) {
            // New image detected - suggest integration
            suggestions.push({
                filename,
                category,
                path: getImagePath(category, filename),
                suggestedUsage: getSuggestedUsage(category, filename)
            });
        }
    });
    
    return suggestions;
};

/**
 * Get suggested usage based on filename and category
 */
const getSuggestedUsage = (category, filename) => {
    const name = filename.toLowerCase();
    
    switch (category) {
        case 'bikes':
            if (name.includes('comfort')) return ['homepage', 'inventory'];
            if (name.includes('standard')) return ['homepage', 'inventory'];
            if (name.includes('rad')) return ['homepage', 'inventory'];
            if (name.includes('mountain')) return ['inventory', 'booking'];
            return ['inventory'];
            
        case 'trails':
            if (name.includes('lake')) return ['homepage', 'trails-page'];
            if (name.includes('canyon')) return ['homepage', 'trails-page'];
            if (name.includes('creek')) return ['trails-page'];
            if (name.includes('river')) return ['trails-page'];
            return ['trails-page'];
            
        case 'logos':
            if (name.includes('dark')) return ['navigation', 'login'];
            if (name.includes('light')) return ['footer'];
            return ['navigation'];
            
        default:
            return ['general'];
    }
};
