/**
 * Footer Component for Bike Branson
 * Site footer with contact info, links, and business details
 */

import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { Link } from 'react-router-dom';

const Footer = () => {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="footer bg-dark text-light">
            <Container>
                <Row>
                    {/* Company Info */}
                    <Col md={4} className="mb-4">
                        <h5 className="text-primary mb-3">
                            <img
                                src="/images/logos/Bike Branson Logo Light.png"
                                alt="Bike Branson"
                                height="40"
                                className="me-2"
                            />
                        </h5>
                        <p className="mb-3">
                            The Outdoors, Effortlessly! Premium e-bike rentals 
                            for exploring Branson's beautiful trails and scenic areas.
                        </p>
                        <div className="contact-info">
                            <p className="mb-1">
                                <i className="bi bi-geo-alt me-2"></i>
                                123 Main St, Branson, MO 65616
                            </p>
                            <p className="mb-1">
                                <i className="bi bi-telephone me-2"></i>
                                <a href="tel:+14175552453" className="text-light">
                                    (417) 555-BIKE
                                </a>
                            </p>
                            <p className="mb-1">
                                <i className="bi bi-envelope me-2"></i>
                                <a href="mailto:<EMAIL>" className="text-light">
                                    <EMAIL>
                                </a>
                            </p>
                        </div>
                    </Col>

                    {/* Quick Links */}
                    <Col md={2} className="mb-4">
                        <h6 className="text-primary mb-3">Quick Links</h6>
                        <ul className="list-unstyled">
                            <li className="mb-2">
                                <Link to="/" className="text-light">Home</Link>
                            </li>
                            <li className="mb-2">
                                <Link to="/trails" className="text-light">Trails</Link>
                            </li>
                            <li className="mb-2">
                                <Link to="/bikes" className="text-light">Our Fleet</Link>
                            </li>
                            <li className="mb-2">
                                <Link to="/book" className="text-light">Book Now</Link>
                            </li>
                            <li className="mb-2">
                                <Link to="/about" className="text-light">About Us</Link>
                            </li>
                            <li className="mb-2">
                                <Link to="/contact" className="text-light">Contact</Link>
                            </li>
                        </ul>
                    </Col>

                    {/* Services */}
                    <Col md={3} className="mb-4">
                        <h6 className="text-primary mb-3">Our Services</h6>
                        <ul className="list-unstyled">
                            <li className="mb-2">E-Bike Rentals</li>
                            <li className="mb-2">Trail Delivery</li>
                            <li className="mb-2">Guided Tours</li>
                            <li className="mb-2">Group Bookings</li>
                            <li className="mb-2">Corporate Events</li>
                            <li className="mb-2">Safety Training</li>
                        </ul>
                    </Col>

                    {/* Business Hours & Social */}
                    <Col md={3} className="mb-4">
                        <h6 className="text-primary mb-3">Business Hours</h6>
                        <div className="business-hours mb-3">
                            <p className="mb-1">Monday - Thursday: 9:00 AM - 6:00 PM</p>
                            <p className="mb-1">Friday: 9:00 AM - 7:00 PM</p>
                            <p className="mb-1">Saturday: 8:00 AM - 7:00 PM</p>
                            <p className="mb-1">Sunday: 8:00 AM - 6:00 PM</p>
                        </div>
                        
                        <h6 className="text-primary mb-3">Follow Us</h6>
                        <div className="social-links">
                            <a href="https://facebook.com/bikebranson" className="text-light me-3" target="_blank" rel="noopener noreferrer">
                                <i className="bi bi-facebook fs-4"></i>
                            </a>
                            <a href="https://instagram.com/bikebranson" className="text-light me-3" target="_blank" rel="noopener noreferrer">
                                <i className="bi bi-instagram fs-4"></i>
                            </a>
                            <a href="https://twitter.com/bikebranson" className="text-light me-3" target="_blank" rel="noopener noreferrer">
                                <i className="bi bi-twitter fs-4"></i>
                            </a>
                            <a href="https://youtube.com/bikebranson" className="text-light" target="_blank" rel="noopener noreferrer">
                                <i className="bi bi-youtube fs-4"></i>
                            </a>
                        </div>
                    </Col>
                </Row>

                {/* Footer Bottom */}
                <Row className="footer-bottom">
                    <Col md={6}>
                        <p className="mb-0">
                            &copy; {currentYear} Bike Branson. All rights reserved.
                        </p>
                    </Col>
                    <Col md={6} className="text-md-end">
                        <Link to="/privacy" className="text-light me-3">Privacy Policy</Link>
                        <Link to="/terms" className="text-light">Terms of Service</Link>
                    </Col>
                </Row>
            </Container>
        </footer>
    );
};

export default Footer;
