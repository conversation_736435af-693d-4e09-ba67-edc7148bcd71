/**
 * WeatherWidget Component
 * Displays current weather conditions for Branson, MO
 */

import React, { useState, useEffect } from 'react';
import { Row, Col, Card } from 'react-bootstrap';

const WeatherWidget = () => {
    const [weather, setWeather] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        // For now, we'll use mock data since we don't have weather API set up yet
        // In production, you would fetch from OpenWeatherMap or similar service
        const mockWeatherData = {
            temperature: 72,
            condition: 'Partly Cloudy',
            humidity: 65,
            windSpeed: 8,
            icon: 'bi-cloud-sun',
            trailCondition: 'Good'
        };

        // Simulate API call delay
        setTimeout(() => {
            setWeather(mockWeatherData);
            setLoading(false);
        }, 1000);
    }, []);

    const getTrailConditionColor = (condition) => {
        switch (condition.toLowerCase()) {
            case 'excellent':
            case 'good':
                return 'text-success';
            case 'fair':
            case 'caution':
                return 'text-warning';
            case 'poor':
            case 'closed':
                return 'text-danger';
            default:
                return 'text-muted';
        }
    };

    if (loading) {
        return (
            <Card className="weather-widget">
                <Card.Body className="text-center">
                    <div className="loading-spinner"></div>
                    <p className="mt-2 mb-0 text-muted">Loading weather...</p>
                </Card.Body>
            </Card>
        );
    }

    if (error) {
        return (
            <Card className="weather-widget">
                <Card.Body className="text-center">
                    <i className="bi bi-exclamation-triangle text-warning fs-4"></i>
                    <p className="mt-2 mb-0 text-muted">Weather data unavailable</p>
                </Card.Body>
            </Card>
        );
    }

    return (
        <Card className="weather-widget">
            <Card.Body>
                <Row className="align-items-center">
                    <Col xs={12} md={3} className="text-center mb-3 mb-md-0">
                        <div className="weather-icon">
                            <i className={`bi ${weather.icon} fs-1 text-primary`}></i>
                        </div>
                        <div className="temperature">
                            <span className="h3 fw-bold">{weather.temperature}°F</span>
                        </div>
                        <div className="condition">
                            <small className="text-muted">{weather.condition}</small>
                        </div>
                    </Col>
                    
                    <Col xs={12} md={6}>
                        <Row className="text-center">
                            <Col xs={6}>
                                <div className="weather-detail">
                                    <i className="bi bi-droplet text-info"></i>
                                    <div className="mt-1">
                                        <small className="text-muted d-block">Humidity</small>
                                        <span className="fw-semibold">{weather.humidity}%</span>
                                    </div>
                                </div>
                            </Col>
                            <Col xs={6}>
                                <div className="weather-detail">
                                    <i className="bi bi-wind text-secondary"></i>
                                    <div className="mt-1">
                                        <small className="text-muted d-block">Wind</small>
                                        <span className="fw-semibold">{weather.windSpeed} mph</span>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Col>
                    
                    <Col xs={12} md={3} className="text-center">
                        <div className="trail-conditions">
                            <i className="bi bi-bicycle text-primary fs-4"></i>
                            <div className="mt-1">
                                <small className="text-muted d-block">Trail Conditions</small>
                                <span className={`fw-semibold ${getTrailConditionColor(weather.trailCondition)}`}>
                                    {weather.trailCondition}
                                </span>
                            </div>
                        </div>
                    </Col>
                </Row>
                
                <div className="text-center mt-3">
                    <small className="text-muted">
                        <i className="bi bi-geo-alt me-1"></i>
                        Branson, Missouri • Perfect for biking today!
                    </small>
                </div>
            </Card.Body>
        </Card>
    );
};

export default WeatherWidget;
