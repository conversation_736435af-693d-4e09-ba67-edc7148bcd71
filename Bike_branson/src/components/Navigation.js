/**
 * Navigation Component for Bike Branson Rental System
 * Responsive navigation bar with authentication and booking features
 */

import React, { useState } from 'react';
import { Navbar, Nav, Container, Button, Dropdown, Badge } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Navigation = () => {
    const { user, logout, isAuthenticated, isAdmin } = useAuth();
    const location = useLocation();
    const [expanded, setExpanded] = useState(false);

    const handleLogout = async () => {
        try {
            await logout();
            setExpanded(false);
        } catch (error) {
            console.error('Logout error:', error);
        }
    };

    const closeNavbar = () => setExpanded(false);

    const isActive = (path) => location.pathname === path;

    return (
        <Navbar 
            bg="white" 
            expand="lg" 
            sticky="top" 
            className="shadow-sm"
            expanded={expanded}
            onToggle={setExpanded}
        >
            <Container>
                {/* Brand Logo */}
                <Navbar.Brand as={Link} to="/" onClick={closeNavbar}>
                    <img
                        src="/images/logos/Bike Branson Logo Dark small.png"
                        alt="Bike Branson"
                        height="80"
                        className="me-2"
                        style={{ maxHeight: '80px', width: 'auto' }}
                    />
                </Navbar.Brand>

                {/* Mobile Toggle */}
                <Navbar.Toggle aria-controls="basic-navbar-nav" />

                <Navbar.Collapse id="basic-navbar-nav">
                    {/* Main Navigation Links */}
                    <Nav className="me-auto">
                        <Nav.Link 
                            as={Link} 
                            to="/" 
                            onClick={closeNavbar}
                            className={isActive('/') ? 'active fw-semibold' : ''}
                        >
                            Home
                        </Nav.Link>
                        
                        <Nav.Link 
                            as={Link} 
                            to="/trails" 
                            onClick={closeNavbar}
                            className={isActive('/trails') ? 'active fw-semibold' : ''}
                        >
                            Trails
                        </Nav.Link>
                        
                        <Nav.Link 
                            as={Link} 
                            to="/bikes" 
                            onClick={closeNavbar}
                            className={isActive('/bikes') ? 'active fw-semibold' : ''}
                        >
                            Our Fleet
                        </Nav.Link>
                        
                        <Nav.Link 
                            as={Link} 
                            to="/about" 
                            onClick={closeNavbar}
                            className={isActive('/about') ? 'active fw-semibold' : ''}
                        >
                            About
                        </Nav.Link>
                        
                        <Nav.Link 
                            as={Link} 
                            to="/contact" 
                            onClick={closeNavbar}
                            className={isActive('/contact') ? 'active fw-semibold' : ''}
                        >
                            Contact
                        </Nav.Link>
                    </Nav>

                    {/* Right Side Navigation */}
                    <Nav className="align-items-lg-center">
                        {/* Book Now Button - Always Visible */}
                        <Nav.Item className="me-2 mb-2 mb-lg-0">
                            <Button 
                                as={Link} 
                                to="/book" 
                                variant="primary"
                                size="sm"
                                onClick={closeNavbar}
                                className="fw-semibold"
                            >
                                <i className="bi bi-calendar-check me-1"></i>
                                Book Now
                            </Button>
                        </Nav.Item>

                        {/* Authentication Links */}
                        {!isAuthenticated() ? (
                            <>
                                <Nav.Link 
                                    as={Link} 
                                    to="/login" 
                                    onClick={closeNavbar}
                                    className="me-2"
                                >
                                    Login
                                </Nav.Link>
                                <Nav.Item>
                                    <Button 
                                        as={Link} 
                                        to="/register" 
                                        variant="outline-primary"
                                        size="sm"
                                        onClick={closeNavbar}
                                    >
                                        Sign Up
                                    </Button>
                                </Nav.Item>
                            </>
                        ) : (
                            <>
                                {/* User Dropdown */}
                                <Dropdown align="end">
                                    <Dropdown.Toggle 
                                        variant="outline-secondary" 
                                        id="user-dropdown"
                                        className="d-flex align-items-center"
                                    >
                                        <i className="bi bi-person-circle me-1"></i>
                                        {user.firstName}
                                        {!user.emailVerified && (
                                            <Badge bg="warning" className="ms-1">
                                                !
                                            </Badge>
                                        )}
                                    </Dropdown.Toggle>

                                    <Dropdown.Menu>
                                        <Dropdown.Header>
                                            {user.firstName} {user.lastName}
                                            <br />
                                            <small className="text-muted">{user.email}</small>
                                        </Dropdown.Header>
                                        
                                        <Dropdown.Divider />
                                        
                                        <Dropdown.Item 
                                            as={Link} 
                                            to="/portal"
                                            onClick={closeNavbar}
                                        >
                                            <i className="bi bi-person me-2"></i>
                                            My Account
                                        </Dropdown.Item>
                                        
                                        <Dropdown.Item 
                                            as={Link} 
                                            to="/portal/bookings"
                                            onClick={closeNavbar}
                                        >
                                            <i className="bi bi-calendar-event me-2"></i>
                                            My Bookings
                                        </Dropdown.Item>

                                        {isAdmin() && (
                                            <>
                                                <Dropdown.Divider />
                                                <Dropdown.Item 
                                                    as={Link} 
                                                    to="/admin"
                                                    onClick={closeNavbar}
                                                >
                                                    <i className="bi bi-gear me-2"></i>
                                                    Admin Dashboard
                                                </Dropdown.Item>
                                            </>
                                        )}
                                        
                                        <Dropdown.Divider />
                                        
                                        <Dropdown.Item onClick={handleLogout}>
                                            <i className="bi bi-box-arrow-right me-2"></i>
                                            Logout
                                        </Dropdown.Item>
                                    </Dropdown.Menu>
                                </Dropdown>
                            </>
                        )}
                    </Nav>
                </Navbar.Collapse>
            </Container>
        </Navbar>
    );
};

export default Navigation;
