/**
 * LoadingSpinner Component
 * Reusable loading spinner with different sizes and full-screen option
 */

import React from 'react';

const LoadingSpinner = ({ 
    size = 'medium', 
    fullScreen = false, 
    message = 'Loading...',
    className = '' 
}) => {
    const spinnerSizes = {
        small: 'loading-spinner',
        medium: 'loading-spinner',
        large: 'loading-spinner large'
    };

    const containerClasses = fullScreen 
        ? 'loading-container fullscreen' 
        : 'loading-container';

    return (
        <div className={`${containerClasses} ${className}`}>
            <div className="text-center">
                <div className={spinnerSizes[size]}></div>
                {message && (
                    <p className="mt-3 text-muted">{message}</p>
                )}
            </div>
        </div>
    );
};

export default LoadingSpinner;
