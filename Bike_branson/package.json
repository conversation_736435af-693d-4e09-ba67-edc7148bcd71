{"name": "bike-branson-rental-system", "version": "1.0.0", "description": "Complete bike rental management system for Bike Branson - e-bike rentals in Branson, Missouri", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/server.js", "client": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install-all": "npm install && cd client && npm install", "deploy": "npm run build && node deployment/deploy.js"}, "keywords": ["bike-rental", "e-bike", "booking-system", "branson", "missouri", "rental-management"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "stripe": "^14.9.0", "googleapis": "^129.0.0", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "uuid": "^9.0.1", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-bootstrap": "^2.9.1", "bootstrap": "^5.3.2", "react-calendar": "^4.6.0", "react-datepicker": "^4.24.0", "react-select": "^5.8.0", "react-toastify": "^9.1.3", "react-loading-skeleton": "^3.3.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "@stripe/stripe-js": "^2.2.0", "@stripe/react-stripe-js": "^2.4.0"}, "devDependencies": {"react-scripts": "5.0.1", "nodemon": "^3.0.2", "concurrently": "^8.2.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "web-vitals": "^2.1.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/bikebranson/rental-system.git"}, "bugs": {"url": "https://github.com/bikebranson/rental-system/issues"}, "homepage": "https://bikebranson.com"}