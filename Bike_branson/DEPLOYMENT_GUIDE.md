# Bike Branson - Hostinger Deployment Guide

This guide provides step-by-step instructions for deploying the Bike Branson rental system to Hostinger hosting.

## 📋 Prerequisites

- Hostinger hosting account with:
  - PHP support (for database management)
  - MySQL database access
  - File manager or FTP access
  - Email accounts
- Domain name configured
- Stripe account for payments
- Google account for calendar integration

## 🗄️ Step 1: Database Setup

### 1.1 Create MySQL Database

1. Log into your Hostinger control panel (hPanel)
2. Navigate to **Databases** → **MySQL Databases**
3. Click **Create Database**
4. Database name: `u1234567890_website_db`
5. Create a database user with full privileges
6. Note down the database credentials:
   - Host: usually `localhost`
   - Database name: `u1234567890_name_of_database` (Hostinger format)
   - Username: `u1234567890_username`
   - Password: [your chosen password]

### 1.2 Import Database Schema

1. In hPanel, go to **Databases** → **phpMyAdmin**
2. Select your database
3. Click **Import** tab
4. Upload the `database/schema.sql` file
5. Click **Go** to execute

## 📁 Step 2: File Upload and Configuration

### 2.1 Prepare Files for Upload

On your local machine:

```bash
# Build the React application
npm run build

# Create a deployment package
mkdir hostinger-deployment
cp -r build/* hostinger-deployment/
cp -r server hostinger-deployment/
cp package.json hostinger-deployment/
cp .env.example hostinger-deployment/
```

### 2.2 Upload Files to Hostinger

**Option A: File Manager**
1. In hPanel, go to **Files** → **File Manager**
2. Navigate to `public_html` directory
3. Upload all files from `hostinger-deployment` folder
4. Extract if uploaded as zip

**Option B: FTP**
```bash
# Using FTP client (FileZilla, etc.)
# Connect to your domain with FTP credentials
# Upload files to public_html directory
```

### 2.3 Set File Permissions

In File Manager, set permissions:
- Files: 644
- Directories: 755
- `uploads/` directory: 755 (writable)

## ⚙️ Step 3: Environment Configuration

### 3.1 Create Production Environment File

In File Manager, create `.env` file in `public_html`:

```env
# Production Environment Variables
NODE_ENV=production
PORT=3001

# Database Configuration (update with your Hostinger credentials)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=u1234567890_website_db
DB_USER=u1234567890_username
DB_PASSWORD=your_database_password

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random-64-chars
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Site URLs (update with your domain)
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://yourdomain.com/api

# Stripe Configuration (use live keys for production)
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration (Hostinger SMTP)
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Bike Branson

# Google Calendar Integration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/google/callback
GOOGLE_CALENDAR_ID=<EMAIL>

# Business Configuration
BUSINESS_NAME=Bike Branson
BUSINESS_PHONE=+1 (417) 555-BIKE
BUSINESS_EMAIL=<EMAIL>
BUSINESS_ADDRESS=123 Main St, Branson, MO 65616
BUSINESS_TIMEZONE=America/Chicago
```

## 📧 Step 4: Email Setup

### 4.1 Create Email Accounts

In hPanel, go to **Emails** → **Email Accounts**:

Create these accounts:
- `<EMAIL>` (main contact)
- `<EMAIL>` (admin notifications)
- `<EMAIL>` (automated emails)

### 4.2 Configure SMTP

Update `.env` with Hostinger SMTP settings:
- Host: `smtp.hostinger.com`
- Port: `587`
- Security: `STARTTLS`

## 🔧 Step 5: Apache Configuration

### 5.1 Create .htaccess File

In `public_html`, create `.htaccess`:

```apache
# Bike Branson - Apache Configuration

# Enable mod_rewrite
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

# HTTPS Redirect
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# API Routes (if using PHP backend alternative)
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# React Router - Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>
```

## 🔐 Step 6: SSL Certificate

### 6.1 Enable SSL in Hostinger

1. In hPanel, go to **Security** → **SSL/TLS**
2. Enable **Free SSL Certificate**
3. Wait for activation (usually 5-15 minutes)
4. Verify HTTPS is working

## 🎯 Step 7: API Integration Setup

### 7.1 Stripe Configuration

1. Log into Stripe Dashboard
2. Switch to **Live mode**
3. Get live API keys from **Developers** → **API keys**
4. Set up webhooks:
   - Endpoint URL: `https://yourdomain.com/api/payments/webhook`
   - Events: `payment_intent.succeeded`, `payment_intent.payment_failed`
5. Update `.env` with live keys

### 7.2 Google Calendar Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create project or select existing
3. Enable Google Calendar API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://yourdomain.com/auth/google/callback`
6. Update `.env` with credentials

## 🧪 Step 8: Testing

### 8.1 Basic Functionality Test

1. Visit `https://yourdomain.com`
2. Test navigation and page loading
3. Try user registration/login
4. Test booking system
5. Verify email sending

### 8.2 Payment Testing

1. Use Stripe test cards in development
2. Switch to live mode for production
3. Test small payment amounts
4. Verify webhook delivery

## 📊 Step 9: Monitoring and Maintenance

### 9.1 Set Up Monitoring

1. Enable error logging in `.env`
2. Monitor `logs/` directory
3. Set up Google Analytics
4. Configure uptime monitoring

### 9.2 Regular Maintenance

- Update dependencies monthly
- Monitor database performance
- Review and rotate security keys
- Backup database weekly
- Monitor SSL certificate expiry

## 🆘 Troubleshooting

### Common Issues

**Database Connection Failed**
- Verify database credentials in `.env`
- Check if database exists in phpMyAdmin
- Ensure database user has proper permissions

**Email Not Sending**
- Verify SMTP credentials
- Check email account exists
- Test with simple email client

**SSL Issues**
- Wait for SSL activation
- Clear browser cache
- Check mixed content warnings

**File Permission Errors**
- Set correct permissions (644 for files, 755 for directories)
- Ensure uploads directory is writable

### Support Resources

- Hostinger Knowledge Base
- Hostinger Live Chat Support
- Application logs in `logs/` directory
- Browser developer console for frontend issues

## ✅ Go-Live Checklist

- [ ] Database created and schema imported
- [ ] All files uploaded to public_html
- [ ] Environment variables configured
- [ ] Email accounts created and tested
- [ ] SSL certificate enabled
- [ ] .htaccess file configured
- [ ] Stripe live keys configured
- [ ] Google Calendar integration tested
- [ ] Admin user created and tested
- [ ] Booking system tested
- [ ] Payment processing tested
- [ ] Email notifications tested
- [ ] Mobile responsiveness verified
- [ ] SEO meta tags configured
- [ ] Analytics tracking enabled
- [ ] Backup strategy implemented

## 🎉 Post-Deployment

1. **Update DNS** if needed
2. **Submit sitemap** to Google Search Console
3. **Set up Google My Business** listing
4. **Configure social media** links
5. **Train staff** on admin dashboard
6. **Create user documentation**
7. **Plan marketing launch**

---

Your Bike Branson rental system is now live! 🚴‍♂️

For ongoing support and updates, refer to the main README.md file.
