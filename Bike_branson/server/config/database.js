/**
 * Database Configuration for Bike Branson Rental System
 * MySQL connection with connection pooling and error handling
 */

const mysql = require('mysql2/promise');
const { logger } = require('../utils/logger');

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4',
    timezone: 'Z', // Use UTC for all database operations
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 10,
    queueLimit: 0,
    // Enable multiple statements for complex queries
    multipleStatements: false,
    // SSL configuration for production
    ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false
    } : false
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

/**
 * Database utility class
 */
class Database {
    constructor() {
        this.pool = pool;
    }

    /**
     * Test database connection
     */
    async testConnection() {
        try {
            const connection = await this.pool.getConnection();
            await connection.ping();
            connection.release();
            logger.info('Database connection test successful');
            return true;
        } catch (error) {
            logger.error('Database connection test failed:', error);
            throw error;
        }
    }

    /**
     * Execute a query with parameters
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} Query result
     */
    async query(query, params = []) {
        try {
            const [rows] = await this.pool.execute(query, params);
            return rows;
        } catch (error) {
            logger.error('Database query error:', {
                query: query.substring(0, 100) + '...',
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Execute a query and return the first row
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} First row or null
     */
    async queryOne(query, params = []) {
        const rows = await this.query(query, params);
        return rows.length > 0 ? rows[0] : null;
    }

    /**
     * Begin a database transaction
     * @returns {Promise} Connection object
     */
    async beginTransaction() {
        const connection = await this.pool.getConnection();
        await connection.beginTransaction();
        return connection;
    }

    /**
     * Commit a transaction
     * @param {Object} connection - Database connection
     */
    async commitTransaction(connection) {
        await connection.commit();
        connection.release();
    }

    /**
     * Rollback a transaction
     * @param {Object} connection - Database connection
     */
    async rollbackTransaction(connection) {
        await connection.rollback();
        connection.release();
    }

    /**
     * Execute query within a transaction
     * @param {Object} connection - Database connection
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} Query result
     */
    async transactionQuery(connection, query, params = []) {
        try {
            const [rows] = await connection.execute(query, params);
            return rows;
        } catch (error) {
            logger.error('Transaction query error:', {
                query: query.substring(0, 100) + '...',
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get database statistics
     */
    async getStats() {
        try {
            const stats = await this.query(`
                SELECT 
                    (SELECT COUNT(*) FROM bookings) as total_bookings,
                    (SELECT COUNT(*) FROM bookings WHERE booking_status = 'confirmed') as confirmed_bookings,
                    (SELECT COUNT(*) FROM bikes WHERE is_active = 1) as active_bikes,
                    (SELECT COUNT(*) FROM users WHERE user_type = 'customer') as total_customers,
                    (SELECT SUM(total_amount) FROM bookings WHERE payment_status = 'paid_full') as total_revenue
            `);
            return stats[0];
        } catch (error) {
            logger.error('Error getting database stats:', error);
            throw error;
        }
    }

    /**
     * Check if a table exists
     * @param {string} tableName - Name of the table
     * @returns {Promise<boolean>} True if table exists
     */
    async tableExists(tableName) {
        try {
            const result = await this.query(`
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = ? AND table_name = ?
            `, [process.env.DB_NAME, tableName]);
            
            return result[0].count > 0;
        } catch (error) {
            logger.error(`Error checking if table ${tableName} exists:`, error);
            return false;
        }
    }

    /**
     * Close all database connections
     */
    async close() {
        try {
            await this.pool.end();
            logger.info('Database connections closed');
        } catch (error) {
            logger.error('Error closing database connections:', error);
        }
    }
}

// Create and export database instance
const database = new Database();

// Handle pool errors
pool.on('error', (error) => {
    logger.error('Database pool error:', error);
    if (error.code === 'PROTOCOL_CONNECTION_LOST') {
        logger.info('Attempting to reconnect to database...');
    }
});

// Export database instance and pool
module.exports = {
    database,
    pool,
    testConnection: () => database.testConnection(),
    query: (query, params) => database.query(query, params),
    queryOne: (query, params) => database.queryOne(query, params),
    beginTransaction: () => database.beginTransaction(),
    commitTransaction: (connection) => database.commitTransaction(connection),
    rollbackTransaction: (connection) => database.rollbackTransaction(connection),
    transactionQuery: (connection, query, params) => database.transactionQuery(connection, query, params),
    getStats: () => database.getStats(),
    tableExists: (tableName) => database.tableExists(tableName),
    close: () => database.close()
};
