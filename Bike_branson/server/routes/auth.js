/**
 * Authentication routes for Bike Branson Rental System
 * Handles user registration, login, password reset, and JWT token management
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const crypto = require('crypto');
const { database } = require('../config/database');
const { asyncHandler, AppError, sendSuccess, sendError, formatValidationErrors } = require('../middleware/errorMiddleware');
const { sendEmail } = require('../utils/email');
const { logger } = require('../utils/logger');

const router = express.Router();

/**
 * Generate JWT token
 */
const generateToken = (userId) => {
    return jwt.sign({ userId }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });
};

/**
 * Hash password
 */
const hashPassword = async (password) => {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    return await bcrypt.hash(password, saltRounds);
};

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return sendError(res, 'Validation failed', 400, formatValidationErrors(errors));
    }

    const { email, password, firstName, lastName, phone } = req.body;

    // Check if user already exists
    const existingUser = await database.queryOne(
        'SELECT id FROM users WHERE email = ?',
        [email]
    );

    if (existingUser) {
        return sendError(res, 'User with this email already exists', 400);
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Generate email verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');

    // Create user
    const result = await database.query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone, verification_token, user_type) 
         VALUES (?, ?, ?, ?, ?, ?, 'customer')`,
        [email, hashedPassword, firstName, lastName, phone, verificationToken]
    );

    const userId = result.insertId;

    // Send verification email
    try {
        await sendEmail({
            to: email,
            subject: 'Welcome to Bike Branson - Verify Your Email',
            template: 'email-verification',
            data: {
                firstName,
                verificationToken,
                verificationUrl: `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`
            }
        });
    } catch (emailError) {
        logger.error('Failed to send verification email:', emailError);
        // Don't fail registration if email fails
    }

    // Generate JWT token
    const token = generateToken(userId);

    logger.info('User registered successfully', { userId, email });

    sendSuccess(res, {
        user: {
            id: userId,
            email,
            firstName,
            lastName,
            phone,
            userType: 'customer',
            emailVerified: false
        },
        token
    }, 'Registration successful. Please check your email to verify your account.', 201);
}));

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .notEmpty()
        .withMessage('Password is required')
], asyncHandler(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return sendError(res, 'Validation failed', 400, formatValidationErrors(errors));
    }

    const { email, password } = req.body;

    // Find user by email
    const user = await database.queryOne(
        `SELECT id, email, password_hash, first_name, last_name, phone, user_type, 
                email_verified, is_active 
         FROM users WHERE email = ?`,
        [email]
    );

    if (!user) {
        return sendError(res, 'Invalid email or password', 401);
    }

    // Check if user is active
    if (!user.is_active) {
        return sendError(res, 'Your account has been deactivated. Please contact support.', 401);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
        return sendError(res, 'Invalid email or password', 401);
    }

    // Generate JWT token
    const token = generateToken(user.id);

    logger.info('User logged in successfully', { userId: user.id, email });

    sendSuccess(res, {
        user: {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            phone: user.phone,
            userType: user.user_type,
            emailVerified: user.email_verified
        },
        token
    }, 'Login successful');
}));

/**
 * @route   POST /api/auth/verify-email
 * @desc    Verify user email
 * @access  Public
 */
router.post('/verify-email', [
    body('token')
        .notEmpty()
        .withMessage('Verification token is required')
], asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return sendError(res, 'Validation failed', 400, formatValidationErrors(errors));
    }

    const { token } = req.body;

    // Find user by verification token
    const user = await database.queryOne(
        'SELECT id, email, first_name FROM users WHERE verification_token = ?',
        [token]
    );

    if (!user) {
        return sendError(res, 'Invalid or expired verification token', 400);
    }

    // Update user as verified
    await database.query(
        'UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?',
        [user.id]
    );

    logger.info('Email verified successfully', { userId: user.id, email: user.email });

    sendSuccess(res, null, 'Email verified successfully');
}));

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Send password reset email
 * @access  Public
 */
router.post('/forgot-password', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
], asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return sendError(res, 'Validation failed', 400, formatValidationErrors(errors));
    }

    const { email } = req.body;

    // Find user by email
    const user = await database.queryOne(
        'SELECT id, email, first_name FROM users WHERE email = ?',
        [email]
    );

    if (!user) {
        // Don't reveal if email exists or not
        return sendSuccess(res, null, 'If an account with that email exists, a password reset link has been sent.');
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    // Save reset token
    await database.query(
        'UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?',
        [resetToken, resetTokenExpires, user.id]
    );

    // Send reset email
    try {
        await sendEmail({
            to: email,
            subject: 'Bike Branson - Password Reset Request',
            template: 'password-reset',
            data: {
                firstName: user.first_name,
                resetToken,
                resetUrl: `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`
            }
        });
    } catch (emailError) {
        logger.error('Failed to send password reset email:', emailError);
        return sendError(res, 'Failed to send password reset email. Please try again.', 500);
    }

    logger.info('Password reset email sent', { userId: user.id, email });

    sendSuccess(res, null, 'If an account with that email exists, a password reset link has been sent.');
}));

module.exports = router;
