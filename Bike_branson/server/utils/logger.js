/**
 * Logger utility for Bike Branson Rental System
 * Provides structured logging with different levels and formats
 */

const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Logger class with multiple log levels and file output
 */
class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.logLevels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
    }

    /**
     * Format log message with timestamp and level
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {Object} meta - Additional metadata
     * @returns {string} Formatted log message
     */
    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const metaString = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaString}`;
    }

    /**
     * Write log to file
     * @param {string} level - Log level
     * @param {string} message - Formatted message
     */
    writeToFile(level, message) {
        if (process.env.NODE_ENV === 'production') {
            const logFile = path.join(logsDir, `${level}.log`);
            const allLogFile = path.join(logsDir, 'all.log');
            
            // Write to level-specific file
            fs.appendFileSync(logFile, message + '\n');
            
            // Write to all.log file
            fs.appendFileSync(allLogFile, message + '\n');
        }
    }

    /**
     * Check if log level should be output
     * @param {string} level - Log level to check
     * @returns {boolean} True if should log
     */
    shouldLog(level) {
        return this.logLevels[level] <= this.logLevels[this.logLevel];
    }

    /**
     * Log error message
     * @param {string} message - Error message
     * @param {Object|Error} meta - Error object or metadata
     */
    error(message, meta = {}) {
        if (!this.shouldLog('error')) return;

        // Handle Error objects
        if (meta instanceof Error) {
            meta = {
                name: meta.name,
                message: meta.message,
                stack: meta.stack
            };
        }

        const formattedMessage = this.formatMessage('error', message, meta);
        console.error('\x1b[31m%s\x1b[0m', formattedMessage); // Red color
        this.writeToFile('error', formattedMessage);
    }

    /**
     * Log warning message
     * @param {string} message - Warning message
     * @param {Object} meta - Additional metadata
     */
    warn(message, meta = {}) {
        if (!this.shouldLog('warn')) return;

        const formattedMessage = this.formatMessage('warn', message, meta);
        console.warn('\x1b[33m%s\x1b[0m', formattedMessage); // Yellow color
        this.writeToFile('warn', formattedMessage);
    }

    /**
     * Log info message
     * @param {string} message - Info message
     * @param {Object} meta - Additional metadata
     */
    info(message, meta = {}) {
        if (!this.shouldLog('info')) return;

        const formattedMessage = this.formatMessage('info', message, meta);
        console.log('\x1b[36m%s\x1b[0m', formattedMessage); // Cyan color
        this.writeToFile('info', formattedMessage);
    }

    /**
     * Log debug message
     * @param {string} message - Debug message
     * @param {Object} meta - Additional metadata
     */
    debug(message, meta = {}) {
        if (!this.shouldLog('debug')) return;

        const formattedMessage = this.formatMessage('debug', message, meta);
        console.log('\x1b[90m%s\x1b[0m', formattedMessage); // Gray color
        this.writeToFile('debug', formattedMessage);
    }

    /**
     * Log HTTP request
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {number} duration - Request duration in ms
     */
    logRequest(req, res, duration) {
        const meta = {
            method: req.method,
            url: req.url,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        };

        if (res.statusCode >= 400) {
            this.warn(`HTTP ${res.statusCode} ${req.method} ${req.url}`, meta);
        } else {
            this.info(`HTTP ${res.statusCode} ${req.method} ${req.url}`, meta);
        }
    }

    /**
     * Log booking activity
     * @param {string} action - Booking action (created, updated, cancelled, etc.)
     * @param {Object} booking - Booking object
     * @param {Object} user - User object
     */
    logBooking(action, booking, user = null) {
        const meta = {
            action,
            bookingId: booking.id,
            bookingNumber: booking.booking_number,
            customerEmail: booking.customer_email,
            rentalDate: booking.rental_date,
            totalAmount: booking.total_amount,
            userId: user ? user.id : null
        };

        this.info(`Booking ${action}`, meta);
    }

    /**
     * Log payment activity
     * @param {string} action - Payment action
     * @param {Object} payment - Payment object
     * @param {Object} booking - Related booking object
     */
    logPayment(action, payment, booking = null) {
        const meta = {
            action,
            paymentId: payment.id,
            amount: payment.amount,
            paymentStatus: payment.payment_status,
            stripePaymentIntentId: payment.stripe_payment_intent_id,
            bookingId: booking ? booking.id : payment.booking_id
        };

        this.info(`Payment ${action}`, meta);
    }

    /**
     * Clean old log files (keep last 30 days)
     */
    cleanOldLogs() {
        try {
            const files = fs.readdirSync(logsDir);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            files.forEach(file => {
                const filePath = path.join(logsDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime < thirtyDaysAgo) {
                    fs.unlinkSync(filePath);
                    this.info(`Cleaned old log file: ${file}`);
                }
            });
        } catch (error) {
            this.error('Error cleaning old logs:', error);
        }
    }
}

// Create and export logger instance
const logger = new Logger();

// Clean old logs on startup (in production)
if (process.env.NODE_ENV === 'production') {
    logger.cleanOldLogs();
}

module.exports = { logger };
