/**
 * Error handling middleware for Bike Branson Rental System
 * Provides consistent error responses and logging
 */

const { logger } = require('../utils/logger');

/**
 * Custom error class for application-specific errors
 */
class AppError extends Error {
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
        
        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * Handle 404 - Not Found errors
 */
const notFound = (req, res, next) => {
    const error = new AppError(`Not found - ${req.originalUrl}`, 404);
    next(error);
};

/**
 * Global error handler middleware
 */
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log error details
    logger.error('Error occurred:', {
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        params: req.params,
        query: req.query
    });

    // Mongoose bad ObjectId
    if (err.name === 'CastError') {
        const message = 'Resource not found';
        error = new AppError(message, 404);
    }

    // Mongoose duplicate key
    if (err.code === 11000) {
        const message = 'Duplicate field value entered';
        error = new AppError(message, 400);
    }

    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const message = Object.values(err.errors).map(val => val.message);
        error = new AppError(message, 400);
    }

    // MySQL errors
    if (err.code) {
        switch (err.code) {
            case 'ER_DUP_ENTRY':
                error = new AppError('Duplicate entry. This record already exists.', 400);
                break;
            case 'ER_NO_REFERENCED_ROW_2':
                error = new AppError('Referenced record does not exist.', 400);
                break;
            case 'ER_ROW_IS_REFERENCED_2':
                error = new AppError('Cannot delete record. It is referenced by other records.', 400);
                break;
            case 'ER_DATA_TOO_LONG':
                error = new AppError('Data too long for field.', 400);
                break;
            case 'ER_BAD_NULL_ERROR':
                error = new AppError('Required field cannot be null.', 400);
                break;
            case 'ECONNREFUSED':
                error = new AppError('Database connection refused.', 500);
                break;
            case 'PROTOCOL_CONNECTION_LOST':
                error = new AppError('Database connection lost.', 500);
                break;
            default:
                if (err.sqlMessage) {
                    error = new AppError('Database error occurred.', 500);
                }
        }
    }

    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        error = new AppError('Invalid token. Please log in again.', 401);
    }

    if (err.name === 'TokenExpiredError') {
        error = new AppError('Your token has expired. Please log in again.', 401);
    }

    // Stripe errors
    if (err.type && err.type.startsWith('Stripe')) {
        switch (err.type) {
            case 'StripeCardError':
                error = new AppError('Your card was declined.', 400);
                break;
            case 'StripeRateLimitError':
                error = new AppError('Too many requests made to Stripe API.', 429);
                break;
            case 'StripeInvalidRequestError':
                error = new AppError('Invalid payment request.', 400);
                break;
            case 'StripeAPIError':
                error = new AppError('Payment processing error. Please try again.', 500);
                break;
            case 'StripeConnectionError':
                error = new AppError('Network error. Please check your connection.', 500);
                break;
            case 'StripeAuthenticationError':
                error = new AppError('Payment authentication error.', 500);
                break;
            default:
                error = new AppError('Payment processing error.', 500);
        }
    }

    // Multer errors (file upload)
    if (err.code === 'LIMIT_FILE_SIZE') {
        error = new AppError('File too large. Maximum size is 10MB.', 400);
    }

    if (err.code === 'LIMIT_FILE_COUNT') {
        error = new AppError('Too many files. Maximum is 5 files.', 400);
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        error = new AppError('Unexpected file field.', 400);
    }

    // Rate limiting errors
    if (err.status === 429) {
        error = new AppError('Too many requests. Please try again later.', 429);
    }

    // Default error response
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal Server Error';

    // Send error response
    res.status(statusCode).json({
        success: false,
        error: {
            message,
            ...(process.env.NODE_ENV === 'development' && {
                stack: err.stack,
                details: err
            })
        },
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method
    });
};

/**
 * Async error handler wrapper
 * Catches async errors and passes them to error middleware
 */
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * Validation error formatter
 */
const formatValidationErrors = (errors) => {
    return errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value
    }));
};

/**
 * Send success response
 */
const sendSuccess = (res, data = null, message = 'Success', statusCode = 200) => {
    res.status(statusCode).json({
        success: true,
        message,
        data,
        timestamp: new Date().toISOString()
    });
};

/**
 * Send error response
 */
const sendError = (res, message = 'Error occurred', statusCode = 500, errors = null) => {
    res.status(statusCode).json({
        success: false,
        message,
        errors,
        timestamp: new Date().toISOString()
    });
};

module.exports = {
    AppError,
    notFound,
    errorHandler,
    asyncHandler,
    formatValidationErrors,
    sendSuccess,
    sendError
};
