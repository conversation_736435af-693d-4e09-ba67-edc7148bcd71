# Bike Branson Rental System

A comprehensive, full-stack bike rental management system for Bike Branson - an e-bike rental company in Branson, Missouri. This system provides real-time booking, inventory management, payment processing, and admin dashboard functionality.

## 🚴‍♂️ Features

### Customer Features
- **Real-time Booking System** - Calendar-based booking with instant availability checking
- **Trail Information** - Detailed trail descriptions with photos and difficulty levels
- **Bike Inventory Display** - Real-time available bikes with specifications and pricing
- **Multiple Rental Types** - On-site pickup, delivery, and guided tours
- **Secure Payment Processing** - Stripe integration for deposits and full payments
- **Customer Portal** - Booking management, history, and profile updates
- **Digital Waivers** - Electronic signature collection
- **Mobile Responsive** - Optimized for all devices

### Admin Features
- **Comprehensive Dashboard** - Overview of bookings, revenue, and bike availability
- **Booking Management** - View, edit, cancel, and create bookings
- **Inventory Control** - Bike management with maintenance tracking
- **Customer Management** - Customer profiles and booking history
- **Financial Reports** - Revenue tracking and analytics
- **Google Calendar Integration** - Two-way sync with booking calendar
- **Discount Management** - Promo codes and seasonal discounts

### Technical Features
- **Real-time Inventory** - Prevents double-booking with live availability updates
- **Payment Integration** - Stripe for secure payment processing
- **Email Automation** - Booking confirmations, reminders, and notifications
- **Weather Integration** - Current conditions and trail status
- **SEO Optimized** - Search engine friendly for local discovery
- **Security** - JWT authentication, input validation, and data protection

## 🛠️ Technology Stack

### Frontend
- **React.js** - Modern UI framework
- **Bootstrap 5** - Responsive design system
- **React Router** - Client-side routing
- **React Query** - Data fetching and caching
- **Stripe Elements** - Payment form components
- **React Toastify** - Notifications

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **MySQL** - Relational database
- **JWT** - Authentication tokens
- **Stripe API** - Payment processing
- **Google Calendar API** - Calendar integration
- **Nodemailer** - Email sending

### Development Tools
- **Concurrently** - Run frontend and backend simultaneously
- **Nodemon** - Auto-restart development server
- **React Scripts** - Build and development tools

## 📋 Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- Stripe account for payment processing
- Google account for calendar integration
- SMTP email service (Gmail, Hostinger, etc.)

## 🚀 Installation & Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd Bike_branson

# Install dependencies
npm install
```

### 2. Database Setup

```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE bike_branson_rental;
exit

# Import database schema
mysql -u root -p bike_branson_rental < database/schema.sql
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

Required environment variables:
- `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME` - Database credentials
- `JWT_SECRET` - Secret key for JWT tokens
- `STRIPE_PUBLISHABLE_KEY`, `STRIPE_SECRET_KEY` - Stripe API keys
- `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET` - Google API credentials
- `SMTP_HOST`, `SMTP_USER`, `SMTP_PASS` - Email configuration

### 4. Google Calendar Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Calendar API
4. Create credentials (OAuth 2.0 Client ID)
5. Add authorized redirect URIs
6. Download credentials and update `.env`

### 5. Stripe Setup

1. Create [Stripe account](https://stripe.com/)
2. Get API keys from dashboard
3. Set up webhooks for payment events
4. Update `.env` with keys

## 🏃‍♂️ Running the Application

### Development Mode

```bash
# Run both frontend and backend
npm run dev

# Or run separately
npm run server  # Backend only (port 3001)
npm run client  # Frontend only (port 3000)
```

### Production Build

```bash
# Build frontend for production
npm run build

# Start production server
npm start
```

## 🌐 Deployment to Hostinger

### 1. Prepare for Deployment

```bash
# Build the application
npm run build

# Create deployment package
tar -czf bike-branson-app.tar.gz .
```

### 2. Upload to Hostinger

1. Access your Hostinger control panel
2. Go to File Manager
3. Upload and extract the tar.gz file to public_html
4. Set up MySQL database in Hostinger panel
5. Import database schema via phpMyAdmin

### 3. Configure Environment

1. Create `.env` file on server with production values
2. Update database credentials for Hostinger MySQL
3. Set `NODE_ENV=production`
4. Configure SMTP with Hostinger email

### 4. Set Up Node.js (if supported)

```bash
# If Hostinger supports Node.js
npm install --production
npm start
```

### 5. Alternative: Static Hosting

For static hosting, build the React app and serve via Apache:

```bash
# Build static files
npm run build

# Copy build files to public_html
cp -r build/* /path/to/public_html/

# Configure .htaccess for React Router
```

## 📁 Project Structure

```
Bike_branson/
├── public/                 # Static files
├── src/                    # React frontend source
│   ├── components/         # Reusable components
│   ├── pages/             # Page components
│   ├── contexts/          # React contexts
│   ├── services/          # API services
│   └── styles/            # CSS files
├── server/                # Node.js backend
│   ├── config/            # Configuration files
│   ├── routes/            # API routes
│   ├── middleware/        # Express middleware
│   ├── models/            # Database models
│   └── utils/             # Utility functions
├── database/              # Database schema and migrations
├── deployment/            # Deployment scripts
└── docs/                  # Documentation
```

## 🔧 Configuration

### Business Settings

Update business information in `database/schema.sql`:
- Business hours
- Contact information
- Pricing
- Available bikes and trails

### Customization

- **Branding**: Update colors in `src/styles/variables.css`
- **Content**: Modify text in component files
- **Images**: Replace placeholder images in `public/images/`

## 📊 Admin Access

Default admin credentials (change immediately):
- Email: <EMAIL>
- Password: admin123

Access admin dashboard at: `/admin`

## 🔒 Security Features

- JWT token authentication
- Password hashing with bcrypt
- Input validation and sanitization
- SQL injection prevention
- Rate limiting
- CORS protection
- Helmet security headers

## 📈 Monitoring & Analytics

- Application logs in `logs/` directory
- Database query logging
- Payment transaction tracking
- Booking analytics in admin dashboard

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL service is running
   - Verify credentials in `.env`
   - Ensure database exists

2. **Stripe Payments Not Working**
   - Verify API keys are correct
   - Check webhook configuration
   - Ensure HTTPS in production

3. **Google Calendar Sync Issues**
   - Verify OAuth credentials
   - Check API quotas
   - Ensure calendar permissions

### Support

For technical support:
- Check application logs
- Review error messages in browser console
- Verify environment configuration

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

---

**Bike Branson** - The Outdoors, Effortlessly! 🚴‍♂️🌲
