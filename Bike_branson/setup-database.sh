#!/bin/bash

# Bike Branson Database Setup Script
# This script creates the MySQL database and imports the schema

echo "🚴‍♂️ Bike Branson Database Setup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="bike_branson_rental"
DB_USER="bikebranson_user"
DB_SCHEMA="database/schema.sql"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if MySQL is installed and running
echo "Checking MySQL installation..."
if ! command -v mysql &> /dev/null; then
    print_error "MySQL is not installed or not in PATH"
    echo "Please install MySQL first:"
    echo "  - macOS: brew install mysql"
    echo "  - Ubuntu: sudo apt-get install mysql-server"
    echo "  - Windows: Download from https://dev.mysql.com/downloads/"
    exit 1
fi

print_status "MySQL found"

# Check if schema file exists
if [ ! -f "$DB_SCHEMA" ]; then
    print_error "Schema file not found: $DB_SCHEMA"
    echo "Please make sure you're running this script from the project root directory"
    exit 1
fi

print_status "Schema file found"

# Get MySQL root password
echo ""
echo "Please enter your MySQL root password:"
read -s MYSQL_ROOT_PASSWORD

# Test MySQL connection
echo ""
echo "Testing MySQL connection..."
if ! mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" &> /dev/null; then
    print_error "Failed to connect to MySQL with provided credentials"
    exit 1
fi

print_status "MySQL connection successful"

# Get password for new database user
echo ""
echo "Enter a password for the database user '$DB_USER':"
read -s DB_PASSWORD

echo ""
echo "Confirm password:"
read -s DB_PASSWORD_CONFIRM

if [ "$DB_PASSWORD" != "$DB_PASSWORD_CONFIRM" ]; then
    print_error "Passwords do not match"
    exit 1
fi

# Create database and user
echo ""
echo "Creating database and user..."

mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- Create database
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';

-- Grant privileges
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';

-- Refresh privileges
FLUSH PRIVILEGES;

-- Show databases to confirm
SHOW DATABASES LIKE '$DB_NAME';
EOF

if [ $? -eq 0 ]; then
    print_status "Database and user created successfully"
else
    print_error "Failed to create database and user"
    exit 1
fi

# Import schema
echo ""
echo "Importing database schema..."

mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$DB_SCHEMA"

if [ $? -eq 0 ]; then
    print_status "Schema imported successfully"
else
    print_error "Failed to import schema"
    exit 1
fi

# Verify import
echo ""
echo "Verifying database setup..."

TABLE_COUNT=$(mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES;" | wc -l)

if [ $TABLE_COUNT -gt 10 ]; then
    print_status "Database setup completed successfully!"
    echo ""
    echo "📋 Database Information:"
    echo "  Database Name: $DB_NAME"
    echo "  Username: $DB_USER"
    echo "  Host: localhost"
    echo "  Port: 3306"
    echo ""
    echo "🔧 Next Steps:"
    echo "1. Copy .env.example to .env"
    echo "2. Update the database credentials in .env:"
    echo "   DB_HOST=localhost"
    echo "   DB_NAME=$DB_NAME"
    echo "   DB_USER=$DB_USER"
    echo "   DB_PASSWORD=[your_password]"
    echo "3. Run 'npm install' to install dependencies"
    echo "4. Run 'npm run dev' to start the application"
    echo ""
    print_status "Setup complete! 🎉"
else
    print_error "Database setup may have failed - not enough tables found"
    exit 1
fi
