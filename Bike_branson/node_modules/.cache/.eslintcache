[{"/home/<USER>/Documents/vscode/Bike_branson/src/index.js": "1", "/home/<USER>/Documents/vscode/Bike_branson/src/App.js": "2", "/home/<USER>/Documents/vscode/Bike_branson/src/components/Navigation.js": "3", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/HomePage.js": "4", "/home/<USER>/Documents/vscode/Bike_branson/src/contexts/AuthContext.js": "5", "/home/<USER>/Documents/vscode/Bike_branson/src/services/apiService.js": "6", "/home/<USER>/Documents/vscode/Bike_branson/src/components/LoadingSpinner.js": "7", "/home/<USER>/Documents/vscode/Bike_branson/src/components/Footer.js": "8", "/home/<USER>/Documents/vscode/Bike_branson/src/components/WeatherWidget.js": "9", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/BookingPage.js": "10", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/CustomerPortal.js": "11", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/BikeInventoryPage.js": "12", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TrailDetailPage.js": "13", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/AdminDashboard.js": "14", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/LoginPage.js": "15", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TrailsPage.js": "16", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/ContactPage.js": "17", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/RegisterPage.js": "18", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/AboutPage.js": "19", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/PrivacyPage.js": "20", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TermsPage.js": "21", "/home/<USER>/Documents/vscode/Bike_branson/src/pages/NotFoundPage.js": "22", "/home/<USER>/Documents/vscode/Bike_branson/src/contexts/BookingContext.js": "23"}, {"size": 508, "mtime": 1749530049663, "results": "24", "hashOfConfig": "25"}, {"size": 8961, "mtime": 1750209976432, "results": "26", "hashOfConfig": "25"}, {"size": 9556, "mtime": 1749530213220, "results": "27", "hashOfConfig": "25"}, {"size": 22281, "mtime": 1750210122888, "results": "28", "hashOfConfig": "25"}, {"size": 9023, "mtime": 1749530108723, "results": "29", "hashOfConfig": "25"}, {"size": 7801, "mtime": 1749530080925, "results": "30", "hashOfConfig": "25"}, {"size": 890, "mtime": 1749530761078, "results": "31", "hashOfConfig": "25"}, {"size": 6635, "mtime": 1749530784014, "results": "32", "hashOfConfig": "25"}, {"size": 5242, "mtime": 1749530803434, "results": "33", "hashOfConfig": "25"}, {"size": 2106, "mtime": 1749530928144, "results": "34", "hashOfConfig": "25"}, {"size": 2131, "mtime": 1749530948065, "results": "35", "hashOfConfig": "25"}, {"size": 2115, "mtime": 1749530937910, "results": "36", "hashOfConfig": "25"}, {"size": 7868, "mtime": 1749530916563, "results": "37", "hashOfConfig": "25"}, {"size": 2342, "mtime": 1749530958213, "results": "38", "hashOfConfig": "25"}, {"size": 8844, "mtime": 1749530858747, "results": "39", "hashOfConfig": "25"}, {"size": 11807, "mtime": 1749530835280, "results": "40", "hashOfConfig": "25"}, {"size": 4087, "mtime": 1749530973697, "results": "41", "hashOfConfig": "25"}, {"size": 14737, "mtime": 1749530892035, "results": "42", "hashOfConfig": "25"}, {"size": 3352, "mtime": 1749530988008, "results": "43", "hashOfConfig": "25"}, {"size": 2725, "mtime": 1749530998704, "results": "44", "hashOfConfig": "25"}, {"size": 3040, "mtime": 1749531010486, "results": "45", "hashOfConfig": "25"}, {"size": 2042, "mtime": 1749531021345, "results": "46", "hashOfConfig": "25"}, {"size": 9104, "mtime": 1749531049239, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pi028i", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Documents/vscode/Bike_branson/src/index.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/App.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/components/Navigation.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/HomePage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/contexts/AuthContext.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/services/apiService.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/components/LoadingSpinner.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/components/Footer.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/components/WeatherWidget.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/BookingPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/CustomerPortal.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/BikeInventoryPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TrailDetailPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/AdminDashboard.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/LoginPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TrailsPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/ContactPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/RegisterPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/AboutPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/PrivacyPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/TermsPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/pages/NotFoundPage.js", [], [], "/home/<USER>/Documents/vscode/Bike_branson/src/contexts/BookingContext.js", [], []]