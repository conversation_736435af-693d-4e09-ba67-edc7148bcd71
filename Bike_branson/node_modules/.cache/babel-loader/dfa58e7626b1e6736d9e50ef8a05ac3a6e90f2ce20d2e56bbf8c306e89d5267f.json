{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/vscode/Bike_branson/src/pages/HomePage.js\",\n  _s = $RefreshSig$();\n/**\n * HomePage Component for Bike Branson Rental System\n * Landing page with hero section, featured trails, and booking CTAs\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Button, Carousel } from 'react-bootstrap';\nimport { apiService } from '../services/apiService';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport WeatherWidget from '../components/WeatherWidget';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [trails, setTrails] = useState([]);\n  const [bikes, setBikes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [videoLoaded, setVideoLoaded] = useState(false);\n  const [showVideo, setShowVideo] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [trailsResponse, bikesResponse] = await Promise.all([apiService.getTrails(), apiService.getBikes({\n          limit: 3\n        })]);\n        setTrails(trailsResponse.data.slice(0, 3)); // Show top 3 trails\n        setBikes(bikesResponse.data);\n      } catch (error) {\n        console.error('Error fetching homepage data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n\n    // Start video fade-in after initial load\n    const videoTimer = setTimeout(() => {\n      setShowVideo(true);\n    }, 1500); // Start showing video after 1.5 seconds\n\n    return () => clearTimeout(videoTimer);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homepage\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section position-relative text-white overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `hero-background-overlay ${showVideo ? 'fade-out' : ''}`,\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%)',\n          zIndex: 1,\n          transition: 'opacity 2s ease-in-out'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), showVideo && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-background fade-in\",\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          zIndex: 0,\n          opacity: 1,\n          transition: 'opacity 2s ease-in-out'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-none d-md-block\",\n          children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n            width: \"100%\",\n            height: \"100%\",\n            src: \"https://www.youtube.com/embed/0oEysAC1NOo?autoplay=1&mute=1&loop=1&playlist=0oEysAC1NOo&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&start=0&vq=hd1080&enablejsapi=1\",\n            title: \"Bike Branson Background Video\",\n            frameBorder: \"0\",\n            allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n            allowFullScreen: true,\n            style: {\n              position: 'absolute',\n              top: '-10%',\n              left: '-10%',\n              width: '120%',\n              height: '120%',\n              minWidth: '120%',\n              minHeight: '120%',\n              objectFit: 'cover'\n            },\n            onLoad: () => setVideoLoaded(true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-md-none\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/hero-trail-background.jpg\",\n            alt: \"Beautiful Branson trail\",\n            style: {\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              minWidth: '100%',\n              minHeight: '100%',\n              width: 'auto',\n              height: 'auto',\n              transform: 'translate(-50%, -50%)',\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            background: 'rgba(0, 0, 0, 0.4)',\n            zIndex: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        style: {\n          position: 'relative',\n          zIndex: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"align-items-center min-vh-75\",\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            lg: 8,\n            className: \"mx-auto text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hero-content py-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"display-3 fw-bold mb-4 text-shadow\",\n                children: [\"The Outdoors, \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-warning\",\n                  children: \"Effortlessly!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 51\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"lead mb-5 fs-4 text-shadow\",\n                children: \"Explore Branson's most beautiful trails with our premium e-bike fleet. From Table Rock Lake to Dogwood Canyon, discover the natural beauty of Missouri with ease and comfort.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hero-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  as: Link,\n                  to: \"/book\",\n                  variant: \"warning\",\n                  size: \"lg\",\n                  className: \"me-3 mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-calendar-check me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 41\n                  }, this), \"Book Your Adventure\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  as: Link,\n                  to: \"/trails\",\n                  variant: \"outline-light\",\n                  size: \"lg\",\n                  className: \"mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg\",\n                  style: {\n                    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    border: '2px solid rgba(255, 255, 255, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-map me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 41\n                  }, this), \"Explore Trails\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 33\n              }, this), !showVideo && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner large mx-auto mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-light opacity-75\",\n                  children: \"Loading your adventure...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 37\n              }, this), showVideo && videoLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 fade-in\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-light opacity-75\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bi bi-camera-video me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 45\n                  }, this), \"Experience the beauty of Branson's trails\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"weather-section py-3 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(WeatherWidget, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-trails py-5\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            lg: 8,\n            className: \"mx-auto text-center mb-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-5 fw-bold text-primary mb-3\",\n              children: \"Featured Trails\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead text-muted\",\n              children: \"Discover Branson's most popular e-bike destinations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: trails.map(trail => {\n            var _trail$image_gallery;\n            return /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"trail-card h-100 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Img, {\n                  variant: \"top\",\n                  src: ((_trail$image_gallery = trail.image_gallery) === null || _trail$image_gallery === void 0 ? void 0 : _trail$image_gallery[0]) || '/images/trail-placeholder.jpg',\n                  alt: trail.name,\n                  style: {\n                    height: '200px',\n                    objectFit: 'cover'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"d-flex flex-column\",\n                  children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                    className: \"text-primary\",\n                    children: trail.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n                    className: \"text-muted flex-grow-1\",\n                    children: [trail.description.substring(0, 120), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"trail-info mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-geo-alt me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 49\n                      }, this), trail.distance_miles, \" miles \\u2022\", /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-clock ms-2 me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 49\n                      }, this), trail.estimated_duration_hours, \"h \\u2022\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `badge ms-2 ${trail.difficulty_level === 'easy' ? 'bg-success' : trail.difficulty_level === 'moderate' ? 'bg-warning' : 'bg-danger'}`,\n                        children: trail.difficulty_level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      as: Link,\n                      to: `/trails/${trail.slug}`,\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      children: \"Learn More\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      as: Link,\n                      to: `/book?trail=${trail.slug}`,\n                      variant: \"primary\",\n                      size: \"sm\",\n                      children: \"Book Trail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this)\n            }, trail.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 29\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            className: \"text-center mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              as: Link,\n              to: \"/trails\",\n              variant: \"outline-primary\",\n              size: \"lg\",\n              children: \"View All Trails\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"our-fleet py-5 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            lg: 8,\n            className: \"mx-auto text-center mb-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-5 fw-bold text-primary mb-3\",\n              children: \"Our Premium E-Bike Fleet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead text-muted\",\n              children: \"High-quality electric bikes for every adventure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: bikes.map(bike => /*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"bike-card h-100 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(Card.Img, {\n                variant: \"top\",\n                src: bike.image_url || '/images/bike-placeholder.jpg',\n                alt: `${bike.brand} ${bike.model}`,\n                style: {\n                  height: '200px',\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                  className: \"text-primary\",\n                  children: [bike.brand, \" \", bike.model]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n                  className: \"text-muted\",\n                  children: [bike.bike_type.charAt(0).toUpperCase() + bike.bike_type.slice(1), \" E-Bike\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bike-specs mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-battery-charging me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 49\n                    }, this), bike.max_range_miles, \" miles range\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 83\n                    }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-speedometer2 me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 49\n                    }, this), \"Up to 20mph\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pricing\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"h5 text-primary fw-bold\",\n                    children: [\"$\", bike.hourly_rate, \"/hour\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [\"$\", bike.daily_rate, \"/day\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this)\n          }, bike.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            className: \"text-center mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              as: Link,\n              to: \"/bikes\",\n              variant: \"outline-primary\",\n              size: \"lg\",\n              children: \"View All Bikes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"why-choose-us py-5\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            lg: 8,\n            className: \"mx-auto text-center mb-5\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-5 fw-bold text-primary mb-3\",\n              children: \"Why Choose Bike Branson?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            className: \"text-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-bicycle display-4 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"fw-bold\",\n              children: \"Premium E-Bikes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Top-quality electric bikes from trusted brands, regularly maintained for optimal performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            className: \"text-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-geo-alt display-4 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"fw-bold\",\n              children: \"Local Expertise\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"We know Branson's trails inside and out. Get insider tips and recommendations for the best experience.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 4,\n            className: \"text-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-truck display-4 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"fw-bold\",\n              children: \"Delivery Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"We'll bring the bikes to you! Delivery service available to popular trail locations.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"cta-section py-5 bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            lg: 8,\n            className: \"mx-auto text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-5 fw-bold mb-3\",\n              children: \"Ready for Your Adventure?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead mb-4\",\n              children: \"Book your e-bike rental today and experience the outdoors like never before!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              as: Link,\n              to: \"/book\",\n              variant: \"warning\",\n              size: \"lg\",\n              className: \"me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-calendar-check me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 33\n              }, this), \"Book Your Ride\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              as: Link,\n              to: \"/contact\",\n              variant: \"outline-light\",\n              size: \"lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-telephone me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 33\n              }, this), \"Contact Us\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 9\n  }, this);\n};\n_s(HomePage, \"FanPdK52NbLWlx2yoSbg0RtOVmk=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Carousel", "apiService", "LoadingSpinner", "WeatherWidget", "jsxDEV", "_jsxDEV", "HomePage", "_s", "trails", "setTrails", "bikes", "setBikes", "loading", "setLoading", "videoLoaded", "setVideoLoaded", "showVideo", "setShowVideo", "fetchData", "trailsResponse", "bikesResponse", "Promise", "all", "getTrails", "getBikes", "limit", "data", "slice", "error", "console", "videoTimer", "setTimeout", "clearTimeout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "style", "position", "top", "left", "width", "height", "background", "zIndex", "transition", "opacity", "src", "title", "frameBorder", "allow", "allowFullScreen", "min<PERSON><PERSON><PERSON>", "minHeight", "objectFit", "onLoad", "alt", "transform", "lg", "as", "to", "variant", "size", "backgroundColor", "<PERSON><PERSON>ilter", "border", "map", "trail", "_trail$image_gallery", "md", "Img", "image_gallery", "name", "Body", "Title", "Text", "description", "substring", "distance_miles", "estimated_duration_hours", "difficulty_level", "slug", "id", "bike", "image_url", "brand", "model", "bike_type", "char<PERSON>t", "toUpperCase", "max_range_miles", "hourly_rate", "daily_rate", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/vscode/Bike_branson/src/pages/HomePage.js"], "sourcesContent": ["/**\n * HomePage Component for Bike Branson Rental System\n * Landing page with hero section, featured trails, and booking CTAs\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Button, Carousel } from 'react-bootstrap';\nimport { apiService } from '../services/apiService';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport WeatherWidget from '../components/WeatherWidget';\n\nconst HomePage = () => {\n    const [trails, setTrails] = useState([]);\n    const [bikes, setBikes] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [videoLoaded, setVideoLoaded] = useState(false);\n    const [showVideo, setShowVideo] = useState(false);\n\n    useEffect(() => {\n        const fetchData = async () => {\n            try {\n                const [trailsResponse, bikesResponse] = await Promise.all([\n                    apiService.getTrails(),\n                    apiService.getBikes({ limit: 3 })\n                ]);\n\n                setTrails(trailsResponse.data.slice(0, 3)); // Show top 3 trails\n                setBikes(bikesResponse.data);\n            } catch (error) {\n                console.error('Error fetching homepage data:', error);\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchData();\n\n        // Start video fade-in after initial load\n        const videoTimer = setTimeout(() => {\n            setShowVideo(true);\n        }, 1500); // Start showing video after 1.5 seconds\n\n        return () => clearTimeout(videoTimer);\n    }, []);\n\n    if (loading) {\n        return <LoadingSpinner />;\n    }\n\n    return (\n        <div className=\"homepage\">\n            {/* Hero Section with Video Background */}\n            <section className=\"hero-section position-relative text-white overflow-hidden\">\n                {/* Green Background Overlay */}\n                <div\n                    className={`hero-background-overlay ${showVideo ? 'fade-out' : ''}`}\n                    style={{\n                        position: 'absolute',\n                        top: 0,\n                        left: 0,\n                        width: '100%',\n                        height: '100%',\n                        background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%)',\n                        zIndex: 1,\n                        transition: 'opacity 2s ease-in-out'\n                    }}\n                ></div>\n\n                {/* YouTube Video Background */}\n                {showVideo && (\n                    <div\n                        className=\"video-background fade-in\"\n                        style={{\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            width: '100%',\n                            height: '100%',\n                            zIndex: 0,\n                            opacity: 1,\n                            transition: 'opacity 2s ease-in-out'\n                        }}\n                    >\n                        {/* Video for desktop */}\n                        <div className=\"d-none d-md-block\">\n                            <iframe\n                                width=\"100%\"\n                                height=\"100%\"\n                                src=\"https://www.youtube.com/embed/0oEysAC1NOo?autoplay=1&mute=1&loop=1&playlist=0oEysAC1NOo&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&start=0&vq=hd1080&enablejsapi=1\"\n                                title=\"Bike Branson Background Video\"\n                                frameBorder=\"0\"\n                                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                                allowFullScreen\n                                style={{\n                                    position: 'absolute',\n                                    top: '-10%',\n                                    left: '-10%',\n                                    width: '120%',\n                                    height: '120%',\n                                    minWidth: '120%',\n                                    minHeight: '120%',\n                                    objectFit: 'cover'\n                                }}\n                                onLoad={() => setVideoLoaded(true)}\n                            ></iframe>\n                        </div>\n\n                        {/* Fallback image for mobile */}\n                        <div className=\"d-md-none\">\n                            <img\n                                src=\"/images/hero-trail-background.jpg\"\n                                alt=\"Beautiful Branson trail\"\n                                style={{\n                                    position: 'absolute',\n                                    top: '50%',\n                                    left: '50%',\n                                    minWidth: '100%',\n                                    minHeight: '100%',\n                                    width: 'auto',\n                                    height: 'auto',\n                                    transform: 'translate(-50%, -50%)',\n                                    objectFit: 'cover'\n                                }}\n                            />\n                        </div>\n\n                        {/* Dark overlay for text readability */}\n                        <div\n                            style={{\n                                position: 'absolute',\n                                top: 0,\n                                left: 0,\n                                width: '100%',\n                                height: '100%',\n                                background: 'rgba(0, 0, 0, 0.4)',\n                                zIndex: 1\n                            }}\n                        ></div>\n                    </div>\n                )}\n\n                {/* Hero Content */}\n                <Container style={{ position: 'relative', zIndex: 2 }}>\n                    <Row className=\"align-items-center min-vh-75\">\n                        <Col lg={8} className=\"mx-auto text-center\">\n                            <div className=\"hero-content py-5\">\n                                <h1 className=\"display-3 fw-bold mb-4 text-shadow\">\n                                    The Outdoors, <span className=\"text-warning\">Effortlessly!</span>\n                                </h1>\n                                <p className=\"lead mb-5 fs-4 text-shadow\">\n                                    Explore Branson's most beautiful trails with our premium e-bike fleet.\n                                    From Table Rock Lake to Dogwood Canyon, discover the natural beauty\n                                    of Missouri with ease and comfort.\n                                </p>\n                                <div className=\"hero-buttons\">\n                                    <Button\n                                        as={Link}\n                                        to=\"/book\"\n                                        variant=\"warning\"\n                                        size=\"lg\"\n                                        className=\"me-3 mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg\"\n                                    >\n                                        <i className=\"bi bi-calendar-check me-2\"></i>\n                                        Book Your Adventure\n                                    </Button>\n                                    <Button\n                                        as={Link}\n                                        to=\"/trails\"\n                                        variant=\"outline-light\"\n                                        size=\"lg\"\n                                        className=\"mb-3 px-4 py-3 fs-5 fw-semibold shadow-lg\"\n                                        style={{\n                                            backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                                            backdropFilter: 'blur(10px)',\n                                            border: '2px solid rgba(255, 255, 255, 0.3)'\n                                        }}\n                                    >\n                                        <i className=\"bi bi-map me-2\"></i>\n                                        Explore Trails\n                                    </Button>\n                                </div>\n\n                                {/* Loading indicator */}\n                                {!showVideo && (\n                                    <div className=\"mt-4\">\n                                        <div className=\"loading-spinner large mx-auto mb-2\"></div>\n                                        <small className=\"text-light opacity-75\">\n                                            Loading your adventure...\n                                        </small>\n                                    </div>\n                                )}\n\n                                {/* Video Credit */}\n                                {showVideo && videoLoaded && (\n                                    <div className=\"mt-4 fade-in\">\n                                        <small className=\"text-light opacity-75\">\n                                            <i className=\"bi bi-camera-video me-1\"></i>\n                                            Experience the beauty of Branson's trails\n                                        </small>\n                                    </div>\n                                )}\n                            </div>\n                        </Col>\n                    </Row>\n                </Container>\n            </section>\n\n            {/* Weather Widget */}\n            <section className=\"weather-section py-3 bg-light\">\n                <Container>\n                    <WeatherWidget />\n                </Container>\n            </section>\n\n            {/* Featured Trails Section */}\n            <section className=\"featured-trails py-5\">\n                <Container>\n                    <Row>\n                        <Col lg={8} className=\"mx-auto text-center mb-5\">\n                            <h2 className=\"display-5 fw-bold text-primary mb-3\">\n                                Featured Trails\n                            </h2>\n                            <p className=\"lead text-muted\">\n                                Discover Branson's most popular e-bike destinations\n                            </p>\n                        </Col>\n                    </Row>\n                    <Row>\n                        {trails.map((trail) => (\n                            <Col md={4} key={trail.id} className=\"mb-4\">\n                                <Card className=\"trail-card h-100 shadow-sm\">\n                                    <Card.Img \n                                        variant=\"top\" \n                                        src={trail.image_gallery?.[0] || '/images/trail-placeholder.jpg'}\n                                        alt={trail.name}\n                                        style={{ height: '200px', objectFit: 'cover' }}\n                                    />\n                                    <Card.Body className=\"d-flex flex-column\">\n                                        <Card.Title className=\"text-primary\">\n                                            {trail.name}\n                                        </Card.Title>\n                                        <Card.Text className=\"text-muted flex-grow-1\">\n                                            {trail.description.substring(0, 120)}...\n                                        </Card.Text>\n                                        <div className=\"trail-info mb-3\">\n                                            <small className=\"text-muted\">\n                                                <i className=\"bi bi-geo-alt me-1\"></i>\n                                                {trail.distance_miles} miles • \n                                                <i className=\"bi bi-clock ms-2 me-1\"></i>\n                                                {trail.estimated_duration_hours}h • \n                                                <span className={`badge ms-2 ${\n                                                    trail.difficulty_level === 'easy' ? 'bg-success' :\n                                                    trail.difficulty_level === 'moderate' ? 'bg-warning' : 'bg-danger'\n                                                }`}>\n                                                    {trail.difficulty_level}\n                                                </span>\n                                            </small>\n                                        </div>\n                                        <div className=\"d-flex justify-content-between align-items-center\">\n                                            <Button \n                                                as={Link} \n                                                to={`/trails/${trail.slug}`} \n                                                variant=\"outline-primary\"\n                                                size=\"sm\"\n                                            >\n                                                Learn More\n                                            </Button>\n                                            <Button \n                                                as={Link} \n                                                to={`/book?trail=${trail.slug}`} \n                                                variant=\"primary\"\n                                                size=\"sm\"\n                                            >\n                                                Book Trail\n                                            </Button>\n                                        </div>\n                                    </Card.Body>\n                                </Card>\n                            </Col>\n                        ))}\n                    </Row>\n                    <Row>\n                        <Col className=\"text-center mt-4\">\n                            <Button \n                                as={Link} \n                                to=\"/trails\" \n                                variant=\"outline-primary\" \n                                size=\"lg\"\n                            >\n                                View All Trails\n                            </Button>\n                        </Col>\n                    </Row>\n                </Container>\n            </section>\n\n            {/* Our Fleet Section */}\n            <section className=\"our-fleet py-5 bg-light\">\n                <Container>\n                    <Row>\n                        <Col lg={8} className=\"mx-auto text-center mb-5\">\n                            <h2 className=\"display-5 fw-bold text-primary mb-3\">\n                                Our Premium E-Bike Fleet\n                            </h2>\n                            <p className=\"lead text-muted\">\n                                High-quality electric bikes for every adventure\n                            </p>\n                        </Col>\n                    </Row>\n                    <Row>\n                        {bikes.map((bike) => (\n                            <Col md={4} key={bike.id} className=\"mb-4\">\n                                <Card className=\"bike-card h-100 shadow-sm\">\n                                    <Card.Img \n                                        variant=\"top\" \n                                        src={bike.image_url || '/images/bike-placeholder.jpg'}\n                                        alt={`${bike.brand} ${bike.model}`}\n                                        style={{ height: '200px', objectFit: 'cover' }}\n                                    />\n                                    <Card.Body className=\"text-center\">\n                                        <Card.Title className=\"text-primary\">\n                                            {bike.brand} {bike.model}\n                                        </Card.Title>\n                                        <Card.Text className=\"text-muted\">\n                                            {bike.bike_type.charAt(0).toUpperCase() + bike.bike_type.slice(1)} E-Bike\n                                        </Card.Text>\n                                        <div className=\"bike-specs mb-3\">\n                                            <small className=\"text-muted\">\n                                                <i className=\"bi bi-battery-charging me-1\"></i>\n                                                {bike.max_range_miles} miles range<br/>\n                                                <i className=\"bi bi-speedometer2 me-1\"></i>\n                                                Up to 20mph\n                                            </small>\n                                        </div>\n                                        <div className=\"pricing\">\n                                            <span className=\"h5 text-primary fw-bold\">\n                                                ${bike.hourly_rate}/hour\n                                            </span>\n                                            <br/>\n                                            <small className=\"text-muted\">\n                                                ${bike.daily_rate}/day\n                                            </small>\n                                        </div>\n                                    </Card.Body>\n                                </Card>\n                            </Col>\n                        ))}\n                    </Row>\n                    <Row>\n                        <Col className=\"text-center mt-4\">\n                            <Button \n                                as={Link} \n                                to=\"/bikes\" \n                                variant=\"outline-primary\" \n                                size=\"lg\"\n                            >\n                                View All Bikes\n                            </Button>\n                        </Col>\n                    </Row>\n                </Container>\n            </section>\n\n            {/* Why Choose Us Section */}\n            <section className=\"why-choose-us py-5\">\n                <Container>\n                    <Row>\n                        <Col lg={8} className=\"mx-auto text-center mb-5\">\n                            <h2 className=\"display-5 fw-bold text-primary mb-3\">\n                                Why Choose Bike Branson?\n                            </h2>\n                        </Col>\n                    </Row>\n                    <Row>\n                        <Col md={4} className=\"text-center mb-4\">\n                            <div className=\"feature-icon mb-3\">\n                                <i className=\"bi bi-bicycle display-4 text-primary\"></i>\n                            </div>\n                            <h4 className=\"fw-bold\">Premium E-Bikes</h4>\n                            <p className=\"text-muted\">\n                                Top-quality electric bikes from trusted brands, \n                                regularly maintained for optimal performance.\n                            </p>\n                        </Col>\n                        <Col md={4} className=\"text-center mb-4\">\n                            <div className=\"feature-icon mb-3\">\n                                <i className=\"bi bi-geo-alt display-4 text-primary\"></i>\n                            </div>\n                            <h4 className=\"fw-bold\">Local Expertise</h4>\n                            <p className=\"text-muted\">\n                                We know Branson's trails inside and out. Get insider \n                                tips and recommendations for the best experience.\n                            </p>\n                        </Col>\n                        <Col md={4} className=\"text-center mb-4\">\n                            <div className=\"feature-icon mb-3\">\n                                <i className=\"bi bi-truck display-4 text-primary\"></i>\n                            </div>\n                            <h4 className=\"fw-bold\">Delivery Available</h4>\n                            <p className=\"text-muted\">\n                                We'll bring the bikes to you! Delivery service \n                                available to popular trail locations.\n                            </p>\n                        </Col>\n                    </Row>\n                </Container>\n            </section>\n\n            {/* Call to Action Section */}\n            <section className=\"cta-section py-5 bg-primary text-white\">\n                <Container>\n                    <Row>\n                        <Col lg={8} className=\"mx-auto text-center\">\n                            <h2 className=\"display-5 fw-bold mb-3\">\n                                Ready for Your Adventure?\n                            </h2>\n                            <p className=\"lead mb-4\">\n                                Book your e-bike rental today and experience the outdoors like never before!\n                            </p>\n                            <Button \n                                as={Link} \n                                to=\"/book\" \n                                variant=\"warning\" \n                                size=\"lg\"\n                                className=\"me-3\"\n                            >\n                                <i className=\"bi bi-calendar-check me-2\"></i>\n                                Book Your Ride\n                            </Button>\n                            <Button \n                                as={Link} \n                                to=\"/contact\" \n                                variant=\"outline-light\" \n                                size=\"lg\"\n                            >\n                                <i className=\"bi bi-telephone me-2\"></i>\n                                Contact Us\n                            </Button>\n                        </Col>\n                    </Row>\n                </Container>\n            </section>\n        </div>\n    );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,iBAAiB;AAC7E,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACZ,MAAMyB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA,MAAM,CAACC,cAAc,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtDrB,UAAU,CAACsB,SAAS,CAAC,CAAC,EACtBtB,UAAU,CAACuB,QAAQ,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC,CAAC,CACpC,CAAC;QAEFhB,SAAS,CAACU,cAAc,CAACO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5ChB,QAAQ,CAACS,aAAa,CAACM,IAAI,CAAC;MAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QACNf,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAEDK,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMY,UAAU,GAAGC,UAAU,CAAC,MAAM;MAChCd,YAAY,CAAC,IAAI,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMe,YAAY,CAACF,UAAU,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIlB,OAAO,EAAE;IACT,oBAAOP,OAAA,CAACH,cAAc;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,oBACI/B,OAAA;IAAKgC,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAErBjC,OAAA;MAASgC,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAE1EjC,OAAA;QACIgC,SAAS,EAAE,2BAA2BrB,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;QACpEuB,KAAK,EAAE;UACHC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE,4EAA4E;UACxFC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE;QAChB;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGNpB,SAAS,iBACNX,OAAA;QACIgC,SAAS,EAAC,0BAA0B;QACpCE,KAAK,EAAE;UACHC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdE,MAAM,EAAE,CAAC;UACTE,OAAO,EAAE,CAAC;UACVD,UAAU,EAAE;QAChB,CAAE;QAAAT,QAAA,gBAGFjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9BjC,OAAA;YACIsC,KAAK,EAAC,MAAM;YACZC,MAAM,EAAC,MAAM;YACbK,GAAG,EAAC,uLAAuL;YAC3LC,KAAK,EAAC,+BAA+B;YACrCC,WAAW,EAAC,GAAG;YACfC,KAAK,EAAC,0FAA0F;YAChGC,eAAe;YACfd,KAAK,EAAE;cACHC,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,MAAM;cACZC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdU,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,MAAM;cACjBC,SAAS,EAAE;YACf,CAAE;YACFC,MAAM,EAAEA,CAAA,KAAM1C,cAAc,CAAC,IAAI;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGN/B,OAAA;UAAKgC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACtBjC,OAAA;YACI4C,GAAG,EAAC,mCAAmC;YACvCS,GAAG,EAAC,yBAAyB;YAC7BnB,KAAK,EAAE;cACHC,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,KAAK;cACVC,IAAI,EAAE,KAAK;cACXY,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,MAAM;cACjBZ,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACde,SAAS,EAAE,uBAAuB;cAClCH,SAAS,EAAE;YACf;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN/B,OAAA;UACIkC,KAAK,EAAE;YACHC,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,oBAAoB;YAChCC,MAAM,EAAE;UACZ;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACR,eAGD/B,OAAA,CAACV,SAAS;QAAC4C,KAAK,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEM,MAAM,EAAE;QAAE,CAAE;QAAAR,QAAA,eAClDjC,OAAA,CAACT,GAAG;UAACyC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eACzCjC,OAAA,CAACR,GAAG;YAAC+D,EAAE,EAAE,CAAE;YAACvB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eACvCjC,OAAA;cAAKgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BjC,OAAA;gBAAIgC,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,gBACjC,eAAAjC,OAAA;kBAAMgC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACL/B,OAAA;gBAAGgC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAI1C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/B,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjC,OAAA,CAACN,MAAM;kBACH8D,EAAE,EAAEnE,IAAK;kBACToE,EAAE,EAAC,OAAO;kBACVC,OAAO,EAAC,SAAS;kBACjBC,IAAI,EAAC,IAAI;kBACT3B,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,gBAE1DjC,OAAA;oBAAGgC,SAAS,EAAC;kBAA2B;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,uBAEjD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/B,OAAA,CAACN,MAAM;kBACH8D,EAAE,EAAEnE,IAAK;kBACToE,EAAE,EAAC,SAAS;kBACZC,OAAO,EAAC,eAAe;kBACvBC,IAAI,EAAC,IAAI;kBACT3B,SAAS,EAAC,2CAA2C;kBACrDE,KAAK,EAAE;oBACH0B,eAAe,EAAE,0BAA0B;oBAC3CC,cAAc,EAAE,YAAY;oBAC5BC,MAAM,EAAE;kBACZ,CAAE;kBAAA7B,QAAA,gBAEFjC,OAAA;oBAAGgC,SAAS,EAAC;kBAAgB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,kBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EAGL,CAACpB,SAAS,iBACPX,OAAA;gBAAKgC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACjBjC,OAAA;kBAAKgC,SAAS,EAAC;gBAAoC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1D/B,OAAA;kBAAOgC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEzC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACR,EAGApB,SAAS,IAAIF,WAAW,iBACrBT,OAAA;gBAAKgC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACzBjC,OAAA;kBAAOgC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpCjC,OAAA;oBAAGgC,SAAS,EAAC;kBAAyB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,6CAE/C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV/B,OAAA;MAASgC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC9CjC,OAAA,CAACV,SAAS;QAAA2C,QAAA,eACNjC,OAAA,CAACF,aAAa;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV/B,OAAA;MAASgC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACrCjC,OAAA,CAACV,SAAS;QAAA2C,QAAA,gBACNjC,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAAC+D,EAAE,EAAE,CAAE;YAACvB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAC5CjC,OAAA;cAAIgC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/B,OAAA;cAAGgC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/B,OAAA,CAACT,GAAG;UAAA0C,QAAA,EACC9B,MAAM,CAAC4D,GAAG,CAAEC,KAAK;YAAA,IAAAC,oBAAA;YAAA,oBACdjE,OAAA,CAACR,GAAG;cAAC0E,EAAE,EAAE,CAAE;cAAgBlC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACvCjC,OAAA,CAACP,IAAI;gBAACuC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACxCjC,OAAA,CAACP,IAAI,CAAC0E,GAAG;kBACLT,OAAO,EAAC,KAAK;kBACbd,GAAG,EAAE,EAAAqB,oBAAA,GAAAD,KAAK,CAACI,aAAa,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAsB,CAAC,CAAC,KAAI,+BAAgC;kBACjEZ,GAAG,EAAEW,KAAK,CAACK,IAAK;kBAChBnC,KAAK,EAAE;oBAAEK,MAAM,EAAE,OAAO;oBAAEY,SAAS,EAAE;kBAAQ;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACF/B,OAAA,CAACP,IAAI,CAAC6E,IAAI;kBAACtC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACrCjC,OAAA,CAACP,IAAI,CAAC8E,KAAK;oBAACvC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC/B+B,KAAK,CAACK;kBAAI;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACb/B,OAAA,CAACP,IAAI,CAAC+E,IAAI;oBAACxC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GACxC+B,KAAK,CAACS,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACZ/B,OAAA;oBAAKgC,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC5BjC,OAAA;sBAAOgC,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBjC,OAAA;wBAAGgC,SAAS,EAAC;sBAAoB;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACrCiC,KAAK,CAACW,cAAc,EAAC,eACtB,eAAA3E,OAAA;wBAAGgC,SAAS,EAAC;sBAAuB;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACxCiC,KAAK,CAACY,wBAAwB,EAAC,UAChC,eAAA5E,OAAA;wBAAMgC,SAAS,EAAE,cACbgC,KAAK,CAACa,gBAAgB,KAAK,MAAM,GAAG,YAAY,GAChDb,KAAK,CAACa,gBAAgB,KAAK,UAAU,GAAG,YAAY,GAAG,WAAW,EACnE;wBAAA5C,QAAA,EACE+B,KAAK,CAACa;sBAAgB;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACN/B,OAAA;oBAAKgC,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAC9DjC,OAAA,CAACN,MAAM;sBACH8D,EAAE,EAAEnE,IAAK;sBACToE,EAAE,EAAE,WAAWO,KAAK,CAACc,IAAI,EAAG;sBAC5BpB,OAAO,EAAC,iBAAiB;sBACzBC,IAAI,EAAC,IAAI;sBAAA1B,QAAA,EACZ;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/B,OAAA,CAACN,MAAM;sBACH8D,EAAE,EAAEnE,IAAK;sBACToE,EAAE,EAAE,eAAeO,KAAK,CAACc,IAAI,EAAG;sBAChCpB,OAAO,EAAC,SAAS;sBACjBC,IAAI,EAAC,IAAI;sBAAA1B,QAAA,EACZ;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC,GAhDMiC,KAAK,CAACe,EAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDpB,CAAC;UAAA,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN/B,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAACwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BjC,OAAA,CAACN,MAAM;cACH8D,EAAE,EAAEnE,IAAK;cACToE,EAAE,EAAC,SAAS;cACZC,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cAAA1B,QAAA,EACZ;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV/B,OAAA;MAASgC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACxCjC,OAAA,CAACV,SAAS;QAAA2C,QAAA,gBACNjC,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAAC+D,EAAE,EAAE,CAAE;YAACvB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBAC5CjC,OAAA;cAAIgC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/B,OAAA;cAAGgC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/B,OAAA,CAACT,GAAG;UAAA0C,QAAA,EACC5B,KAAK,CAAC0D,GAAG,CAAEiB,IAAI,iBACZhF,OAAA,CAACR,GAAG;YAAC0E,EAAE,EAAE,CAAE;YAAelC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACtCjC,OAAA,CAACP,IAAI;cAACuC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACvCjC,OAAA,CAACP,IAAI,CAAC0E,GAAG;gBACLT,OAAO,EAAC,KAAK;gBACbd,GAAG,EAAEoC,IAAI,CAACC,SAAS,IAAI,8BAA+B;gBACtD5B,GAAG,EAAE,GAAG2B,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,KAAK,EAAG;gBACnCjD,KAAK,EAAE;kBAAEK,MAAM,EAAE,OAAO;kBAAEY,SAAS,EAAE;gBAAQ;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACF/B,OAAA,CAACP,IAAI,CAAC6E,IAAI;gBAACtC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC9BjC,OAAA,CAACP,IAAI,CAAC8E,KAAK;kBAACvC,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC/B+C,IAAI,CAACE,KAAK,EAAC,GAAC,EAACF,IAAI,CAACG,KAAK;gBAAA;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACb/B,OAAA,CAACP,IAAI,CAAC+E,IAAI;kBAACxC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAC5B+C,IAAI,CAACI,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,IAAI,CAACI,SAAS,CAAC9D,KAAK,CAAC,CAAC,CAAC,EAAC,SACtE;gBAAA;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZ/B,OAAA;kBAAKgC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC5BjC,OAAA;oBAAOgC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBjC,OAAA;sBAAGgC,SAAS,EAAC;oBAA6B;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC9CiD,IAAI,CAACO,eAAe,EAAC,cAAY,eAAAvF,OAAA;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvC/B,OAAA;sBAAGgC,SAAS,EAAC;oBAAyB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAE/C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACN/B,OAAA;kBAAKgC,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACpBjC,OAAA;oBAAMgC,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,GACrC,EAAC+C,IAAI,CAACQ,WAAW,EAAC,OACvB;kBAAA;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP/B,OAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL/B,OAAA;oBAAOgC,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,GACzB,EAAC+C,IAAI,CAACS,UAAU,EAAC,MACtB;kBAAA;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAjCMiD,IAAI,CAACD,EAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCnB,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN/B,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAACwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BjC,OAAA,CAACN,MAAM;cACH8D,EAAE,EAAEnE,IAAK;cACToE,EAAE,EAAC,QAAQ;cACXC,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cAAA1B,QAAA,EACZ;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV/B,OAAA;MAASgC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACnCjC,OAAA,CAACV,SAAS;QAAA2C,QAAA,gBACNjC,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAAC+D,EAAE,EAAE,CAAE;YAACvB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eAC5CjC,OAAA;cAAIgC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/B,OAAA,CAACT,GAAG;UAAA0C,QAAA,gBACAjC,OAAA,CAACR,GAAG;YAAC0E,EAAE,EAAE,CAAE;YAAClC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACpCjC,OAAA;cAAKgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAC9BjC,OAAA;gBAAGgC,SAAS,EAAC;cAAsC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACN/B,OAAA;cAAIgC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAe;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C/B,OAAA;cAAGgC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAG1B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA,CAACR,GAAG;YAAC0E,EAAE,EAAE,CAAE;YAAClC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACpCjC,OAAA;cAAKgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAC9BjC,OAAA;gBAAGgC,SAAS,EAAC;cAAsC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACN/B,OAAA;cAAIgC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAe;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C/B,OAAA;cAAGgC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAG1B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/B,OAAA,CAACR,GAAG;YAAC0E,EAAE,EAAE,CAAE;YAAClC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACpCjC,OAAA;cAAKgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAC9BjC,OAAA;gBAAGgC,SAAS,EAAC;cAAoC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN/B,OAAA;cAAIgC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C/B,OAAA;cAAGgC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAG1B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV/B,OAAA;MAASgC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACvDjC,OAAA,CAACV,SAAS;QAAA2C,QAAA,eACNjC,OAAA,CAACT,GAAG;UAAA0C,QAAA,eACAjC,OAAA,CAACR,GAAG;YAAC+D,EAAE,EAAE,CAAE;YAACvB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBACvCjC,OAAA;cAAIgC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAEvC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/B,OAAA;cAAGgC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAEzB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ/B,OAAA,CAACN,MAAM;cACH8D,EAAE,EAAEnE,IAAK;cACToE,EAAE,EAAC,OAAO;cACVC,OAAO,EAAC,SAAS;cACjBC,IAAI,EAAC,IAAI;cACT3B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBjC,OAAA;gBAAGgC,SAAS,EAAC;cAA2B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/B,OAAA,CAACN,MAAM;cACH8D,EAAE,EAAEnE,IAAK;cACToE,EAAE,EAAC,UAAU;cACbC,OAAO,EAAC,eAAe;cACvBC,IAAI,EAAC,IAAI;cAAA1B,QAAA,gBAETjC,OAAA;gBAAGgC,SAAS,EAAC;cAAsB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,cAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd,CAAC;AAAC7B,EAAA,CAjbID,QAAQ;AAAAyF,EAAA,GAARzF,QAAQ;AAmbd,eAAeA,QAAQ;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}