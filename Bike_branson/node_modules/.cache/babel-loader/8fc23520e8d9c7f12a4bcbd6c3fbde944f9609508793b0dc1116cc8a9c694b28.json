{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Documents/vscode/Bike_branson/src/App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\n/**\n * Main App Component for Bike Branson Rental System\n * React Router setup with authentication context and global state management\n */\n\nimport React, { useEffect, useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\nimport { Elements } from '@stripe/react-stripe-js';\nimport { loadStripe } from '@stripe/stripe-js';\n\n// Import components\nimport Navigation from './components/Navigation';\nimport Footer from './components/Footer';\nimport HomePage from './pages/HomePage';\nimport TrailsPage from './pages/TrailsPage';\nimport TrailDetailPage from './pages/TrailDetailPage';\nimport BookingPage from './pages/BookingPage';\nimport BikeInventoryPage from './pages/BikeInventoryPage';\nimport CustomerPortal from './pages/CustomerPortal';\nimport AdminDashboard from './pages/AdminDashboard';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ContactPage from './pages/ContactPage';\nimport AboutPage from './pages/AboutPage';\nimport PrivacyPage from './pages/PrivacyPage';\nimport TermsPage from './pages/TermsPage';\nimport NotFoundPage from './pages/NotFoundPage';\n\n// Import context providers\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { BookingProvider } from './contexts/BookingContext';\n\n// Import utilities\nimport { apiService } from './services/apiService';\nimport LoadingSpinner from './components/LoadingSpinner';\n\n// Import styles\nimport 'react-toastify/dist/ReactToastify.css';\nimport './styles/App.css';\n\n// Initialize Stripe (disabled for preview)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst stripePromise = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY ? loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY) : Promise.resolve(null);\n\n// Initialize React Query client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutes\n    }\n  }\n});\n\n/**\n * Protected Route Component\n * Redirects to login if user is not authenticated\n */\nconst ProtectedRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 16\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 16\n    }, this);\n  }\n  if (adminOnly && user.userType !== 'admin' && user.userType !== 'staff') {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 16\n    }, this);\n  }\n  return children;\n};\n\n/**\n * Public Route Component\n * Redirects authenticated users away from auth pages\n */\n_s(ProtectedRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst PublicRoute = ({\n  children\n}) => {\n  _s2();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 16\n    }, this);\n  }\n  if (user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 16\n    }, this);\n  }\n  return children;\n};\n\n/**\n * Main App Layout Component\n */\n_s2(PublicRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c2 = PublicRoute;\nconst AppLayout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-layout\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * App Routes Component\n */\n_c3 = AppLayout;\nconst AppRoutes = () => {\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/trails\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(TrailsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/trails/:slug\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(TrailDetailPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/bikes\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(BikeInventoryPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/book\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(BookingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/contact\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/about\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/privacy\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(PrivacyPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/terms\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(TermsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/register\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        children: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/portal\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(AppLayout, {\n          children: /*#__PURE__*/_jsxDEV(CustomerPortal, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/admin/*\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        adminOnly: true,\n        children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"*\",\n      element: /*#__PURE__*/_jsxDEV(AppLayout, {\n        children: /*#__PURE__*/_jsxDEV(NotFoundPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * Main App Component\n */\n_c4 = AppRoutes;\nconst App = () => {\n  _s3();\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    // Initialize app\n    const initializeApp = async () => {\n      try {\n        // Check if API is available\n        await apiService.get('/health');\n\n        // Any other initialization logic\n      } catch (error) {\n        console.error('Failed to initialize app:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    initializeApp();\n  }, []);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: stripePromise ? /*#__PURE__*/_jsxDEV(Elements, {\n      stripe: stripePromise,\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(BookingProvider, {\n          children: /*#__PURE__*/_jsxDEV(Router, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"App\",\n              children: [/*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                newestOnTop: false,\n                closeOnClick: true,\n                rtl: false,\n                pauseOnFocusLoss: true,\n                draggable: true,\n                pauseOnHover: true,\n                theme: \"light\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(BookingProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"App\",\n            children: [/*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n              position: \"top-right\",\n              autoClose: 5000,\n              hideProgressBar: false,\n              newestOnTop: false,\n              closeOnClick: true,\n              rtl: false,\n              pauseOnFocusLoss: true,\n              draggable: true,\n              pauseOnHover: true,\n              theme: \"light\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 9\n  }, this);\n};\n_s3(App, \"Yt82d/dvZsn5nYh5sqDQjv+rJ38=\");\n_c5 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"AppLayout\");\n$RefreshReg$(_c4, \"AppRoutes\");\n$RefreshReg$(_c5, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "QueryClient", "QueryClientProvider", "ToastContainer", "Elements", "loadStripe", "Navigation", "Footer", "HomePage", "TrailsPage", "TrailDetailPage", "BookingPage", "BikeInventoryPage", "CustomerPortal", "AdminDashboard", "LoginPage", "RegisterPage", "ContactPage", "AboutPage", "PrivacyPage", "TermsPage", "NotFoundPage", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "BookingProvider", "apiService", "LoadingSpinner", "jsxDEV", "_jsxDEV", "stripePromise", "process", "env", "REACT_APP_STRIPE_PUBLISHABLE_KEY", "Promise", "resolve", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "ProtectedRoute", "children", "adminOnly", "_s", "user", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "userType", "_c", "PublicRoute", "_s2", "_c2", "AppLayout", "className", "_c3", "AppRoutes", "path", "element", "_c4", "App", "_s3", "isLoading", "setIsLoading", "initializeApp", "get", "error", "console", "fullScreen", "client", "stripe", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Documents/vscode/Bike_branson/src/App.js"], "sourcesContent": ["/**\n * Main App Component for Bike Branson Rental System\n * React Router setup with authentication context and global state management\n */\n\nimport React, { useEffect, useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\nimport { Elements } from '@stripe/react-stripe-js';\nimport { loadStripe } from '@stripe/stripe-js';\n\n// Import components\nimport Navigation from './components/Navigation';\nimport Footer from './components/Footer';\nimport HomePage from './pages/HomePage';\nimport TrailsPage from './pages/TrailsPage';\nimport TrailDetailPage from './pages/TrailDetailPage';\nimport BookingPage from './pages/BookingPage';\nimport BikeInventoryPage from './pages/BikeInventoryPage';\nimport CustomerPortal from './pages/CustomerPortal';\nimport AdminDashboard from './pages/AdminDashboard';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport ContactPage from './pages/ContactPage';\nimport AboutPage from './pages/AboutPage';\nimport PrivacyPage from './pages/PrivacyPage';\nimport TermsPage from './pages/TermsPage';\nimport NotFoundPage from './pages/NotFoundPage';\n\n// Import context providers\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { BookingProvider } from './contexts/BookingContext';\n\n// Import utilities\nimport { apiService } from './services/apiService';\nimport LoadingSpinner from './components/LoadingSpinner';\n\n// Import styles\nimport 'react-toastify/dist/ReactToastify.css';\nimport './styles/App.css';\n\n// Initialize Stripe (disabled for preview)\nconst stripePromise = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY\n    ? loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY)\n    : Promise.resolve(null);\n\n// Initialize React Query client\nconst queryClient = new QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 1,\n            refetchOnWindowFocus: false,\n            staleTime: 5 * 60 * 1000, // 5 minutes\n        },\n    },\n});\n\n/**\n * Protected Route Component\n * Redirects to login if user is not authenticated\n */\nconst ProtectedRoute = ({ children, adminOnly = false }) => {\n    const { user, loading } = useAuth();\n\n    if (loading) {\n        return <LoadingSpinner />;\n    }\n\n    if (!user) {\n        return <Navigate to=\"/login\" replace />;\n    }\n\n    if (adminOnly && user.userType !== 'admin' && user.userType !== 'staff') {\n        return <Navigate to=\"/\" replace />;\n    }\n\n    return children;\n};\n\n/**\n * Public Route Component\n * Redirects authenticated users away from auth pages\n */\nconst PublicRoute = ({ children }) => {\n    const { user, loading } = useAuth();\n\n    if (loading) {\n        return <LoadingSpinner />;\n    }\n\n    if (user) {\n        return <Navigate to=\"/\" replace />;\n    }\n\n    return children;\n};\n\n/**\n * Main App Layout Component\n */\nconst AppLayout = ({ children }) => {\n    return (\n        <div className=\"app-layout\">\n            <Navigation />\n            <main className=\"main-content\">\n                {children}\n            </main>\n            <Footer />\n        </div>\n    );\n};\n\n/**\n * App Routes Component\n */\nconst AppRoutes = () => {\n    return (\n        <Routes>\n            {/* Public Routes */}\n            <Route path=\"/\" element={\n                <AppLayout>\n                    <HomePage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/trails\" element={\n                <AppLayout>\n                    <TrailsPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/trails/:slug\" element={\n                <AppLayout>\n                    <TrailDetailPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/bikes\" element={\n                <AppLayout>\n                    <BikeInventoryPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/book\" element={\n                <AppLayout>\n                    <BookingPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/contact\" element={\n                <AppLayout>\n                    <ContactPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/about\" element={\n                <AppLayout>\n                    <AboutPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/privacy\" element={\n                <AppLayout>\n                    <PrivacyPage />\n                </AppLayout>\n            } />\n            \n            <Route path=\"/terms\" element={\n                <AppLayout>\n                    <TermsPage />\n                </AppLayout>\n            } />\n\n            {/* Authentication Routes */}\n            <Route path=\"/login\" element={\n                <PublicRoute>\n                    <LoginPage />\n                </PublicRoute>\n            } />\n            \n            <Route path=\"/register\" element={\n                <PublicRoute>\n                    <RegisterPage />\n                </PublicRoute>\n            } />\n\n            {/* Protected Customer Routes */}\n            <Route path=\"/portal\" element={\n                <ProtectedRoute>\n                    <AppLayout>\n                        <CustomerPortal />\n                    </AppLayout>\n                </ProtectedRoute>\n            } />\n\n            {/* Protected Admin Routes */}\n            <Route path=\"/admin/*\" element={\n                <ProtectedRoute adminOnly={true}>\n                    <AdminDashboard />\n                </ProtectedRoute>\n            } />\n\n            {/* 404 Route */}\n            <Route path=\"*\" element={\n                <AppLayout>\n                    <NotFoundPage />\n                </AppLayout>\n            } />\n        </Routes>\n    );\n};\n\n/**\n * Main App Component\n */\nconst App = () => {\n    const [isLoading, setIsLoading] = useState(true);\n\n    useEffect(() => {\n        // Initialize app\n        const initializeApp = async () => {\n            try {\n                // Check if API is available\n                await apiService.get('/health');\n                \n                // Any other initialization logic\n                \n            } catch (error) {\n                console.error('Failed to initialize app:', error);\n            } finally {\n                setIsLoading(false);\n            }\n        };\n\n        initializeApp();\n    }, []);\n\n    if (isLoading) {\n        return <LoadingSpinner fullScreen />;\n    }\n\n    return (\n        <QueryClientProvider client={queryClient}>\n            {stripePromise ? (\n                <Elements stripe={stripePromise}>\n                    <AuthProvider>\n                        <BookingProvider>\n                            <Router>\n                                <div className=\"App\">\n                                    <AppRoutes />\n\n                                    {/* Toast Notifications */}\n                                    <ToastContainer\n                                        position=\"top-right\"\n                                        autoClose={5000}\n                                        hideProgressBar={false}\n                                        newestOnTop={false}\n                                        closeOnClick\n                                        rtl={false}\n                                        pauseOnFocusLoss\n                                        draggable\n                                        pauseOnHover\n                                        theme=\"light\"\n                                    />\n                                </div>\n                            </Router>\n                        </BookingProvider>\n                    </AuthProvider>\n                </Elements>\n            ) : (\n                <AuthProvider>\n                    <BookingProvider>\n                        <Router>\n                            <div className=\"App\">\n                                <AppRoutes />\n\n                                {/* Toast Notifications */}\n                                <ToastContainer\n                                    position=\"top-right\"\n                                    autoClose={5000}\n                                    hideProgressBar={false}\n                                    newestOnTop={false}\n                                    closeOnClick\n                                    rtl={false}\n                                    pauseOnFocusLoss\n                                    draggable\n                                    pauseOnHover\n                                    theme=\"light\"\n                                />\n                            </div>\n                        </Router>\n                    </BookingProvider>\n                </AuthProvider>\n            )}\n        </QueryClientProvider>\n    );\n};\n\nexport default App;\n"], "mappings": ";;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;;AAE/C;AACA,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,eAAe,QAAQ,2BAA2B;;AAE3D;AACA,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AACA,OAAO,uCAAuC;AAC9C,OAAO,kBAAkB;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,gCAAgC,GAC5D3B,UAAU,CAACyB,OAAO,CAACC,GAAG,CAACC,gCAAgC,CAAC,GACxDC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;;AAE3B;AACA,MAAMC,WAAW,GAAG,IAAIlC,WAAW,CAAC;EAChCmC,cAAc,EAAE;IACZC,OAAO,EAAE;MACLC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC9B;EACJ;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAEnC,IAAIuB,OAAO,EAAE;IACT,oBAAOlB,OAAA,CAACF,cAAc;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,IAAI,CAACL,IAAI,EAAE;IACP,oBAAOjB,OAAA,CAAC5B,QAAQ;MAACmD,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3C;EAEA,IAAIP,SAAS,IAAIE,IAAI,CAACQ,QAAQ,KAAK,OAAO,IAAIR,IAAI,CAACQ,QAAQ,KAAK,OAAO,EAAE;IACrE,oBAAOzB,OAAA,CAAC5B,QAAQ;MAACmD,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,OAAOR,QAAQ;AACnB,CAAC;;AAED;AACA;AACA;AACA;AAHAE,EAAA,CAlBMH,cAAc;EAAA,QACUlB,OAAO;AAAA;AAAA+B,EAAA,GAD/Bb,cAAc;AAsBpB,MAAMc,WAAW,GAAGA,CAAC;EAAEb;AAAS,CAAC,KAAK;EAAAc,GAAA;EAClC,MAAM;IAAEX,IAAI;IAAEC;EAAQ,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAEnC,IAAIuB,OAAO,EAAE;IACT,oBAAOlB,OAAA,CAACF,cAAc;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,IAAIL,IAAI,EAAE;IACN,oBAAOjB,OAAA,CAAC5B,QAAQ;MAACmD,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,OAAOR,QAAQ;AACnB,CAAC;;AAED;AACA;AACA;AAFAc,GAAA,CAdMD,WAAW;EAAA,QACahC,OAAO;AAAA;AAAAkC,GAAA,GAD/BF,WAAW;AAiBjB,MAAMG,SAAS,GAAGA,CAAC;EAAEhB;AAAS,CAAC,KAAK;EAChC,oBACId,OAAA;IAAK+B,SAAS,EAAC,YAAY;IAAAjB,QAAA,gBACvBd,OAAA,CAACtB,UAAU;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdtB,OAAA;MAAM+B,SAAS,EAAC,cAAc;MAAAjB,QAAA,EACzBA;IAAQ;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eACPtB,OAAA,CAACrB,MAAM;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AAFAU,GAAA,GAZMF,SAAS;AAef,MAAMG,SAAS,GAAGA,CAAA,KAAM;EACpB,oBACIjC,OAAA,CAAC9B,MAAM;IAAA4C,QAAA,gBAEHd,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,GAAG;MAACC,OAAO,eACnBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACpB,QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,SAAS;MAACC,OAAO,eACzBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACnB,UAAU;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,eAAe;MAACC,OAAO,eAC/BnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAAClB,eAAe;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,QAAQ;MAACC,OAAO,eACxBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAAChB,iBAAiB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,OAAO;MAACC,OAAO,eACvBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACjB,WAAW;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,UAAU;MAACC,OAAO,eAC1BnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACX,WAAW;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,QAAQ;MAACC,OAAO,eACxBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACV,SAAS;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,UAAU;MAACC,OAAO,eAC1BnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACT,WAAW;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,QAAQ;MAACC,OAAO,eACxBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACR,SAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,QAAQ;MAACC,OAAO,eACxBnC,OAAA,CAAC2B,WAAW;QAAAb,QAAA,eACRd,OAAA,CAACb,SAAS;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAChB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,WAAW;MAACC,OAAO,eAC3BnC,OAAA,CAAC2B,WAAW;QAAAb,QAAA,eACRd,OAAA,CAACZ,YAAY;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAChB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,SAAS;MAACC,OAAO,eACzBnC,OAAA,CAACa,cAAc;QAAAC,QAAA,eACXd,OAAA,CAAC8B,SAAS;UAAAhB,QAAA,eACNd,OAAA,CAACf,cAAc;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACnB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,UAAU;MAACC,OAAO,eAC1BnC,OAAA,CAACa,cAAc;QAACE,SAAS,EAAE,IAAK;QAAAD,QAAA,eAC5Bd,OAAA,CAACd,cAAc;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACnB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGJtB,OAAA,CAAC7B,KAAK;MAAC+D,IAAI,EAAC,GAAG;MAACC,OAAO,eACnBnC,OAAA,CAAC8B,SAAS;QAAAhB,QAAA,eACNd,OAAA,CAACP,YAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEjB,CAAC;;AAED;AACA;AACA;AAFAc,GAAA,GAjGMH,SAAS;AAoGf,MAAMI,GAAG,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACd,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAEhDD,SAAS,CAAC,MAAM;IACZ;IACA,MAAM2E,aAAa,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACA;QACA,MAAM5C,UAAU,CAAC6C,GAAG,CAAC,SAAS,CAAC;;QAE/B;MAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,CAAC,SAAS;QACNH,YAAY,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC;IAEDC,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,SAAS,EAAE;IACX,oBAAOvC,OAAA,CAACF,cAAc;MAAC+C,UAAU;IAAA;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC;EAEA,oBACItB,OAAA,CAAC1B,mBAAmB;IAACwE,MAAM,EAAEvC,WAAY;IAAAO,QAAA,EACpCb,aAAa,gBACVD,OAAA,CAACxB,QAAQ;MAACuE,MAAM,EAAE9C,aAAc;MAAAa,QAAA,eAC5Bd,OAAA,CAACN,YAAY;QAAAoB,QAAA,eACTd,OAAA,CAACJ,eAAe;UAAAkB,QAAA,eACZd,OAAA,CAAC/B,MAAM;YAAA6C,QAAA,eACHd,OAAA;cAAK+B,SAAS,EAAC,KAAK;cAAAjB,QAAA,gBAChBd,OAAA,CAACiC,SAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGbtB,OAAA,CAACzB,cAAc;gBACXyE,QAAQ,EAAC,WAAW;gBACpBC,SAAS,EAAE,IAAK;gBAChBC,eAAe,EAAE,KAAM;gBACvBC,WAAW,EAAE,KAAM;gBACnBC,YAAY;gBACZC,GAAG,EAAE,KAAM;gBACXC,gBAAgB;gBAChBC,SAAS;gBACTC,YAAY;gBACZC,KAAK,EAAC;cAAO;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,gBAEXtB,OAAA,CAACN,YAAY;MAAAoB,QAAA,eACTd,OAAA,CAACJ,eAAe;QAAAkB,QAAA,eACZd,OAAA,CAAC/B,MAAM;UAAA6C,QAAA,eACHd,OAAA;YAAK+B,SAAS,EAAC,KAAK;YAAAjB,QAAA,gBAChBd,OAAA,CAACiC,SAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGbtB,OAAA,CAACzB,cAAc;cACXyE,QAAQ,EAAC,WAAW;cACpBC,SAAS,EAAE,IAAK;cAChBC,eAAe,EAAE,KAAM;cACvBC,WAAW,EAAE,KAAM;cACnBC,YAAY;cACZC,GAAG,EAAE,KAAM;cACXC,gBAAgB;cAChBC,SAAS;cACTC,YAAY;cACZC,KAAK,EAAC;YAAO;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACjB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAE9B,CAAC;AAACgB,GAAA,CAjFID,GAAG;AAAAqB,GAAA,GAAHrB,GAAG;AAmFT,eAAeA,GAAG;AAAC,IAAAX,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAjC,EAAA;AAAAiC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}