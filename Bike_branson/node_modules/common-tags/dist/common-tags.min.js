!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r(n.commonTags=n.commonTags||{})}(this,function(n){"use strict";var r,t,o=function(){function e(n,r){for(var t=0;t<r.length;t++){var e=r[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(n,e.key,e)}}return function(n,r,t){return r&&e(n.prototype,r),t&&e(n,t),n}}(),i=(r=["",""],t=["",""],Object.freeze(Object.defineProperties(r,{raw:{value:Object.freeze(t)}})));var e=function(){function e(){for(var o=this,n=arguments.length,r=Array(n),t=0;t<n;t++)r[t]=arguments[t];return function(n,r){if(!(n instanceof r))throw new TypeError("Cannot call a class as a function")}(this,e),this.tag=function(n){for(var r=arguments.length,t=Array(1<r?r-1:0),e=1;e<r;e++)t[e-1]=arguments[e];return"function"==typeof n?o.interimTag.bind(o,n):"string"==typeof n?o.transformEndResult(n):(n=n.map(o.transformString.bind(o)),o.transformEndResult(n.reduce(o.processSubstitutions.bind(o,t))))},0<r.length&&Array.isArray(r[0])&&(r=r[0]),this.transformers=r.map(function(n){return"function"==typeof n?n():n}),this.tag}return o(e,[{key:"interimTag",value:function(n,r){for(var t=arguments.length,e=Array(2<t?t-2:0),o=2;o<t;o++)e[o-2]=arguments[o];return this.tag(i,n.apply(void 0,[r].concat(e)))}},{key:"processSubstitutions",value:function(n,r,t){var e=this.transformSubstitution(n.shift(),r);return"".concat(r,e,t)}},{key:"transformString",value:function(n){return this.transformers.reduce(function(n,r){return r.onString?r.onString(n):n},n)}},{key:"transformSubstitution",value:function(n,t){return this.transformers.reduce(function(n,r){return r.onSubstitution?r.onSubstitution(n,t):n},n)}},{key:"transformEndResult",value:function(n){return this.transformers.reduce(function(n,r){return r.onEndResult?r.onEndResult(n):n},n)}}]),e}(),u=function(){var r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return{onEndResult:function(n){if(""===r)return n.trim();if("start"===(r=r.toLowerCase())||"left"===r)return n.replace(/^\s*/,"");if("end"===r||"right"===r)return n.replace(/\s*$/,"");throw new Error("Side not supported: "+r)}}};var a=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"initial";return{onEndResult:function(n){if("initial"===o){var r=n.match(/^[^\S\n]*(?=\S)/gm),t=r&&Math.min.apply(Math,function(n){if(Array.isArray(n)){for(var r=0,t=Array(n.length);r<n.length;r++)t[r]=n[r];return t}return Array.from(n)}(r.map(function(n){return n.length})));if(t){var e=new RegExp("^.{"+t+"}","gm");return n.replace(e,"")}return n}if("all"===o)return n.replace(/^[^\S\n]+/gm,"");throw new Error("Unknown type: "+o)}}},s=function(r,t){return{onEndResult:function(n){if(null==r||null==t)throw new Error("replaceResultTransformer requires at least 2 arguments.");return n.replace(r,t)}}},f=function(t,e){return{onSubstitution:function(n,r){if(null==t||null==e)throw new Error("replaceSubstitutionTransformer requires at least 2 arguments.");return null==n?n:n.toString().replace(t,e)}}},c={separator:"",conjunction:"",serial:!1},l=function(){var s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:c;return{onSubstitution:function(n,r){if(Array.isArray(n)){var t=n.length,e=s.separator,o=s.conjunction,i=s.serial,u=r.match(/(\n?[^\S\n]+)$/);if(n=u?n.join(e+u[1]):n.join(e+" "),o&&1<t){var a=n.lastIndexOf(e);n=n.slice(0,a)+(i?e:"")+" "+o+n.slice(a+1)}}return n}}},m=function(t){return{onSubstitution:function(n,r){if(null==t||"string"!=typeof t)throw new Error("You need to specify a string character to split by.");return"string"==typeof n&&n.includes(t)&&(n=n.split(t)),n}}},p=function(n){return null!=n&&!Number.isNaN(n)&&"boolean"!=typeof n},g=function(){return{onSubstitution:function(n){return Array.isArray(n)?n.filter(p):p(n)?n:""}}},d=new e(l({separator:","}),a,u),h=new e(l({separator:",",conjunction:"and"}),a,u),y=new e(l({separator:",",conjunction:"or"}),a,u),w=new e(m("\n"),g,l,a,u),v=new e(m("\n"),l,a,u,f(/&/g,"&amp;"),f(/</g,"&lt;"),f(/>/g,"&gt;"),f(/"/g,"&quot;"),f(/'/g,"&#x27;"),f(/`/g,"&#x60;")),b=new e(s(/(?:\n(?:\s*))+/g," "),u),S=new e(s(/(?:\n\s*)/g,""),u),T=new e(l({separator:","}),s(/(?:\s+)/g," "),u),A=new e(l({separator:",",conjunction:"or"}),s(/(?:\s+)/g," "),u),E=new e(l({separator:",",conjunction:"and"}),s(/(?:\s+)/g," "),u),L=new e(l,a,u),j=new e(l,s(/(?:\s+)/g," "),u),R=new e(a,u),k=new e(a("all"),u);n.TemplateTag=e,n.trimResultTransformer=u,n.stripIndentTransformer=a,n.replaceResultTransformer=s,n.replaceSubstitutionTransformer=f,n.replaceStringTransformer=function(r,t){return{onString:function(n){if(null==r||null==t)throw new Error("replaceStringTransformer requires at least 2 arguments.");return n.replace(r,t)}}},n.inlineArrayTransformer=l,n.splitStringTransformer=m,n.removeNonPrintingValuesTransformer=g,n.commaLists=d,n.commaListsAnd=h,n.commaListsOr=y,n.html=w,n.codeBlock=w,n.source=w,n.safeHtml=v,n.oneLine=b,n.oneLineTrim=S,n.oneLineCommaLists=T,n.oneLineCommaListsOr=A,n.oneLineCommaListsAnd=E,n.inlineLists=L,n.oneLineInlineLists=j,n.stripIndent=R,n.stripIndents=k,Object.defineProperty(n,"__esModule",{value:!0})});
