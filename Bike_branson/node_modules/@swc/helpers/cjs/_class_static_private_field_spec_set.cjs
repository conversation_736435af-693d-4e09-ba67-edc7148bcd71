"use strict";

var _class_apply_descriptor_set = require("./_class_apply_descriptor_set.cjs");
var _class_check_private_static_access = require("./_class_check_private_static_access.cjs");
var _class_check_private_static_field_descriptor = require("./_class_check_private_static_field_descriptor.cjs");

function _class_static_private_field_spec_set(receiver, classConstructor, descriptor, value) {
    _class_check_private_static_access._(receiver, classConstructor);
    _class_check_private_static_field_descriptor._(descriptor, "set");
    _class_apply_descriptor_set._(receiver, descriptor, value);

    return value;
}
exports._ = _class_static_private_field_spec_set;
