import t from"postcss-value-parser";import e from"vm";function n(t){return t*(180/Math.PI)}const r={turn:function(t){return 2*t*Math.PI},deg:function(t){return t*(Math.PI/180)},grad:function(t){return t*(Math.PI/200)}};function u(t){return"word"===t.type}const i=["+","-","*","/"];var o;function a(t){return!Number.isNaN(t)&&Number.isFinite(t)}function s(n,u=!1){let f=!0;const l=[];if(n.filter((t=>"function"===t.type)).forEach((t=>{var e;if(!f)return;if(""!==t.value)return void(f=!1);const n=s(t.nodes.slice(0),u),r=1===n.length,i=Number((null==(e=n[0])?void 0:e.value)||"");r&&"word"===n[0].type&&!Number.isNaN(i)?(c(t),t.value=n[0].value):f=!1})),!f)return n;const m=n.filter((t=>"word"===t.type||i.includes(t.value)));let v=o.Number;const h=[];let N;const b=(t,e,n)=>{if(v===e){if(e===o.Number){const e=n||"";h.includes(e)||h.push({number:t,unit:e,index:l.length})}l.push(t),v=e===o.Number?o.Operation:o.Number}else f=!1};for(let e=0,n=m.length;e<n&&f;e++){const n=m[e];if(i.includes(n.value)){b(n.value,o.Operation);continue}if("pi"===n.value){b(Math.PI.toString(),o.Number);continue}if("e"===n.value){b(Math.E.toString(),o.Number);continue}const a=t.unit(n.value);if(!a){f=!1;break}if(u){if(N||(N=a.unit),N!==a.unit){f=!1;break}b(n.value,o.Operation)}else a.unit?"rad"!==a.unit&&"function"!=typeof r[a.unit]?f=!1:b(a.number,o.Number,a.unit):b(n.value,o.Number)}if(!f)return n;if(l.length%2==0||l.length<3)return n;let d;try{let t="";const n=new Set(h.map((t=>t.unit)));if(n.size>1)if(n.has("")){if(2!==n.size)throw new Error;[t]=Array.from(n).filter((t=>""!==t))}else h.forEach((t=>{if("rad"!==t.unit){const e=r[t.unit](Number(t.number));if(!a(e))throw new Error;l[t.index]=e.toString()}}));const u=e.createContext({result:NaN});new e.Script(`result = ${l.join(" ")}`).runInContext(u),"number"==typeof u.result&&a(u.result)&&(t&&(u.result=r[t](u.result)),a(u.result)&&(d=u.result))}catch(t){}if(void 0!==d){let t=d.toString();N&&(t+=N);const e=n[0].sourceIndex,r=t.length;n.length=0,n.push({type:"word",value:t,sourceIndex:e,sourceEndIndex:r})}return n}function c(t){delete t.nodes;const e=t;return e.type="word",e}function f(t,e){if(!Number.isNaN(t)){if(t>Number.MAX_SAFE_INTEGER)return"infinity";if(t<Number.MIN_SAFE_INTEGER)return"-infinity"}return Number(t.toFixed(e)).toString()}function l(e){let n,r="";if("infinity"===e.toLowerCase()?n=1/0:"-infinity"===e.toLowerCase()?n=-1/0:"pi"===e?n=Math.PI:"e"===e&&(n=Math.E),!n){const u=t.unit(e);if(!u)return!1;n=Number(u.number),Number.isNaN(n)||(r=u.unit)}return{number:n,unit:r}}function m(t,e=!0){t.nodes=s(t.nodes);const n=t.nodes.filter(u);if(1!==t.nodes.length||1!==n.length)return;const{value:i}=n[0],o=l(i);if(!o)return;let a=o.number;if(e){if(o.unit&&"rad"!==o.unit){if(!r[o.unit])return;a=r[o.unit](a)}}else if(o.unit)return;return[c(t),a]}!function(t){t[t.Number=0]="Number",t[t.Operation=1]="Operation"}(o||(o={}));const v=[{check:"asin(",transform:function(e){const r=t(e.value);return r.walk((t=>{if("function"!==t.type||"asin"!==t.value.toLowerCase())return;const e=m(t,!1);if(!e)return;const[r,u]=e;let i=Math.asin(u);Number.isNaN(i)||"number"!=typeof i||(i=`${f(n(i),2)}deg`),r.value=i+""}),!0),r.toString()}},{check:"acos(",transform:function(e){const r=t(e.value);return r.walk((t=>{if("function"!==t.type||"acos"!==t.value.toLowerCase())return;const e=m(t,!1);if(!e)return;const[r,u]=e;let i=Math.acos(u);Number.isNaN(i)||"number"!=typeof i||(i=`${f(n(i),2)}deg`),r.value=i+""}),!0),r.toString()}},{check:"atan(",transform:function(e){const r=t(e.value);return r.walk((t=>{if("function"!==t.type||"atan"!==t.value.toLowerCase())return;const e=m(t,!1);if(!e)return;const[r,u]=e;let i=Math.atan(u);Number.isNaN(i)||"number"!=typeof i||(i=`${f(n(i),2)}deg`),r.value=i+""}),!0),r.toString()}},{check:"atan2(",transform:function(e){const r=t(e.value);return r.walk((t=>{if("function"!==t.type||"atan2"!==t.value.toLowerCase())return;const e=t.nodes.findIndex((t=>"div"===t.type&&","===t.value));if(e<0)return;let r=t.nodes.slice(0,e).filter(u),i=t.nodes.slice(e+1).filter(u);if(0===r.length||0===i.length)return;if(r.length>1&&(r=s(r,!0)),i.length>1&&(i=s(i,!0)),1!==r.length||1!==i.length)return;const o=l(r[0].value),a=l(i[0].value);if(!o||!a)return;if(o.unit!==a.unit)return;let m=Math.atan2(o.number,a.number);Number.isNaN(m)||"number"!=typeof m||(m=`${f(n(m),2)}deg`);c(t).value=m+""}),!0),r.toString()}},{check:"sin(",transform:function(e){const n=t(e.value);return n.walk((t=>{if("function"!==t.type||"sin"!==t.value.toLowerCase())return;const e=m(t);if(!e)return;const[n,r]=e;n.value=f(Math.sin(r),5)}),!0),n.toString()}},{check:"cos(",transform:function(e){const n=t(e.value);return n.walk((t=>{if("function"!==t.type||"cos"!==t.value.toLowerCase())return;const e=m(t);if(!e)return;const[n,r]=e;n.value=f(Math.cos(r),5)}),!0),n.toString()}},{check:"tan(",transform:function(e){const r=t(e.value);return r.walk((t=>{if("function"!==t.type||"tan"!==t.value.toLowerCase())return;const e=m(t);if(!e)return;const[r,u]=e,i=Number(f(n(u),2)),o=i/90;r.value=i%90==0&&o%2!=0?o>0?"infinity":"-infinity":f(Math.tan(u),5)}),!0),r.toString()}}],h=t=>{const e=Object.assign({preserve:!1},t);return{postcssPlugin:"postcss-trigonometric-functions",Declaration(t){const n=v.filter((e=>t.value.toLowerCase().includes(e.check)));if(!t||0===n.length)return;const r=t.clone();n.forEach((t=>{const e=t.transform(r);e&&(r.value=e)})),t.value!==r.value&&(t.before(r),e.preserve||t.remove())}}};h.postcss=!0;export{h as default};
