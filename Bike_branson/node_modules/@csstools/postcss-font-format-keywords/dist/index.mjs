import e from"postcss-value-parser";const o=["woff","truetype","opentype","woff2","embedded-opentype","collection","svg"],t=t=>{const r="preserve"in Object(t)&&Boolean(t.preserve);return{postcssPlugin:"postcss-font-format-keywords",AtRule(t){"font-face"===t.name.toLowerCase()&&t.walkDecls((t=>{if("src"!==t.prop.toLowerCase())return;if(!t.value.toLowerCase().includes("format("))return;const s=e(t.value);s.walk((t=>{"function"===t.type&&"format"===t.value.toLowerCase()&&t.nodes.forEach((t=>{"word"===t.type&&o.includes(t.value.toLowerCase())&&(t.value=e.stringify({type:"string",value:t.value,quote:'"'}))}))})),s.toString()!==t.value&&(t.cloneBefore({value:s.toString()}),r||t.remove())}))}}};t.postcss=!0;export{t as default};
