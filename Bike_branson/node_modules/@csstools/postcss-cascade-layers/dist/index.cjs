"use strict";var e=require("postcss-selector-parser"),r=require("@csstools/selector-specificity");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=a(e);const s=["media","supports"],n=["keyframes"],o=new Set(["layer","supports","media","container","scope"]);function l(e){if("atrule"!==e.type)return!1;if("layer"!==e.name.toLowerCase())return!1;let r=e.parent;for(;r;){if("rule"===r.type)return!1;if("atrule"===r.type&&!o.has(r.name.toLowerCase()))return!1;r=r.parent}return!0}class i{constructor(){this.anonymousLayerCount=0,this.layerCount=0,this.anonymousLayerCount=0,this.layerCount=0,this.layerOrder=new Map,this.layerParamsParsed=new Map,this.layerNameParts=new Map}createAnonymousLayerName(){const e=`anonymous-${this.anonymousLayerCount}-6efdb677-bb05-44e5-840f-29d2175862fd`;return this.addLayerNameParts(e),this.layerParamsParsed.set(e,[e]),this.anonymousLayerCount++,e}createImplicitLayerName(e){const r=this.layerNameParts.get(e),a=`implicit-${r[r.length-1]}-b147acf6-11a6-4338-a4d0-80aef4cd1a2f`;return this.addLayerNameParts([...r,a]),this.layerParamsParsed.set(a,[a]),a}addLayerParams(e,r){r?"string"!=typeof r?this.layerParamsParsed.set(e,r):this.layerParamsParsed.set(e,[r]):this.layerParamsParsed.set(e,[e])}addLayerNameParts(e){"string"!=typeof e?this.layerNameParts.set(e.join("."),e):this.layerNameParts.set(e,[e])}getLayerParams(e){const r=[...this.layerParamsParsed.get(e.params)];let a=e.parent;for(;a;)"atrule"===a.type?(l(a)&&r.push(...this.layerParamsParsed.get(a.params)),a=a.parent):a=a.parent;return r.reverse(),r.flatMap((e=>this.layerNameParts.get(e)))}getLayerNameList(e){const r=this.layerNameParts.get(e),a=[];for(let e=0;e<r.length;e++){const t=r.slice(0,e+1).join(".");this.layerParamsParsed.has(t)||this.layerParamsParsed.set(t,[t]),this.layerNameParts.has(t)||this.layerNameParts.set(t,r.slice(0,e+1)),a.push(r.slice(0,e+1).join("."))}return a}sortLayerNames(){for(const[e,r]of this.layerOrder){const a=this.layerNameParts.get(e);for(let e=1;e<a.length;e++){const t=a.slice(0,e).join(".");this.layerOrder.has(t)||this.layerOrder.set(t,r)}}let e=Array.from(this.layerOrder.entries());e=e.sort(((e,r)=>{const a=this.layerNameParts.get(e[0]),t=this.layerNameParts.get(r[0]);if(a[0]!==t[0])return this.layerOrder.get(a[0])-this.layerOrder.get(t[0]);const s=Math.max(a.length,t.length);for(let e=0;e<s;e++){const r=a[e],s=t[e];if(r!==s)return r?s?this.layerOrder.get(a.slice(0,e).join("."))-this.layerOrder.get(t.slice(0,e).join(".")):-1:1}})),this.layerOrder.clear(),e.forEach(((e,r)=>{this.layerOrder.set(e[0],r)}))}}function u(e,r){const a=t.default().astSync(e),s=t.default().astSync(function(e){if(0===e)return"";let r="";for(let a=0;a<e;a++)r+=":not(#\\#)";return r}(r));let n=!1;for(let e=0;e<a.nodes[0].nodes.length;e++)if("combinator"===a.nodes[0].nodes[e].type||t.default.isPseudoElement(a.nodes[0].nodes[e])){a.nodes[0].insertBefore(a.nodes[0].nodes[e],s),n=!0;break}return n||a.nodes[0].insertAfter(a.nodes[0].nodes[a.nodes[0].nodes.length-1],s),a.toString()}function y(e,r){let a=!1;return e.walk((e=>{if(r(e))return a=!0,!1})),a}function c(e,r){let a=!1;return e.walkAtRules((e=>{if(r(e))return a=!0,!1})),a}function d(e){let r=e.parent;for(;r;)if("atrule"===r.type){if(l(r))return r;r=r.parent}else r=r.parent;return null}function p(e){var r;e.walk((e=>{var r;("rule"===e.type||"atrule"===e.type&&["layer",...s].includes(e.name.toLowerCase()))&&(0===(null==(r=e.nodes)?void 0:r.length)&&e.remove())})),0===(null==(r=e.nodes)?void 0:r.length)&&e.remove()}function m(e){let r=e;for(;r;){if(void 0===r.nodes)return;if(r.nodes.length>0)return;const e=r.parent;r.remove(),r=e}}function f(e,r,{result:a,options:t}){e.walkAtRules((e=>{if(!l(e))return;const n=r.getLayerParams(e),o=n.join(".");r.layerOrder.has(o)||(t.onConditionalRulesChangingLayerOrder&&function(e){let r=e.parent;for(;r;)if("atrule"===r.type){if(s.includes(r.name.toLowerCase()))return r;r=r.parent}else r=r.parent;return null}(e)&&!e.params.endsWith("b147acf6-11a6-4338-a4d0-80aef4cd1a2f")&&!e.params.endsWith("6efdb677-bb05-44e5-840f-29d2175862fd")&&e.warn(a,"handling different layer orders in conditional rules is unsupported by this plugin and will cause style differences between browser versions."),r.layerParamsParsed.has(o)||r.layerParamsParsed.set(o,[o]),r.layerNameParts.has(o)||r.layerNameParts.set(o,[...n]),r.getLayerNameList(o).forEach((e=>{r.layerOrder.has(e)||(r.layerOrder.set(e,r.layerCount),r.layerCount+=1)}))),e.nodes&&0!==e.nodes.length||e.remove()}))}const h=e=>{const a=Object.assign({onRevertLayerKeyword:"warn",onConditionalRulesChangingLayerOrder:"warn",onImportLayerRule:"warn"},e);return{postcssPlugin:"postcss-cascade-layers",OnceExit(e,{result:o}){a.onRevertLayerKeyword&&e.walkDecls((e=>{"revert-layer"===e.value.toLowerCase()&&e.warn(o,'handling "revert-layer" is unsupported by this plugin and will cause style differences between browser versions.')})),a.onImportLayerRule&&e.walkAtRules((e=>{"import"===e.name.toLowerCase()&&e.params.toLowerCase().includes("layer")&&e.warn(o,"To use @import with layers, the postcss-import plugin is also required. This plugin alone will not support using the @import at-rule.")})),function(e){e.walkDecls((e=>{if(!e.important)return;const r=e.parent;if(r.parent&&"atrule"===r.parent.type&&n.includes(r.parent.name.toLowerCase()))return;const a=r.clone();a.each((e=>{"decl"===e.type&&e.important||e.remove()})),r.each((e=>{"decl"===e.type&&e.important&&e.remove()})),r.before(a),p(r)}))}(e);const h=new i;if(function(e,r){e.walkAtRules((e=>{if(!l(e))return;if(e.params){const a=[];let s=!1;if(t.default().astSync(e.params).each((e=>{const t=[];e.walk((e=>{switch(e.type){case"class":case"tag":t.push(e.value);break;default:s=!0}})),s||(a.push(t.join(".")),r.addLayerNameParts(t))})),r.addLayerParams(e.params,a),e.nodes&&a.length>1&&(s=!0),s)return void(e.name="csstools-invalid-layer");if(!e.nodes||0===e.nodes.length){if(a.length<=1)return;return a.slice(0,-1).forEach((a=>{r.addLayerParams(a,a),e.cloneBefore({params:a})})),r.addLayerParams(a[a.length-1],a[a.length-1]),void(e.params=a[a.length-1])}}e.params||(e.raws.afterName=" ",e.params=r.createAnonymousLayerName());const a=c(e,(e=>l(e))),n=y(e,(r=>{if("rule"===r.type)return d(r)===e}));if(a&&n){const a=r.createImplicitLayerName(e.params),t=e.clone({params:a});t.walkAtRules((e=>{l(e)&&e.remove()})),e.walk((r=>{"atrule"===r.type&&l(r)||"atrule"===r.type&&s.includes(r.name.toLowerCase())||d(r)===e&&r.remove()})),e.append(t),p(e),m(e)}}))}(e,h),f(e,h,{result:o,options:a}),!h.layerCount)return void e.walkAtRules("csstools-invalid-layer",(e=>{e.name="layer"}));let w=0;for(e.walkRules((e=>{e.selectors.forEach((e=>{const a=r.selectorSpecificity(t.default().astSync(e));w=Math.max(w,a.a+1)}))})),e.walkRules((e=>{e.parent&&"atrule"===e.parent.type&&n.includes(e.parent.name.toLowerCase())||d(e)||e.some((e=>"decl"===e.type&&e.important))||(e.selectors=e.selectors.map((e=>u(e,h.layerCount*w))))})),h.sortLayerNames(),function(e,r){for(;c(e,(e=>e.nodes&&c(e,(e=>l(e)))));){let a=!1;if(e.walkAtRules((t=>{if(l(t)&&t.parent!==e){if("atrule"===t.parent.type&&l(t.parent)){const e=t.parent;return r.layerNameParts.set(`${e.params}.${t.params}`,[...r.layerNameParts.get(e.params),...r.layerNameParts.get(t.params)]),r.layerParamsParsed.set(`${e.params}.${t.params}`,[`${e.params}.${t.params}`]),t.params=`${e.params}.${t.params}`,e.before(t),p(e),void m(e)}if("atrule"===t.parent.type){const e=t.parent,r=e.clone(),a=t.clone();return r.removeAll(),a.removeAll(),r.append(t.nodes),a.append(r),e.before(a),t.remove(),p(e),void m(e)}a=!0}})),a)break}}(e,h),function(e,r){e.walkAtRules((e=>{if(!l(e))return;const r=e.clone(),a=e.clone();r.walkAtRules((e=>{if(n.includes(e.name.toLowerCase())){const r=e.parent;return e.remove(),p(r),void m(r)}if(y(e,(e=>"rule"===e.type)))return;const r=e.parent;e.remove(),p(r),m(r)})),a.walkRules((e=>{if(e.parent&&"atrule"===e.parent.type&&n.includes(e.parent.name.toLowerCase()))return;const r=e.parent;e.remove(),p(r),m(r)})),a.walkAtRules((e=>{if(s.includes(e.name.toLowerCase()))return p(e),void m(e)})),r.name="csstools-layer-with-selector-rules",e.replaceWith(r,a),0===r.nodes.length&&r.remove(),0===a.nodes.length&&a.remove()})),e.nodes.sort(((e,a)=>{const t="atrule"===e.type&&"layer"===e.name.toLowerCase(),s="atrule"===a.type&&"layer"===a.name.toLowerCase();return t&&s?r.layerOrder.get(e.params)-r.layerOrder.get(a.params):t!==s?t?-1:1:0})),e.walkAtRules("csstools-layer-with-selector-rules",(e=>{e.name="layer"}))}(e,h),e.walkRules((e=>{if(e.parent&&"atrule"===e.parent.type&&n.includes(e.parent.name.toLowerCase()))return;const r=d(e);if(!r)return;const a=h.getLayerParams(r).join(".");let t=h.layerOrder.get(a)*w;e.some((e=>"decl"===e.type&&e.important))&&(t=h.layerCount-t),e.selectors=e.selectors.map((e=>u(e,t)))}));c(e,(e=>l(e)));)e.walkAtRules((e=>{l(e)&&e.replaceWith(e.nodes)}));e.walkAtRules("csstools-invalid-layer",(e=>{e.name="layer"}))}}};h.postcss=!0,module.exports=h;
