{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "5.3.6", "config": {"version_short": "5.3"}, "keywords": ["css", "sass", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "https://getbootstrap.com/", "author": "The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "main": "dist/js/bootstrap.js", "module": "dist/js/bootstrap.esm.js", "sass": "scss/bootstrap.scss", "style": "dist/css/bootstrap.css", "scripts": {"start": "npm-run-all --parallel watch docs-serve", "bundlewatch": "bundlewatch --config .bundlewatch.config.json", "css": "npm-run-all css-compile css-prefix css-rtl css-minify", "css-compile": "sass --style expanded --source-map --embed-sources --no-error-css scss/:dist/css/", "css-rtl": "cross-env NODE_ENV=RTL postcss --config build/postcss.config.mjs --dir \"dist/css\" --ext \".rtl.css\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*.rtl.css\"", "css-lint": "npm-run-all --aggregate-output --continue-on-error --parallel css-lint-*", "css-lint-stylelint": "stylelint \"**/*.{css,scss}\" --cache --cache-location .cache/.stylelintcache", "css-lint-vars": "fusv scss/ site/src/scss/", "css-minify": "npm-run-all --aggregate-output --parallel css-minify-*", "css-minify-main": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*rtl*.css\"", "css-minify-rtl": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*rtl.css\" \"!dist/css/*.min.css\"", "css-prefix": "npm-run-all --aggregate-output --parallel css-prefix-*", "css-prefix-main": "postcss --config build/postcss.config.mjs --replace \"dist/css/*.css\" \"!dist/css/*.rtl*.css\" \"!dist/css/*.min.css\"", "css-prefix-examples": "postcss --config build/postcss.config.mjs --replace \"site/src/assets/examples/**/*.css\"", "css-prefix-examples-rtl": "cross-env-shell NODE_ENV=RTL postcss --config build/postcss.config.mjs --dir \"site/src/assets/examples/\" --ext \".rtl.css\" --base \"site/src/assets/examples/\" \"site/src/assets/examples/{blog,carousel,dashboard,cheatsheet}/*.css\" \"!site/src/assets/examples/{blog,carousel,dashboard,cheatsheet}/*.rtl.css\"", "css-test": "jasmine --config=scss/tests/jasmine.js", "js": "npm-run-all js-compile js-minify", "js-compile": "npm-run-all --aggregate-output --parallel js-compile-*", "js-compile-standalone": "rollup --environment BUNDLE:false --config build/rollup.config.mjs --sourcemap", "js-compile-standalone-esm": "rollup --environment ESM:true,BUNDLE:false --config build/rollup.config.mjs --sourcemap", "js-compile-bundle": "rollup --environment BUNDLE:true --config build/rollup.config.mjs --sourcemap", "js-compile-plugins": "node build/build-plugins.mjs", "js-lint": "eslint --cache --cache-location .cache/.eslintcache --report-unused-disable-directives --ext .html,.js,.mjs,.md .", "js-minify": "npm-run-all --aggregate-output --parallel js-minify-*", "js-minify-standalone": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.js.map,includeSources,url=bootstrap.min.js.map\" --output dist/js/bootstrap.min.js dist/js/bootstrap.js", "js-minify-standalone-esm": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.esm.js.map,includeSources,url=bootstrap.esm.min.js.map\" --output dist/js/bootstrap.esm.min.js dist/js/bootstrap.esm.js", "js-minify-bundle": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.bundle.js.map,includeSources,url=bootstrap.bundle.min.js.map\" --output dist/js/bootstrap.bundle.min.js dist/js/bootstrap.bundle.js", "js-test": "npm-run-all --aggregate-output --parallel js-test-karma js-test-jquery js-test-integration-*", "js-debug": "cross-env DEBUG=true npm run js-test-karma", "js-test-karma": "karma start js/tests/karma.conf.js", "js-test-integration-bundle": "rollup --config js/tests/integration/rollup.bundle.js", "js-test-integration-modularity": "rollup --config js/tests/integration/rollup.bundle-modularity.js", "js-test-cloud": "cross-env BROWSERSTACK=true npm run js-test-karma", "js-test-jquery": "cross-env JQUERY=true npm run js-test-karma", "lint": "npm-run-all --aggregate-output --continue-on-error --parallel js-lint css-lint lockfile-lint", "docs": "npm-run-all docs-build docs-lint", "docs-build": "npm run astro-build", "docs-compile": "npm run docs-build", "docs-vnu": "node build/vnu-jar.mjs", "docs-lint": "npm-run-all docs-prettier-check docs-vnu", "docs-prettier-check": "prettier --config site/.prettierrc.json -c --cache site", "docs-prettier-format": "prettier --config site/.prettierrc.json --write --cache site", "docs-serve": "npm run astro-dev", "docs-serve-only": "npx sirv-cli _site --port 9001", "lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "update-deps": "ncu -u -x eslint,eslint-config-xo,karma-browserstack-launcher,karma-rollup-preprocessor,sass", "release": "npm-run-all dist release-sri docs-build release-zip*", "release-sri": "node build/generate-sri.mjs", "release-version": "node build/change-version.mjs", "release-zip": "cross-env-shell \"rm -rf bootstrap-$npm_package_version-dist bootstrap-$npm_package_version-dist.zip && cp -r dist/ bootstrap-$npm_package_version-dist && zip -qr9 bootstrap-$npm_package_version-dist.zip bootstrap-$npm_package_version-dist && rm -rf bootstrap-$npm_package_version-dist\"", "release-zip-examples": "node build/zip-examples.mjs", "dist": "npm-run-all --aggregate-output --parallel css js", "test": "npm-run-all lint dist js-test docs-build docs-lint", "netlify": "npm-run-all dist release-sri astro-build", "watch": "npm-run-all --parallel watch-*", "watch-css-main": "nodemon --watch scss/ --ext scss --exec \"npm-run-all css-lint css-compile css-prefix\"", "watch-css-dist": "nodemon --watch dist/css/ --ext css --ignore \"dist/css/*.rtl.*\" --exec \"npm run css-rtl\"", "watch-css-docs": "nodemon --watch site/src/scss/ --ext scss --exec \"npm run css-lint\"", "watch-css-test": "nodemon --watch scss/ --ext scss,js --exec \"npm run css-test\"", "watch-js-main": "nodemon --watch js/src/ --ext js --exec \"npm-run-all js-lint js-compile\"", "watch-js-docs": "nodemon --watch site/src/assets/ --ext js --exec \"npm run js-lint\"", "astro-dev": "astro dev --root site", "astro-build": "astro build --root site && rm -rf _site && cp -r site/dist _site", "astro-preview": "astro preview --root site"}, "peerDependencies": {"@popperjs/core": "^2.11.8"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/markdown-remark": "^6.3.1", "@astrojs/mdx": "^4.2.6", "@astrojs/prism": "^3.2.0", "@astrojs/sitemap": "^3.3.1", "@babel/cli": "^7.27.0", "@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@docsearch/js": "^3.9.0", "@popperjs/core": "^2.11.8", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@stackblitz/sdk": "^1.11.0", "@types/google.analytics": "^0.0.46", "@types/js-yaml": "^4.0.5", "@types/mime": "^3.0.1", "@types/prismjs": "^1.26.0", "astro": "^5.7.10", "astro-auto-import": "^0.4.4", "autoprefixer": "^10.4.21", "bundlewatch": "^0.4.1", "clean-css-cli": "^5.6.3", "clipboard": "^2.0.11", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-xo": "^0.45.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-unicorn": "^55.0.0", "find-unused-sass-variables": "^6.1.0", "github-slugger": "^2.0.0", "globby": "^14.1.0", "hammer-simulator": "0.0.1", "htmlparser2": "^10.0.0", "image-size": "^2.0.2", "ip": "^2.0.1", "jasmine": "^5.6.0", "jquery": "^3.7.1", "js-yaml": "^4.1.0", "karma": "^6.4.4", "karma-browserstack-launcher": "1.4.0", "karma-chrome-launcher": "^3.2.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-rollup-preprocessor": "7.0.7", "lockfile-lint": "^4.14.1", "mime": "^4.0.7", "nodemon": "^3.1.10", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "rehype-autolink-headings": "^7.1.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "rollup": "^4.38.0", "rollup-plugin-istanbul": "^5.0.0", "rtlcss": "^4.3.0", "sass": "1.78.0", "sass-true": "^8.1.0", "shelljs": "^0.9.2", "stylelint": "^16.17.0", "stylelint-config-twbs-bootstrap": "^16.0.0", "terser": "^5.39.0", "unist-util-visit": "^5.0.0", "vnu-jar": "24.10.17", "zod": "^3.24.3"}, "files": ["dist/{css,js}/*.{css,js,map}", "js/{src,dist}/**/*.{js,map}", "js/index.{esm,umd}.js", "scss/**/*.scss", "!scss/tests/**"], "jspm": {"registry": "npm", "main": "js/bootstrap", "directories": {"lib": "dist"}, "shim": {"js/bootstrap": {"deps": ["@popperjs/core"]}}, "dependencies": {}, "peerDependencies": {"@popperjs/core": "^2.11.8"}}}