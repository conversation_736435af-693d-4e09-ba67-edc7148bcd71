# Change Log

All notable changes to this project will be documented in this file.
This project adheres to [Semantic Versioning].

This change log follows the format documented in [Keep a CHANGELOG].

[semantic versioning]: http://semver.org/
[keep a changelog]: http://keepachangelog.com/

## v2.30.0 - 2023-04-30

<PERSON><PERSON> to @kossnocorp and @Andarist for working on the release.

### Changes

- Fixed increased build size after enabling compatibility with older browsers in the previous release. This was done by adding @babel/runtime as a dependency. [See more details](https://github.com/date-fns/date-fns/issues/3208#issuecomment-1528592465).

## v2.29.3 - 2022-09-13

This release is prepared by our own @leshakoss.

### Fixed

- [Fixed Ukrainian (`uk`) locale grammar for `formatDistance`.](https://github.com/date-fns/date-fns/pull/3175)

- [Improved browser compatibility by transforming the code with `@babel/preset-env`.](https://github.com/date-fns/date-fns/pull/3167)

## v2.29.2 - 2022-08-18

This release is brought to you by @nopears, @vadimpopa and @leshakoss.

### Fixed

- [Fixed `sv` locale abbreviated months matcher.](https://github.com/date-fns/date-fns/pull/3160)

- [Fixed `uk` locale abbreviated months matcher.](https://github.com/date-fns/date-fns/pull/3139)

- [Fixed a breaking change in `intervalToDuration` by removing a recently introduced RangeError.](https://github.com/date-fns/date-fns/pull/3153)

## v2.29.1 - 2022-08-18

Thanks to @fturmel for working on the release.

### Fixed

- [Fixed TypeScript and flow types for daysInYear constant.](https://github.com/date-fns/date-fns/pull/3125)

## v2.29.0 - 2022-07-22

On this release worked @tan75, @kossnocorp, @nopears, @Balastrong, @cpapazoglou, @dovca, @aliasgar55, @tomchentw, @JuanM04, @alexandresaura, @fturmel, @aezell, @andersravn, @TiagoPortfolio, @SukkaW, @Zebreus, @aviskarkc10, @maic66, @a-korzun, @Mejans, @davidspiess, @alexgul1, @matroskin062, @undecaf, @mprovenc, @jooola and @leshakoss.

### Added

- [Added `intlFormatDistance` function`.](https://github.com/date-fns/date-fns/pull/2173)

- [Added `setDefaultOptions` and `getDefaultOptions` functions that allow you to set default default locale, `weekStartsOn` and `firstWeekContainsDate`.](https://github.com/date-fns/date-fns/pull/3069)

- [Added `roundingMethod` option to `roundToNearestMinutes`.](https://github.com/date-fns/date-fns/pull/3091)

- [Added Swiss Italian locale (`it-CH`).](https://github.com/date-fns/date-fns/pull/2886)

- [Added Occitan (`oc`) locale.](https://github.com/date-fns/date-fns/pull/2106) ([#2061](https://github.com/date-fns/date-fns/issues/2061))

- [Added Belarusian Classic (`be-tarask`) locale.](https://github.com/date-fns/date-fns/pull/3115)

### Fixed

- [Fixed Azerbaijani (`az`) locale for `formatDistance`.](https://github.com/date-fns/date-fns/pull/2924)

- [Fixed Czech (`cs`) locale for `parse`.](https://github.com/date-fns/date-fns/pull/3059)

- [Fixed TypeScript types for constants.](https://github.com/date-fns/date-fns/pull/2941)

- [Fixed long formatters in the South African English locale (`en-ZA`).](https://github.com/date-fns/date-fns/pull/3014)

- [Fixed a typo in the Icelandic locale (`is`) for `format`.](https://github.com/date-fns/date-fns/pull/2974)

- [Fixed weekday format for `formatRelative` in the Portuguese locale (`pt`).](https://github.com/date-fns/date-fns/pull/2992)

- [Fixed `intervalToDuration` being off by 1 day sometimes.](https://github.com/date-fns/date-fns/pull/2616)

- [Fixed ordinal number formatting in Italian locale (`it`).](https://github.com/date-fns/date-fns/pull/1617)

- [Fixed issue parsing months in Croatian (`hr`), Georgian (`ka`) and Serbian (`sr` and `sr-Latn`) locales.](https://github.com/date-fns/date-fns/pull/2898)

### Changed

- [Replaced `git.io` links with full URLs in error messages.](https://github.com/date-fns/date-fns/pull/3021)

- [_Internal_: removed "v2.0.0 breaking changes" section from individual function docs](https://github.com/date-fns/date-fns/pull/2905)

## v2.28.0 - 2021-12-28

Kudos to @tan75, @fturmel, @arcanar7, @jeffjose, @helmut-lang, @zrev2220, @jooola, @minitesh, @cowboy-bebug, @mesqueeb, @JuanM04, @zhirzh, @damon02 and @leshakoss for working on the release.

### Added

- [Added West Frisian (`fy`) locale.](https://github.com/date-fns/date-fns/pull/2183)

- [Added Uzbek Cyrillic locale (`uz-Cyrl`).](https://github.com/date-fns/date-fns/pull/2811)

### Fixed

- [add the missing accent mark for Saturday in Spanish locale (`es`) for `format`.](https://github.com/date-fns/date-fns/pull/2869)

- [allowed `K` token to be used with `a` or `b` in `parse`.](https://github.com/date-fns/date-fns/pull/2814)

## v2.27.0 - 2021-11-30

Kudos to @tan75, @hg-pyun, @07akioni, @razvanmitre, @Haqverdi, @pgcalixto, @janziemba, @fturmel, @JuanM04, @zhirzh, @seanghay, @bulutfatih, @nodeadtree, @cHaLkdusT, @a-korzun, @fishmandev, @wingclover, @Zacharias3690, @kossnocorp and @leshakoss for working on the release.

### Fixed

- [Fixed translation for quarters in `format` in Chinese Simplified locale (`zh-CN`).](https://github.com/date-fns/date-fns/pull/2771)

- [Fixed `P` token in `format` for Romanian locale (`ro`).](https://github.com/date-fns/date-fns/pull/2213)

- [Fixed era and month formatters in Azerbaijani locale (`az`).](https://github.com/date-fns/date-fns/pull/1632)

- [Fixed `formatRelative` patterns in Georgian locale (`ka`).](https://github.com/date-fns/date-fns/pull/2797)

- [Fixed regular expressions for `parse` in Estonian locale (`er`).](https://github.com/date-fns/date-fns/pull/2038)

- [Fixed the format of zeros in `formatDuration` in Czech locale (`cs`).](https://github.com/date-fns/date-fns/pull/2579)

- [Fixed ordinal formatting for years, weeks, hours, minutes and seconds in `fr`, `fr-CA` and `fr-CH` locales.](https://github.com/date-fns/date-fns/pull/2626)

- [Fixed constants not having proper TypeScript and Flow types.](https://github.com/date-fns/date-fns/pull/2791)

- [Fixed translation for Monday in Turkish locale (`tr`).](https://github.com/date-fns/date-fns/pull/2720)

- [Fixed `eachMinuteOfInterval` not handling intervals less than a minute correctly.](https://github.com/date-fns/date-fns/pull/2603)

- [Fixed flow types for `closestTo` and `closestIndexTo`.](https://github.com/date-fns/date-fns/pull/2781)

### Added

- [Added Khmer locale (`km`).](https://github.com/date-fns/date-fns/pull/2713)

## v2.26.0 - 2021-11-19

Thanks to @kossnocorp, @leshakoss, @tan75, @gaplo, @AbdAllahAbdElFattah13, @fturmel, @kentaro84207, @V-Gutierrez, @atefBB, @jhonatanmacazana, @zhirzh, @Haqverdi, @mandaputtra, @micnic and @rikkalo for working on the release.

### Fixed

- [Fixed `formatRelative` format for `lastWeek` in Spanish locale.](https://github.com/date-fns/date-fns/pull/2753)

- [Fixed translation for October in Hindi locale.](https://github.com/date-fns/date-fns/pull/2729)

- [Fixed Azerbaijani locale to use correct era matchers for `parse`.](https://github.com/date-fns/date-fns/pull/1633)

- [Added the functions that use `weekStartsOn` and `firstWeekContainsDate` that were missing from the `Locale` documentation page.](https://github.com/date-fns/date-fns/pull/2652)

### Changed

- [Changed abbreviation for August from "Ags" to "Agt" in Indonesian locale.](https://github.com/date-fns/date-fns/pull/2658)

### Added

- [Added Irish English locale (`en-IE`).](https://github.com/date-fns/date-fns/pull/2772)

- [Added Arabic locale (`ar`).](https://github.com/date-fns/date-fns/pull/2721) ([#1670](https://github.com/date-fns/date-fns/issues/1670))

- [Added Hong Kong Traditional Chinese locale (zh-HK).](https://github.com/date-fns/date-fns/pull/2686) ([#2684](https://github.com/date-fns/date-fns/issues/2684))

- [Added Egyptian Arabic locale (ar-EG).](https://github.com/date-fns/date-fns/pull/2699)

## v2.25.0 - 2021-10-05

This release is brought to you by @kossnocorp, @gierschv, @fturmel, @redbmk, @mprovenc, @artyom-ivanov and @tan75.

### Added

- [Added Japanese Hiragana locale (`ja-Hira`).](https://github.com/date-fns/date-fns/pull/2663)

- [Added standalone months support to `de` and `de-AT` locales.](https://github.com/date-fns/date-fns/pull/2602)

## v2.24.0 - 2021-09-17

Kudos to [Sasha Koss](http://github.com/kossnocorp), [Lucas Silva](http://github.com/LucasHFS), [Jan Ziemba](http://github.com/janziemba), [Anastasia Kobzar](http://github.com/rikkalo), [Deepak Gupta](http://github.com/Mr-DG-Wick), [Jonas L](http://github.com/jooola), [Kentaro Suzuki](http://github.com/kentaro84207), [Koussay Haj Kacem](http://github.com/essana3), [fturmel](http://github.com/fturmel), [Tan75](http://github.com/tan75) and [Adriaan Callaerts](http://github.com/call-a3) for working on the release.

### Fixed

- [Fixed an edge case in the Slovak locale caused by unescaped character.](https://github.com/date-fns/date-fns/pull/2540) ([#2083](https://github.com/date-fns/date-fns/issues/2083))

### Changed

- [Used `1` instead of `ein` for German `formatDuration` to make it consistent with other locales and formats.](https://github.com/date-fns/date-fns/pull/2576) ([#2505](https://github.com/date-fns/date-fns/issues/2505))

- [Made Norwegian `formatDuration` consistent with other locales by using numeric representation instead of written.](https://github.com/date-fns/date-fns/pull/2631) ([#2469](https://github.com/date-fns/date-fns/issues/2469))

- [Use the word "sekunda" instead of "vteřina" for second in the Czech locale.](https://github.com/date-fns/date-fns/pull/2577)

- [Made Flemish short date format corresponds to the Flemish government.](https://github.com/date-fns/date-fns/pull/2535)

### Added

- [Added `roundingMethod` option to `differenceInHours`, `differenceInMinutes`, `differenceInQuarters`, `differenceInSeconds` and `differenceInWeeks` with `trunc` as the default method.](https://github.com/date-fns/date-fns/pull/2571) ([#2555](https://github.com/date-fns/date-fns/issues/2555))

- [Added new functions: `previousDay`, `previousMonday`, `previousTuesday`, `previousWednesday`, `previousThursday`, `previousFriday`, `previousSaturday` and `previousSunday`.](https://github.com/date-fns/date-fns/pull/2522)

## v2.23.0 - 2021-07-23

Thanks to [Liam Tait](http://github.com/Liam-Tait), [fturmel](http://github.com/fturmel), [Takuya Uehara](http://github.com/indigolain), [Branislav Lazic](http://github.com/BranislavLazic), [Seyyed Morteza Moosavi](http://github.com/smmoosavi), [Felipe Armoni](http://github.com/komyg), [Sasha Koss](http://github.com/kossnocorp), [Michael Mok](http://github.com/pmmmwh), [Tan75](http://github.com/tan75) and [Maxim Topciu](http://github.com/maximtop) for working on the release.

### Changed

- [Improved `nextDay` performance by roughly 50%.](https://github.com/date-fns/date-fns/pull/2524)

- [Added more ordinal formatting to the Japanese locale.](https://github.com/date-fns/date-fns/pull/2471)

### Added

- [Added a new `clamp` function that allows to bound a date to an interval.](https://github.com/date-fns/date-fns/pull/2498)

- [Added Bosnian locale (bs).](https://github.com/date-fns/date-fns/pull/2495)

- [Allowed passing `undefined` in the duration to add and sub functions.](https://github.com/date-fns/date-fns/pull/2515)

## v2.22.1 - 2021-05-28

Thanks to [Sasha Koss](http://github.com/kossnocorp) for working on the release.

### Fixed

- Fixed constant typings. ([#2491](https://github.com/date-fns/date-fns/issues/2491))

## v2.22.0 - 2021-05-28

[Sasha Koss](http://github.com/kossnocorp), [Lucas Silva](http://github.com/LucasHFS), [Lay](http://github.com/brownsugar), [jwbth](http://github.com/jwbth), [fturmel](http://github.com/fturmel), [Tan75](http://github.com/tan75) and [Anastasia Kobzar](http://github.com/rikkalo) worked on this release.

### Fixed

- [Fixed Taiwanese locale to use traditional Chinese and removed unnecessary spaces.](https://github.com/date-fns/date-fns/pull/2436)

- [Fixed Russian locale to use correct long formats.](https://github.com/date-fns/date-fns/pull/2478)

### Added

- [Added 18 new conversion functions](https://github.com/date-fns/date-fns/pull/2433):
  - `daysToWeeks`
  - `hoursToMilliseconds`
  - `hoursToMinutes`
  - `hoursToSeconds`
  - `millisecondsToHours`
  - `millisecondsToMinutes`
  - `millisecondsToSeconds`
  - `minutesToHours`
  - `minutesToMilliseconds`
  - `minutesToSeconds`
  - `monthsToQuarters`
  - `monthsToYears`
  - `quartersToMonths`
  - `quartersToYears`
  - `secondsToHours`
  - `secondsToMilliseconds`
  - `secondsToMinutes`
  - `weeksToDays`
  - `yearsToMonths`
  - `yearsToQuarters`

## v2.21.3 - 2021-05-08

This release is brought to you by [Maxim Topciu](http://github.com/maximtop).

### Fixed

- [Fixed IE11 support by babelifing the shorthand properties.](https://github.com/date-fns/date-fns/pull/2467)

## v2.21.2 - 2021-05-05

Kudos to [Aleksei Korzun](http://github.com/a-korzun), [Maxim Topciu](http://github.com/maximtop), [Jonas L](http://github.com/jooola), [Mohammad ali Ali panah](http://github.com/always-maap) and [Tan75](http://github.com/tan75) for working on the release.

### Fixed

- [`differenceInBusinessDays` now returns `NaN` instead of `Invalid Date` when an invalid argument is passed to the function.](https://github.com/date-fns/date-fns/pull/2414)

- [Fixed `weekStartsOn` in Persian locale.](https://github.com/date-fns/date-fns/pull/2430)

## v2.21.1 - 2021-04-15

Thanks to [Sasha Koss](http://github.com/kossnocorp) for working on the release.

### Fixed

- [Fixed a breaking change introduced by using modern default argument value syntax (see https://github.com/Hacker0x01/react-datepicker/issues/2870).](https://github.com/date-fns/date-fns/pull/2423)

## v2.21.0 - 2021-04-14

This release is brought to you by [Aleksei Korzun](http://github.com/a-korzun), [Tan75](http://github.com/tan75), [Rubens Mariuzzo](http://github.com/rmariuzzo), [Christoph Stenglein](http://github.com/cstenglein) and [Clément Tamisier](http://github.com/ctamisier).

### Fixed

- [Made `formatDistanceStrict` return `12 months` instead of `1 year` when `unit: 'month'`.](https://github.com/date-fns/date-fns/pull/2411)

### Added

- [Added Haitian Creole (`ht`) locale.](https://github.com/date-fns/date-fns/pull/2396)
- [Added Austrian German (`de-AT`) locale.](https://github.com/date-fns/date-fns/pull/2362)

## v2.20.3 - 2021-04-13

Kudos to [fturmel](http://github.com/fturmel) for working on the release.

### Fixed

- [Fixed broken tree-shaking caused by missing links to corresponding ESM.](https://github.com/date-fns/date-fns/pull/2339) ([#2207](https://github.com/date-fns/date-fns/issues/2207))

## v2.20.2 - 2021-04-12

Kudos to [Maxim Topciu](http://github.com/maximtop) for working on the release.

### Fixed

- [Fixed IE11 incompatibility caused by the usage of spread syntax.](https://github.com/date-fns/date-fns/pull/2407) ([#2408](https://github.com/date-fns/date-fns/issues/2408))

## v2.20.1 - 2021-04-09

This release is brought to you by [Sasha Koss](http://github.com/kossnocorp) and [Tan75](http://github.com/tan75).

### Fixed

- Fixed `isDate` Flow typings that we broke in `v2.20.0`.

## v2.20.0 - 2021-04-08

This release is brought to you by [Sasha Koss](http://github.com/kossnocorp), [Maxim Topciu](http://github.com/maximtop), [tu4mo](http://github.com/tu4mo), [Tan75](http://github.com/tan75), [Ardit Dine](http://github.com/arditdine), [Carl Rosell](http://github.com/CarlRosell), [Roman Mahotskyi](http://github.com/enheit), [Mateusz Krzak](http://github.com/mateuszkrzak), [fgottschalk](http://github.com/fgottschalk), [Anastasia Kobzar](http://github.com/rikkalo), [Bilguun Ochirbat](http://github.com/bilguun0203), [Lesha Koss](http://github.com/leshakoss), [YuLe](http://github.com/yuler) and [guyroberts21](http://github.com/guyroberts21).

### Fixed

- [Made `formatDistanceStrict` and `formatDistanceToNowStrict` always return `1 year` instead of `12 months`.](https://github.com/date-fns/date-fns/pull/2391) ([#2388](https://github.com/date-fns/date-fns/issues/2388))

- Fixed `nextDay`, `nextMonday` and `nextTuesday` missing in exports and type definitions. ([#2325](https://github.com/date-fns/date-fns/issues/2325))

- [Fixed a DST bug in `formatDistanceStrict`.](https://github.com/date-fns/date-fns/pull/2329) ([#2307](https://github.com/date-fns/date-fns/issues/2307))

### Added

- [Added new `eachMinuteOfInterval` function.](https://github.com/date-fns/date-fns/pull/2382)

- [Added Albanian (`sq`) locale](https://github.com/date-fns/date-fns/pull/2290)

- [Added Mongolian (`mn`) locale](https://github.com/date-fns/date-fns/pull/1961)

- [Added `nextWednesday`, `nextThursday`, `nextFriday`, `nextSaturday` and `nextSunday`.](https://github.com/date-fns/date-fns/pull/2291)

## v2.19.0 - 2021-03-05

[Tan75](http://github.com/tan75) worked on this release.

### Fixed

- [Assigned the correct `firstWeekContainsDate` value (`4`) for the French locale.](https://github.com/date-fns/date-fns/pull/2273) ([#2148](https://github.com/date-fns/date-fns/issues/2148))

- [Fixed torsdag abbreviation in the Swedish locale.](https://github.com/date-fns/date-fns/pull/2220)

- [Fixed a bug in `differenceInMonths` and `intervalToDuration` that occurs when dealing with the 28th of February.](https://github.com/date-fns/date-fns/pull/2256) ([#2255](https://github.com/date-fns/date-fns/issues/2255))

### Added

- [Added new functions: `nextDay`, `nextMonday` and `nextTuesday` that allows getting the next day of the week, Monday or Tuesday respectively.](https://github.com/date-fns/date-fns/pull/2214)

## v2.18.0 - 2021-03-01

Thanks to [Tan75](http://github.com/tan75) and [Lesha Koss](http://github.com/leshakoss).

### Fixed

- [Fixed documentation missing for `intlFormat`.](https://github.com/date-fns/date-fns/pull/2259) ([#2258](https://github.com/date-fns/date-fns/issues/2258))

- [Fixed date formats in the Latvian locale.](https://github.com/date-fns/date-fns/pull/2205) ([#2202](https://github.com/date-fns/date-fns/issues/2202))

### Added

- [Added support of positive and negative offsets in `parseJSON`.](https://github.com/date-fns/date-fns/pull/2200) ([#2149](https://github.com/date-fns/date-fns/issues/2149))

## [2.17.0] - 2021-02-05

Kudos to [@shaykav](https://github.com/date-fns/date-fns/pull/1952), [@davidgape89](https://github.com/davidgape89), [@rikkalo](https://github.com/rikkalo), [@tan75](https://github.com/tan75), [@talgautb](https://github.com/talgautb), [@owenl131](https://github.com/owenl131), [@kylesezhi](https://github.com/kylesezhi), [@inigoiparragirre](https://github.com/inigoiparragirre), [@gius](https://github.com/gius), [@Endeauvirr](https://github.com/Endeauvirr) and [@frankyston](https://github.com/frankyston).

### Fixed

- [Fixed Russian locale parsing issue](https://github.com/date-fns/date-fns/pull/1950).

- [Fixed `differenceInMonths` for edge cases, such as the end of February dates](https://github.com/date-fns/date-fns/pull/2185).

- [Fixed suffixes for the Kazakh locale](https://github.com/date-fns/date-fns/pull/2010).

- [Fixed `formatDuration` week translation in `pt` and `pt-BR` locales](https://github.com/date-fns/date-fns/pull/2125).

- [Made Japanese locale to use the correct value for the start of the week](https://github.com/date-fns/date-fns/pull/2099).

- [Adjusted date formats in the Basque locale](https://github.com/date-fns/date-fns/pull/2080).

- [Fixed the short and medium date formats in the Czech locale](https://github.com/date-fns/date-fns/pull/2111).

- [Adjusted the Polish translations of `formatDistance`](https://github.com/date-fns/date-fns/pull/2187).

- [Fixed the week's abbreviations in the Brazilian Portuguese](https://github.com/date-fns/date-fns/pull/2170).

### Added

- [Added `intlFormat`](https://github.com/date-fns/date-fns/pull/2172) a lightweight formatting function that uses [Intl API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl). Eventually, it will become the default formatting function, so it's highly recommended for new code.

- [Added `en-ZA` locale](https://github.com/date-fns/date-fns/pull/1952).

- [Added an ability to format lowercase am/pm with `aaa` and `bbb` tokens](https://github.com/date-fns/date-fns/pull/2016).

- [Added ordinal formatting for Japanese year values](https://github.com/date-fns/date-fns/pull/2177/files).

## [2.16.1] - 2020-07-31

Kudos to [@aleksaps](https://github.com/aleksaps), [@leedriscoll](https://github.com/leedriscoll) and [@BanForFun](https://github.com/BanForFun) for pull-requests!

### Fixed

- [Fixed a typo in Scottish Gaelic (gd) locale](https://github.com/date-fns/date-fns/pull/1925).
- [Fixed typos in Serbian Latin locale](https://github.com/date-fns/date-fns/pull/1928).
- [Fixed greek grammar for Saturday on `formatRelative`](https://github.com/date-fns/date-fns/pull/1930).
- Removed locale snapshots from the npm package making it lighter.

## [2.16.0] - 2020-08-27

Kudos to [@jvpelt](https://github.com/jvpelt), [@piotrl](https://github.com/piotrl), [@yotamofek](https://github.com/yotamofek), [@dwaxweiler](https://github.com/dwaxweiler), [@leedriscoll](https://github.com/leedriscoll) and [@bradevans](https://github.com/bradevans) for working on the release. Also thanks to [@PascalHonegger](https://github.com/PascalHonegger), [@pickfire](https://github.com/pickfire), [@TheJaredWilcurt](https://github.com/TheJaredWilcurt), [@SidKH](https://github.com/SidKH) and [@nfantone](https://github.com/nfantone) for improving the documentation.

### Fixed

- [Added correct translations for Welsh `1 minute` and `2 days`](https://github.com/date-fns/date-fns/pull/1903).
- [Fixed `formatRFC3339` formatting timezone offset with minutes](https://github.com/date-fns/date-fns/pull/1890).
- [Added missing locale type definition for `formatDuration`](https://github.com/date-fns/date-fns/pull/1881)
- [Fixed Scottish Gaelic locale issues](https://github.com/date-fns/date-fns/pull/1914).

### Changed

- [Used shorter Hebrew alternative for "about"](https://github.com/date-fns/date-fns/pull/1893).
- [Improved string arguments warning after upgrading to v2](https://github.com/date-fns/date-fns/pull/1910).

### Added

- [Added Luxembourgish (lb) locale](https://github.com/date-fns/date-fns/pull/1900).

## [2.15.0] - 2020-07-17

Thanks to [@belgamo](https://github.com/belgamo), [@Matsuuu](https://github.com/Matsuuu), [@Imballinst](https://github.com/Imballinst), [@arsnyder16](https://github.com/arsnyder16), [@pankajupadhyay29](https://github.com/pankajupadhyay29), [@DCBN](https://github.com/DCBN), [@leedriscoll](https://github.com/leedriscoll), [@gottsohn](https://github.com/gottsohn), [@mukuljainx](https://github.com/mukuljainx) and [@dtriana](https://github.com/dtriana) for working on the release. Also kudos to [@KidkArolis](https://github.com/KidkArolis), [@imgx64](https://github.com/imgx64), [@fjc0k](https://github.com/fjc0k), [@wmonk](https://github.com/wmonk), [@djD-REK](https://github.com/djD-REK), [@dandv](https://github.com/dandv), [@psimk](https://github.com/psimk) and [@brimworks](https://github.com/brimworks) for improving the documentation.

### Fixed

- [Fixed behavior of `addBusinessDays` when input date is a weekend day](https://github.com/date-fns/date-fns/pull/1790).
- [Fixed `parseISO` not returning `Invalid Date` on incorrect string when there are spaces in it](https://github.com/date-fns/date-fns/pull/1791).
- [Fixed `es` round-tripping dates with Wednesday](https://github.com/date-fns/date-fns/pull/1792).
- [Fixed round-trip bug with `d`/`EEEE` ordering in tokens like `PPPPP`](https://github.com/date-fns/date-fns/pull/1795).
- [Fixed issues with parsing values in Japanese](https://github.com/date-fns/date-fns/pull/1807).
- [Fixed Hungarian breaking IE11](https://github.com/date-fns/date-fns/pull/1842).
- [Fixed Spanish accents in Saturday and Wednesday](https://github.com/date-fns/date-fns/pull/1872).

### Changed

- [Improved the message of protected tokens error](https://github.com/date-fns/date-fns/pull/1641).

### Added

- [Added Swiss-French `fr-CH` locale](https://github.com/date-fns/date-fns/pull/1809).
- [Added Flemish `nl-BE` locale](https://github.com/date-fns/date-fns/pull/1812).
- [Added Scottish Gaelic `gd` locale](https://github.com/date-fns/date-fns/pull/1832).
- [Added New Zealand English `en-NZ` locale](https://github.com/date-fns/date-fns/pull/1835).
- [Added `isMatch` function](https://github.com/date-fns/date-fns/pull/1868).

## [2.14.0] - 2020-05-18

Kudos to [@julamb](https://github.com/julamb), [@JacobSoderblom](https://github.com/JacobSoderblom), [@justingrant](http://github.com/justingrant), [@dragunoff](https://github.com/dragunoff), [@jmate0321](https://github.com/jmate0321), [@gbhasha](https://github.com/gbhasha), [@rasck](https://github.com/rasck), [@AlbertoPdRF](https://github.com/AlbertoPdRF), [@sebastianhaberey](https://github.com/sebastianhaberey) and [@giogonzo](https://github.com/giogonzo) for working on the release!

### Fixed

- [Fixed DST issues with `add`, `addDays` and `addMonths`](https://github.com/date-fns/date-fns/pull/1760).
- [Fixed "quarter" translation in the Bulgarian locale](https://github.com/date-fns/date-fns/pull/1763).
- [Fixed `formatDistance` strings in the Hungarian locale](https://github.com/date-fns/date-fns/pull/1765).
- [Fixed Danish month abbreviations](https://github.com/date-fns/date-fns/pull/1774).
- [Fixed parsing of mei in the Dutch locale](https://github.com/date-fns/date-fns/pull/1774).
- [Fixed missing preposition in `formatLong` in the Spanish locale](https://github.com/date-fns/date-fns/pull/1775).
- [Fixed `formatRelative` in the Italian locale](https://github.com/date-fns/date-fns/pull/1777).

### Added

- [Added `eachQuarterOfInterval`](https://github.com/date-fns/date-fns/pull/1715).
- [Added Basque (`eu`) locale](https://github.com/date-fns/date-fns/pull/1759).
- [Added Indian English (`en-IN`) locale](https://github.com/date-fns/date-fns/pull/1767).
- [Added `eachHourOfInterval`](https://github.com/date-fns/date-fns/pull/1776).

## [2.13.0] - 2020-05-06

Thanks to [@JorenVos](https://github.com/JorenVos), [@developergouli](https://github.com/developergouli), [@rhlowe](https://github.com/rhlowe) and [@justingrant](http://github.com/justingrant) for working on the release!

### Fixed

- [Fixed mei abbreviation in the Dutch locale](https://github.com/date-fns/date-fns/pull/1752).
- [Fixed `differenceInDays` DST behavior broken in 2.12.0](https://github.com/date-fns/date-fns/pull/1754).

### Added

- [Added Kannada locale support](https://github.com/date-fns/date-fns/pull/1747).
- [Added `formatISODuration` function](https://github.com/date-fns/date-fns/pull/1713).
- [Added `intervalToDuration` function](https://github.com/date-fns/date-fns/pull/1713).

## [2.12.0] - 2020-04-09

Kudos to [@leshakoss](http://github.com/leshakoss), [@skyuplam](https://github.com/skyuplam), [@so99ynoodles](https://github.com/so99ynoodles), [@dkozickis](https://github.com/dkozickis), [@belgamo](https://github.com/belgamo), [@akgondber](https://github.com/akgondber), [@dcousens](https://github.com/dcousens) and [@BoomDev](https://github.com/BoomDev) for working on the release!

### Fixed

- [Fixed minulý štvrtok in Slovak locale](https://github.com/date-fns/date-fns/pull/1701).
- Fixed date ordinalNumber for [ja/zh-CN/zh-TW](https://github.com/date-fns/date-fns/pull/1690) and [ko](https://github.com/date-fns/date-fns/pull/1696).
- [Fixed quarters parsing](https://github.com/date-fns/date-fns/pull/1694).
- [Fixed `setDay` with `weekStartsOn` != 0](https://github.com/date-fns/date-fns/pull/1639).
- [Fixed differenceInDays across DST](https://github.com/date-fns/date-fns/pull/1630).
- [Fixed required arguments exception message](https://github.com/date-fns/date-fns/pull/1674).

### Added

- [Added new function `formatDistanceToNowStrict`](https://github.com/date-fns/date-fns/pull/1679).

## [2.11.1] - 2020-03-26

### Fixed

- Rebuilt TypeScript and flow types.

## [2.11.0] - 2020-03-13

Kudos to [@oakhan3](https://github.com/oakhan3), [@Mukhammadali](https://github.com/Mukhammadali), [@altrim](https://github.com/altrim), [@leepowellcouk](https://github.com/leepowellcouk), [@amatzon](@https://github.com/amatzon), [@bryanMt](https://github.com/bryanMt), [@kalekseev](https://github.com/kalekseev), [@eugene-platov](https://github.com/eugene-platov) and [@tjrobinson](https://github.com/tjrobinson) for working on the release.

### Fixed

- [Fixed a bug in `differenceInYears` causing incorrect results when the left date is a leap day](https://github.com/date-fns/date-fns/pull/1654).
- [Fixed `parseISO` to work correctly around time shift dates](https://github.com/date-fns/date-fns/pull/1667).
- [Fixed `format` to work correctly with GMT-0752/GMT-0456 and similar timezones](https://github.com/date-fns/date-fns/pull/1666).

### Changed

- [Changed `getDay` typings to return `0|1|2|3|4|5|6` instead of `number`](https://github.com/date-fns/date-fns/pull/1668).
- [Improved Chinese locale](https://github.com/date-fns/date-fns/pull/1664):
  - Change date format to meet the national standard (GB/T 7408-2005).
  - Improve `ordinalNumber` function behavior.
  - Add prefix in `formatRelative` depending on if it's a current week or not.

### Added

- [Added Uzbek `uz` locale](https://github.com/date-fns/date-fns/pull/1648).
- [Updated Macedonian locale for v2](https://github.com/date-fns/date-fns/pull/1649).
- [Added Maltese `mt` locale](https://github.com/date-fns/date-fns/pull/1658).

## [2.10.0] - 2020-02-25

### Fixed

- [Fixed `formatISO` when formatting time with timezones with minute offsets > 0](https://github.com/date-fns/date-fns/pull/1599). Kudos to [@dcRUSTy](https://github.com/dcRUSTy).

### Fixed

- Fixed a bug in setDay when using weekStartsOn that is not 0

### Added

- [Added `weeks` to `Duration`](https://github.com/date-fns/date-fns/pull/1592).
- [Added `weeks` support to `add` and `sub`](https://github.com/date-fns/date-fns/pull/1592).
- [Added details message in `throwProtectedError`](https://github.com/date-fns/date-fns/pull/1592).

## [2.9.0] - 2020-01-08

Thanks to [@mborgbrant](https://github.com/mborgbrant), [@saintplay](https://github.com/saintplay), [@mrenty](https://github.com/mrenty), [@kibertoad](https://github.com/kibertoad), [@levibuzolic](https://github.com/levibuzolic), [@Anshuman71](https://github.com/Anshuman71), [@talgautb](https://github.com/talgautb), [@filipjuza](https://github.com/filipjuza), [@tobyzerner](https://github.com/tobyzerner), [@emil9453](https://github.com/emil9453), [@fintara](https://github.com/fintara), [@pascaliske](https://github.com/pascaliske), [@rramiachraf](https://github.com/rramiachraf), [@marnusw](https://github.com/marnusw) and [@Imballinst](https://github.com/Imballinst) for working on the release.

### Fixed

- [Fixed a bug with addBusinessDays returning the Tuesday when adding 1 day on weekends. Now it returns the Monday](https://github.com/date-fns/date-fns/pull/1588).
- [Added missing timezone to `formatISO`](https://github.com/date-fns/date-fns/pull/1576).
- [Removed dots from short day period names in the Kazakh locale](https://github.com/date-fns/date-fns/pull/1512).
- [Fixed typo in formatDistance in the Czech locale](https://github.com/date-fns/date-fns/pull/1540).
- [Fixed shortenings in the Bulgarian locale](https://github.com/date-fns/date-fns/pull/1560).
- [Fixed regex for the May in the Portuguese locale](https://github.com/date-fns/date-fns/pull/1565).

### Added

- [Added `eachMonthOfInterval` and `eachYearOfInterval`](https://github.com/date-fns/date-fns/pull/618).
- [Added `inclusive` option to `areIntervalsOverlapping](https://github.com/date-fns/date-fns/pull/643).
- [Added `isExists` function that checks if the given date is exists](https://github.com/date-fns/date-fns/pull/682).
- [Added `add` function to add seconds, minutes, hours, weeks, years in single call](https://github.com/date-fns/date-fns/pull/1581).
- [Added `sub` function, the opposite of `add`](https://github.com/date-fns/date-fns/pull/1583).
- [Added `Duration` type used in `add` and `sub`](https://github.com/date-fns/date-fns/pull/1583).
- [Added Azerbaijani (az) locale](https://github.com/date-fns/date-fns/pull/1547).
- [Added Moroccan Arabic (ar-MA) locale](https://github.com/date-fns/date-fns/pull/1578).

### Changed

- [Reduced the total minified build size by 1Kb/4%](https://github.com/date-fns/date-fns/pull/1563).
- [Made all properties in `Locale` type optional](https://github.com/date-fns/date-fns/pull/1542).
- [Added missing properties to `Locale` type](https://github.com/date-fns/date-fns/pull/1542).
- [Add the locale code to `Locale` type](https://github.com/date-fns/date-fns/pull/1580).
- [Added support of space time separator to `parseJSON`](https://github.com/date-fns/date-fns/pull/1579).
- [Allowed up to 7 digits in milliseconds in `parseJSON`](https://github.com/date-fns/date-fns/pull/1579).

## [2.8.1] - 2019-11-22

Thanks to [@Imballinst](https://github.com/Imballinst) for the bug fix!

### Fixed

- [Add colon between the hour and minutes for `formatRFC3339`](https://github.com/date-fns/date-fns/pull/1549). [See #1548](https://github.com/date-fns/date-fns/issues/1548).

## [2.8.0] - 2019-11-19

Kudos to [@NaridaL](https://github.com/NaridaL), [@Zyten](https://github.com/Zyten), [@Imballinst](https://github.com/Imballinst), [@leshakoss](https://github.com/leshakoss) and [@Neorth](https://github.com/Neorth) for working on the release.

### Fixed

- [Remove the next week preposition in the Swedish locale](https://github.com/date-fns/date-fns/pull/1538).

### Added

- [Added Malay (ms) locale](https://github.com/date-fns/date-fns/pull/1537).
- [Added `formatISO`, `formatISO9075`, `formatRFC3339`, and `formatRFC7231` functions](https://github.com/date-fns/date-fns/pull/1536).

## [2.7.0] - 2019-11-07

Thanks to [@mzgajner](https://github.com/mzgajner), [@NaridaL](https://github.com/NaridaL), [@Zyten](https://github.com/Zyten), [@leshakoss](https://github.com/leshakoss), [@fintara](https://github.com/fintara), [@kpr-hellofresh](https://github.com/kpr-hellofresh) for contributing to the release.

### Fixed

- [Fixed a mistake in the Slovenian locale](https://github.com/date-fns/date-fns/pull/1529).
- [Fixed incorrect behavior of `parseISO` in Firefox caused by differences in `getTimezoneOffset`](https://github.com/date-fns/date-fns/pull/1495).

### Changed

- [Make object arguments types more elaborate in Flow type definitions](https://github.com/date-fns/date-fns/pull/1519).
- [Get rid of deprecated Function in Flow type definitions](https://github.com/date-fns/date-fns/pull/1520).
- [Allow `parseJSON` to accept strings without trailing 'Z' symbol and with up to 6 digits in the milliseconds' field](https://github.com/date-fns/date-fns/pull/1499).

### Added

- [Added Bulgarian (bg) locale](https://github.com/date-fns/date-fns/pull/1522).

## [2.6.0] - 2019-10-22

Kudos to [@marnusw](https://github.com/marnusw), [@cdrikd](https://github.com/cdrikd) and [@rogyvoje](https://github.com/rogyvoje) for working on the release!

### Added

- [Added `parseJSON` - lightweight function (just 411 B) that parses dates formatted with `toJSON`](https://github.com/date-fns/date-fns/pull/1463).
- [Added the language code to each locale](https://github.com/date-fns/date-fns/pull/1489).
- [Added `subBusinessDays` function](https://github.com/date-fns/date-fns/pull/1491).
- [Added both Serbian - cyrillic (sr) and latin (sr-Latn) locales](https://github.com/date-fns/date-fns/pull/1494).

## [2.5.1] - 2019-10-18

Thanks to [@mitchellbutler](https://github.com/mitchellbutler) for the bug fix!

### Fixed

- [Fixed infinite loop in `addBusinessDays`](https://github.com/date-fns/date-fns/pull/1486).

## [2.5.0] - 2019-10-16

Kudos to [@dkozickis](https://github.com/dkozickis), [@drugoi](https://github.com/drugoi), [@kranthilakum](https://github.com/kranthilakum), [@102](https://github.com/102), [@gpetrioli](https://github.com/gpetrioli) and [@JulienMalige](https://github.com/JulienMalige) for making the release happen.

### Fixed

- [Fixed compatibility with IE11 by removing `findIndex` from the code](https://github.com/date-fns/date-fns/pull/1457).
- [Fixed Greek locale patterns](https://github.com/date-fns/date-fns/pull/1480).

### Added

- [Added Kazakh (kk) locale](https://github.com/date-fns/date-fns/pull/1460).
- [Added Telugu (te) locale](https://github.com/date-fns/date-fns/pull/1464).
- [Added Canadian French (fr-CA) locale](https://github.com/date-fns/date-fns/issues/1465).
- [Added Australian English (en-AU) locale](https://github.com/date-fns/date-fns/pull/1470).
- [Exported `Interval` and `Locale` types from Flow typings](https://github.com/date-fns/date-fns/pull/1475).

## [2.4.1] - 2019-09-28

Thanks to [@mrclayman](https://github.com/mrclayman) for reporting the issue and [@leshakoss](https://github.com/leshakoss) for fixing it.

### Fixed

- [Fixed am/pm mixup in the Czech locale](https://github.com/date-fns/date-fns/pull/1453).

## [2.4.0] - 2019-09-27

This release is brought to you by these amazing people: [@lovelovedokidoki](https://github.com/lovelovedokidoki), [@alexigityan](https://github.com/alexigityan), [@kalekseev](https://github.com/kalekseev) and [@andybangs](https://github.com/andybangs). You rock!

### Fixed

- [Fixed Vietnamese parsing patterns](https://github.com/date-fns/date-fns/pull/1445).
- [Fixed Czech parsing regexes](https://github.com/date-fns/date-fns/pull/1446).
- [Fixed offset for Eastern Hemisphere in `parseISO`](https://github.com/date-fns/date-fns/pull/1450).

### Added

- [Added Armenian locale support](https://github.com/date-fns/date-fns/pull/1448).

## [2.3.0] - 2019-09-24

Huge thanks to [@lovelovedokidoki](https://github.com/lovelovedokidoki) who improved 8 (!) locales in an unstoppable open-source rampage and [@VesterDe](https://github.com/VesterDe) for fixing Slovenian locale 👏

### Fixed

- [Fixed the translation of "yesterday" in the Slovenian locale](https://github.com/date-fns/date-fns/pull/1420).
- [Fixed French parsing issues with June and August](https://github.com/date-fns/date-fns/pull/1430).
- [Improved Turkish parsing](https://github.com/date-fns/date-fns/pull/1432).
- [Fixed "March" in Dutch parsing patterns](https://github.com/date-fns/date-fns/pull/1433).
- [Fixed Hindi parsing patterns](https://github.com/date-fns/date-fns/pull/1434).

### Added

- [Added Finnish matching patterns](https://github.com/date-fns/date-fns/pull/1425).
- [Accept abbreviated March, June, July in Norwegian locales](https://github.com/date-fns/date-fns/pull/1431).
- [Added parsing for Greek months with long formatting](https://github.com/date-fns/date-fns/pull/1435).

## [2.2.1] - 2019-09-12

Kudos to date-fns contributors: [@mzgajner](https://github.com/mzgajner), [@sibiraj-s](https://github.com/sibiraj-s), [@mukeshmandiwal](https://github.com/mukeshmandiwal), [@SneakyFish5](https://github.com/SneakyFish5) and [@CarterLi](https://github.com/CarterLi).

### Added

- [Added new `set` function](https://github.com/date-fns/date-fns/pull/1398).
- [Updated Slovenian (sl) locale for v2](https://github.com/date-fns/date-fns/pull/1418).
- [Added Tamil (ta) locale](https://github.com/date-fns/date-fns/pull/1411).
- [Added Hindi (hi) locale](https://github.com/date-fns/date-fns/pull/1409).
- [Added support of `\n` in `format`, `lightFormat` and `parse`](https://github.com/date-fns/date-fns/pull/1417).

## [2.1.0] - 2019-09-06

Thanks to date-fns contributors: [@ManadayM](https://github.com/ManadayM), [@illuminist](https://github.com/illuminist), [@visualfanatic](https://github.com/visualfanatic), [@vsaarinen](https://github.com/vsaarinen) and at last but not the least [@leshakoss](https://github.com/leshakoss)!

### Fixed

- [Set start of the week to Sunday for Thai locale](https://github.com/date-fns/date-fns/pull/1402).
- [Fixed month matching in Polish locale](https://github.com/date-fns/date-fns/pull/1404).
- [Fixed `eachWeekendOfInterval` skipping the first date in the supplied interval](https://github.com/date-fns/date-fns/pull/1407).

### Added

- [Added Gujarati locale](https://github.com/date-fns/date-fns/pull/1400).

## [2.0.1] - 2019-08-23

### Fixed

- [Fix](https://github.com/date-fns/date-fns/pull/1046) `getWeekOfMonth` with `options.weekStartsOn` set to 1 [not working for Sundays](https://github.com/date-fns/date-fns/issues/1040). Kudos to [@waseemahmad31](https://github.com/waseemahmad31)!

## [2.0.0] - 2019-08-20

If you're upgrading from v2 alpha or beta, [see the pre-release changelog](https://gist.github.com/kossnocorp/a307a464760b405bb78ef5020a4ab136).

### Fixed

- Fixed the `toDate` bug occurring when parsing ISO-8601 style dates (but not valid ISO format)
  with a trailing Z (e.g `2012-01Z`), it returned Invalid Date for FireFox/IE11 [#510](https://github.com/date-fns/date-fns/issue/510)

- Fixed `differenceIn...` functions returning negative zero in some cases:
  [#692](https://github.com/date-fns/date-fns/issues/692)

- `isDate` now works properly with dates passed across iframes [#754](https://github.com/date-fns/date-fns/pull/754).

- Fixed a few bugs that appeared in timezones with offsets that include seconds (e.g. GMT+00:57:44).
  See PR [#789](https://github.com/date-fns/date-fns/pull/789).

- [Fixed DST issue](https://github.com/date-fns/date-fns/pull/1003). See [#972](https://github.com/date-fns/date-fns/issues/972) and [#992](https://github.com/date-fns/date-fns/issues/992) for more details.

- Fixed DST issue in `eachDayOfInterval` that caused time in the days
  after DST change to have the shift as well.

- Fixed bug in Galician locale caused by incorrect usage of `getHours`
  instead of `getUTCHours`.

### Changed

- **BREAKING**: now functions don't accept string arguments, but only
  numbers or dates. When a string is passed, it will result in
  an unexpected result (`Invalid Date`, `NaN`, etc).

  From now on a string should be parsed using `parseISO` (ISO 8601)
  or `parse`.

  In v1 we've used `new Date()` to parse strings, but it resulted in many
  hard-to-track bugs caused by inconsistencies in different browsers.
  To address that we've implemented our ISO 8601 parser but that made
  library to significantly grow in size. To prevent inevitable bugs
  and keep the library tiny, we made this trade-off.

  See [this post](https://blog.date-fns.org/post/we-cut-date-fns-v2-minimal-build-size-down-to-300-bytes-and-now-its-the-smallest-date-library-18f2nvh2z0yal) for more details.

  ```javascript
  // Before v2.0.0
  addDays('2016-01-01', 1)

  // v2.0.0 onward
  addDays(parseISO('2016-01-01'), 1)
  ```

- **BREAKING**: new format string API for `format` function
  which is based on [Unicode Technical Standard #35](https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table).
  See [this post](https://blog.date-fns.org/post/unicode-tokens-in-date-fns-v2-sreatyki91jg) for more details.

  | Unit                            | v2 Pattern | v1 Pattern | Result examples                   |
  | ------------------------------- | ---------- | ---------- | --------------------------------- |
  | Era                             | G..GGG     |            | AD, BC                            |
  |                                 | GGGG       |            | Anno Domini, Before Christ        |
  |                                 | GGGGG      |            | A, B                              |
  | Calendar year                   | y          |            | 44, 1, 1900, 2017                 |
  |                                 | yo         |            | 44th, 1st, 0th, 17th              |
  |                                 | yy         | YY         | 44, 01, 00, 17                    |
  |                                 | yyy        |            | 044, 001, 1900, 2017              |
  |                                 | yyyy       | YYYY       | 0044, 0001, 1900, 2017            |
  |                                 | yyyyy      |            | ...                               |
  | Local week-numbering year       | Y          |            | 44, 1, 1900, 2017                 |
  |                                 | Yo         |            | 44th, 1st, 1900th, 2017th         |
  |                                 | YY         |            | 44, 01, 00, 17                    |
  |                                 | YYY        |            | 044, 001, 1900, 2017              |
  |                                 | YYYY       |            | 0044, 0001, 1900, 2017            |
  |                                 | YYYYY      |            | ...                               |
  | ISO week-numbering year         | R          |            | -43, 0, 1, 1900, 2017             |
  |                                 | RR         | GG         | -43, 00, 01, 1900, 2017           |
  |                                 | RRR        |            | -043, 000, 001, 1900, 2017        |
  |                                 | RRRR       | GGGG       | -0043, 0000, 0001, 1900, 2017     |
  |                                 | RRRRR      |            | ...                               |
  | Extended year                   | u          |            | -43, 0, 1, 1900, 2017             |
  |                                 | uu         |            | -43, 01, 1900, 2017               |
  |                                 | uuu        |            | -043, 001, 1900, 2017             |
  |                                 | uuuu       |            | -0043, 0001, 1900, 2017           |
  |                                 | uuuuu      |            | ...                               |
  | Quarter (formatting)            | Q          |            | 1, 2, 3, 4                        |
  |                                 | Qo         |            | 1st, 2nd, 3rd, 4th                |
  |                                 | QQ         |            | 01, 02, 03, 04                    |
  |                                 | QQQ        |            | Q1, Q2, Q3, Q4                    |
  |                                 | QQQQ       |            | 1st quarter, 2nd quarter, ...     |
  |                                 | QQQQQ      |            | 1, 2, 3, 4                        |
  | Quarter (stand-alone)           | q          | Q          | 1, 2, 3, 4                        |
  |                                 | qo         | Qo         | 1st, 2nd, 3rd, 4th                |
  |                                 | qq         |            | 01, 02, 03, 04                    |
  |                                 | qqq        |            | Q1, Q2, Q3, Q4                    |
  |                                 | qqqq       |            | 1st quarter, 2nd quarter, ...     |
  |                                 | qqqqq      |            | 1, 2, 3, 4                        |
  | Month (formatting)              | M          |            | 1, 2, ..., 12                     |
  |                                 | Mo         |            | 1st, 2nd, ..., 12th               |
  |                                 | MM         |            | 01, 02, ..., 12                   |
  |                                 | MMM        |            | Jan, Feb, ..., Dec                |
  |                                 | MMMM       |            | January, February, ..., December  |
  |                                 | MMMMM      |            | J, F, ..., D                      |
  | Month (stand-alone)             | L          | M          | 1, 2, ..., 12                     |
  |                                 | Lo         |            | 1st, 2nd, ..., 12th               |
  |                                 | LL         | MM         | 01, 02, ..., 12                   |
  |                                 | LLL        | MMM        | Jan, Feb, ..., Dec                |
  |                                 | LLLL       | MMMM       | January, February, ..., December  |
  |                                 | LLLLL      |            | J, F, ..., D                      |
  | Local week of year              | w          |            | 1, 2, ..., 53                     |
  |                                 | wo         |            | 1st, 2nd, ..., 53th               |
  |                                 | ww         |            | 01, 02, ..., 53                   |
  | ISO week of year                | I          | W          | 1, 2, ..., 53                     |
  |                                 | Io         | Wo         | 1st, 2nd, ..., 53th               |
  |                                 | II         | WW         | 01, 02, ..., 53                   |
  | Day of month                    | d          | D          | 1, 2, ..., 31                     |
  |                                 | do         | Do         | 1st, 2nd, ..., 31st               |
  |                                 | dd         | DD         | 01, 02, ..., 31                   |
  | Day of year                     | D          | DDD        | 1, 2, ..., 365, 366               |
  |                                 | Do         | DDDo       | 1st, 2nd, ..., 365th, 366th       |
  |                                 | DD         |            | 01, 02, ..., 365, 366             |
  |                                 | DDD        | DDDD       | 001, 002, ..., 365, 366           |
  |                                 | DDDD       |            | ...                               |
  | Day of week (formatting)        | E..EEE     |            | Mon, Tue, Wed, ..., Su            |
  |                                 | EEEE       |            | Monday, Tuesday, ..., Sunday      |
  |                                 | EEEEE      |            | M, T, W, T, F, S, S               |
  |                                 | EEEEEE     |            | Mo, Tu, We, Th, Fr, Sa, Su        |
  | ISO day of week (formatting)    | i          | E          | 1, 2, 3, ..., 7                   |
  |                                 | io         | do         | 1st, 2nd, ..., 7th                |
  |                                 | ii         |            | 01, 02, ..., 07                   |
  |                                 | iii        | ddd        | Mon, Tue, Wed, ..., Su            |
  |                                 | iiii       | dddd       | Monday, Tuesday, ..., Sunday      |
  |                                 | iiiii      |            | M, T, W, T, F, S, S               |
  |                                 | iiiiii     | dd         | Mo, Tu, We, Th, Fr, Sa, Su        |
  | Local day of week (formatting)  | e          |            | 2, 3, 4, ..., 1                   |
  |                                 | eo         |            | 2nd, 3rd, ..., 1st                |
  |                                 | ee         |            | 02, 03, ..., 01                   |
  |                                 | eee        |            | Mon, Tue, Wed, ..., Su            |
  |                                 | eeee       |            | Monday, Tuesday, ..., Sunday      |
  |                                 | eeeee      |            | M, T, W, T, F, S, S               |
  |                                 | eeeeee     |            | Mo, Tu, We, Th, Fr, Sa, Su        |
  | Local day of week (stand-alone) | c          |            | 2, 3, 4, ..., 1                   |
  |                                 | co         |            | 2nd, 3rd, ..., 1st                |
  |                                 | cc         |            | 02, 03, ..., 01                   |
  |                                 | ccc        |            | Mon, Tue, Wed, ..., Su            |
  |                                 | cccc       |            | Monday, Tuesday, ..., Sunday      |
  |                                 | ccccc      |            | M, T, W, T, F, S, S               |
  |                                 | cccccc     |            | Mo, Tu, We, Th, Fr, Sa, Su        |
  | AM, PM                          | a..aaa     | A          | AM, PM                            |
  |                                 | aaaa       | aa         | a.m., p.m.                        |
  |                                 | aaaaa      |            | a, p                              |
  | AM, PM, noon, midnight          | b..bbb     |            | AM, PM, noon, midnight            |
  |                                 | bbbb       |            | a.m., p.m., noon, midnight        |
  |                                 | bbbbb      |            | a, p, n, mi                       |
  | Flexible day period             | B..BBB     |            | at night, in the morning, ...     |
  |                                 | BBBB       |            | at night, in the morning, ...     |
  |                                 | BBBBB      |            | at night, in the morning, ...     |
  | Hour [1-12]                     | h          |            | 1, 2, ..., 11, 12                 |
  |                                 | ho         |            | 1st, 2nd, ..., 11th, 12th         |
  |                                 | hh         |            | 01, 02, ..., 11, 12               |
  | Hour [0-23]                     | H          |            | 0, 1, 2, ..., 23                  |
  |                                 | Ho         |            | 0th, 1st, 2nd, ..., 23rd          |
  |                                 | HH         |            | 00, 01, 02, ..., 23               |
  | Hour [0-11]                     | K          |            | 1, 2, ..., 11, 0                  |
  |                                 | Ko         |            | 1st, 2nd, ..., 11th, 0th          |
  |                                 | KK         |            | 1, 2, ..., 11, 0                  |
  | Hour [1-24]                     | k          |            | 24, 1, 2, ..., 23                 |
  |                                 | ko         |            | 24th, 1st, 2nd, ..., 23rd         |
  |                                 | kk         |            | 24, 01, 02, ..., 23               |
  | Minute                          | m          |            | 0, 1, ..., 59                     |
  |                                 | mo         |            | 0th, 1st, ..., 59th               |
  |                                 | mm         |            | 00, 01, ..., 59                   |
  | Second                          | s          |            | 0, 1, ..., 59                     |
  |                                 | so         |            | 0th, 1st, ..., 59th               |
  |                                 | ss         |            | 00, 01, ..., 59                   |
  | Fraction of second              | S          |            | 0, 1, ..., 9                      |
  |                                 | SS         |            | 00, 01, ..., 99                   |
  |                                 | SSS        |            | 000, 0001, ..., 999               |
  |                                 | SSSS       |            | ...                               |
  | Timezone (ISO-8601 w/ Z)        | X          |            | -08, +0530, Z                     |
  |                                 | XX         |            | -0800, +0530, Z                   |
  |                                 | XXX        |            | -08:00, +05:30, Z                 |
  |                                 | XXXX       |            | -0800, +0530, Z, +123456          |
  |                                 | XXXXX      |            | -08:00, +05:30, Z, +12:34:56      |
  | Timezone (ISO-8601 w/o Z)       | x          |            | -08, +0530, +00                   |
  |                                 | xx         | ZZ         | -0800, +0530, +0000               |
  |                                 | xxx        | Z          | -08:00, +05:30, +00:00            |
  |                                 | xxxx       |            | -0800, +0530, +0000, +123456      |
  |                                 | xxxxx      |            | -08:00, +05:30, +00:00, +12:34:56 |
  | Timezone (GMT)                  | O...OOO    |            | GMT-8, GMT+5:30, GMT+0            |
  |                                 | OOOO       |            | GMT-08:00, GMT+05:30, GMT+00:00   |
  | Timezone (specific non-locat.)  | z...zzz    |            | GMT-8, GMT+5:30, GMT+0            |
  |                                 | zzzz       |            | GMT-08:00, GMT+05:30, GMT+00:00   |
  | Seconds timestamp               | t          | X          | 512969520                         |
  |                                 | tt         |            | ...                               |
  | Milliseconds timestamp          | T          | x          | 512969520900                      |
  |                                 | TT         |            | ...                               |
  | Long localized date             | P          |            | 5/29/53                           |
  |                                 | PP         |            | May 29, 1453                      |
  |                                 | PPP        |            | May 29th, 1453                    |
  |                                 | PPPP       |            | Sunday, May 29th, 1453            |
  | Long localized time             | p          |            | 12:00 AM                          |
  |                                 | pp         |            | 12:00:00 AM                       |
  |                                 | ppp        |            | 12:00:00 AM GMT+2                 |
  |                                 | pppp       |            | 12:00:00 AM GMT+02:00             |
  | Combination of date and time    | Pp         |            | 5/29/53, 12:00 AM                 |
  |                                 | PPpp       |            | May 29, 1453, 12:00 AM            |
  |                                 | PPPppp     |            | May 29th, 1453 at ...             |
  |                                 | PPPPpppp   |            | Sunday, May 29th, 1453 at ...     |

  Characters are now escaped using single quote symbols (`'`) instead of square brackets.
  `format` now throws RangeError if it encounters an unescaped latin character
  that isn't a valid formatting token.

  To use `YY` and `YYYY` tokens that represent week-numbering years,
  you should set `useAdditionalWeekYearTokens` option:

  ```javascript
  format(Date.now(), 'YY', { useAdditionalWeekYearTokens: true })
  //=> '86'
  ```

  To use `D` and `DD` tokens which represent days of the year,
  set `useAdditionalDayOfYearTokens` option:

  ```javascript
  format(Date.now(), 'D', { useAdditionalDayOfYearTokens: true })
  //=> '364'
  ```

- **BREAKING**: function submodules now use camelCase naming schema:

  ```javascript
  // Before v2.0.0
  import differenceInCalendarISOYears from 'date-fns/difference_in_calendar_iso_years'

  // v2.0.0 onward
  import differenceInCalendarISOYears from 'date-fns/differenceInCalendarISOYears'
  ```

- **BREAKING**: min and max functions now accept an array of dates
  rather than spread arguments.

  ```javascript
  // Before v2.0.0
  var date1 = new Date(1989, 6 /* Jul */, 10)
  var date2 = new Date(1987, 1 /* Feb */, 11)

  var minDate = min(date1, date2)
  var maxDate = max(date1, date2)

  // v2.0.0 onward:
  var dates = [new Date(1989, 6 /* Jul */, 10), new Date(1987, 1 /* Feb */, 11)]

  var minDate = min(dates)
  var maxDate = max(dates)
  ```

- **BREAKING**: make the second argument of `format` required for the sake of explicitness.

  ```javascript
  // Before v2.0.0
  format(new Date(2016, 0, 1))

  // v2.0.0 onward
  format(new Date(2016, 0, 1), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx")
  ```

- **BREAKING** renamed ISO week-numbering year helpers:

  - `addISOYears` → `addISOWeekYears`
  - `differenceInCalendarISOYears` → `differenceInCalendarISOWeekYears`
  - `differenceInISOYears` → `differenceInISOWeekYears`
  - `endOfISOYear` → `endOfISOWeekYear`
  - `getISOYear` → `getISOWeekYear`
  - `isSameISOYear` → `isSameISOWeekYear`
  - `lastDayOfISOYear` → `lastDayOfISOWeekYear`
  - `setISOYear` → `setISOWeekYear`
  - `subISOYears` → `subISOWeekYears`

  i.e. "ISO year" renamed to "ISO week year", which is short for
  [ISO week-numbering year](https://en.wikipedia.org/wiki/ISO_week_date).
  It makes them consistent with locale-dependent week-numbering year helpers,
  e.g., `startOfWeekYear`.

- **BREAKING**: functions renamed:

  - `areRangesOverlapping` → `areIntervalsOverlapping`
  - `eachDay` → `eachDayOfInterval`
  - `getOverlappingDaysInRanges` → `getOverlappingDaysInIntervals`
  - `isWithinRange` → `isWithinInterval`

  This change was made to mirror the use of the word "interval" in standard ISO 8601:2004 terminology:

  ```
  2.1.3
  time interval
  part of the time axis limited by two instants
  ```

  Also these functions now accept an object with `start` and `end` properties
  instead of two arguments as an interval. All these functions
  throw `RangeError` if the start of the interval is after its end
  or if any date in the interval is `Invalid Date`.

  ```javascript
  // Before v2.0.0

  areRangesOverlapping(
    new Date(2014, 0, 10),
    new Date(2014, 0, 20),
    new Date(2014, 0, 17),
    new Date(2014, 0, 21)
  )

  eachDay(new Date(2014, 0, 10), new Date(2014, 0, 20))

  getOverlappingDaysInRanges(
    new Date(2014, 0, 10),
    new Date(2014, 0, 20),
    new Date(2014, 0, 17),
    new Date(2014, 0, 21)
  )

  isWithinRange(
    new Date(2014, 0, 3),
    new Date(2014, 0, 1),
    new Date(2014, 0, 7)
  )

  // v2.0.0 onward

  areIntervalsOverlapping(
    { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },
    { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }
  )

  eachDayOfInterval({
    start: new Date(2014, 0, 10),
    end: new Date(2014, 0, 20),
  })

  getOverlappingDaysInIntervals(
    { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },
    { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }
  )

  isWithinInterval(new Date(2014, 0, 3), {
    start: new Date(2014, 0, 1),
    end: new Date(2014, 0, 7),
  })
  ```

- **BREAKING**: functions renamed:

  - `distanceInWords` → `formatDistance`
  - `distanceInWordsStrict` → `formatDistanceStrict`
  - `distanceInWordsToNow` → `formatDistanceToNow`

  to make them consistent with `format` and `formatRelative`.

- **BREAKING**: The order of arguments of `distanceInWords` and `distanceInWordsStrict`
  is swapped to make them consistent with `differenceIn...` functions.

  ```javascript
  // Before v2.0.0

  distanceInWords(
    new Date(1986, 3, 4, 10, 32, 0),
    new Date(1986, 3, 4, 11, 32, 0),
    { addSuffix: true }
  ) //=> 'in about 1 hour'

  // v2.0.0 onward

  formatDistance(
    new Date(1986, 3, 4, 11, 32, 0),
    new Date(1986, 3, 4, 10, 32, 0),
    { addSuffix: true }
  ) //=> 'in about 1 hour'
  ```

- **BREAKING**: `partialMethod` option in `formatDistanceStrict` is renamed to `roundingMethod`.

  ```javascript
  // Before v2.0.0

  distanceInWordsStrict(
    new Date(1986, 3, 4, 10, 32, 0),
    new Date(1986, 3, 4, 10, 33, 1),
    { partialMethod: 'ceil' }
  ) //=> '2 minutes'

  // v2.0.0 onward

  formatDistanceStrict(
    new Date(1986, 3, 4, 10, 33, 1),
    new Date(1986, 3, 4, 10, 32, 0),
    { roundingMethod: 'ceil' }
  ) //=> '2 minutes'
  ```

- **BREAKING**: in `formatDistanceStrict`, if `roundingMethod` is not specified,
  it now defaults to `round` instead of `floor`.

- **BREAKING**: `unit` option in `formatDistanceStrict` now accepts one of the strings:
  'second', 'minute', 'hour', 'day', 'month' or 'year' instead of 's', 'm', 'h', 'd', 'M' or 'Y'

  ```javascript
  // Before v2.0.0

  distanceInWordsStrict(
    new Date(1986, 3, 4, 10, 32, 0),
    new Date(1986, 3, 4, 10, 33, 1),
    { unit: 'm' }
  )

  // v2.0.0 onward

  formatDistanceStrict(
    new Date(1986, 3, 4, 10, 33, 1),
    new Date(1986, 3, 4, 10, 32, 0),
    { unit: 'minute' }
  )
  ```

- **BREAKING**: `parse` that previously used to convert strings and
  numbers to dates now parses only strings in an arbitrary format
  specified as an argument. Use `toDate` to coerce numbers and `parseISO`
  to parse ISO 8601 strings.

  ```javascript
  // Before v2.0.0
  parse('2016-01-01')
  parse(1547005581366)
  parse(new Date()) // Clone the date

  // v2.0.0 onward
  parse('2016-01-01', 'yyyy-MM-dd', new Date())
  parseISO('2016-01-01')
  toDate(1547005581366)
  toDate(new Date()) // Clone the date
  ```

- **BREAKING**: `toDate` (previously `parse`) now doesn't accept string
  arguments but only numbers and dates. `toDate` called with an invalid
  argument will return `Invalid Date`.

- **BREAKING**: new locale format.
  See [docs/Locale](https://date-fns.org/docs/Locale).
  Locales renamed:

  - `en` → `en-US`
  - `zh_cn` → `zh-CN`
  - `zh_tw` → `zh-TW`

  ```javascript
  // Before v2.0.0
  import locale from 'date-fns/locale/zh_cn'

  // v2.0.0 onward
  import locale from 'date-fns/locale/zh-CN'
  ```

- **BREAKING**: now `closestTo` and `closestIndexTo` don't throw an exception
  when the second argument is not an array, and return Invalid Date instead.

- **BREAKING**: now `isValid` doesn't throw an exception
  if the first argument is not an instance of Date.
  Instead, argument is converted beforehand using `toDate`.

  Examples:

  | `isValid` argument        | Before v2.0.0 | v2.0.0 onward |
  | ------------------------- | ------------- | ------------- |
  | `new Date()`              | `true`        | `true`        |
  | `new Date('2016-01-01')`  | `true`        | `true`        |
  | `new Date('')`            | `false`       | `false`       |
  | `new Date(1488370835081)` | `true`        | `true`        |
  | `new Date(NaN)`           | `false`       | `false`       |
  | `'2016-01-01'`            | `TypeError`   | `false`       |
  | `''`                      | `TypeError`   | `false`       |
  | `1488370835081`           | `TypeError`   | `true`        |
  | `NaN`                     | `TypeError`   | `false`       |

  We introduce this change to make _date-fns_ consistent with ECMAScript behavior
  that try to coerce arguments to the expected type
  (which is also the case with other _date-fns_ functions).

- **BREAKING**: functions now throw `RangeError` if optional values passed to `options`
  are not `undefined` or have expected values.
  This change is introduced for consistency with ECMAScript standard library which does the same.

- **BREAKING**: `format`, `formatDistance` (previously `distanceInWords`) and
  `formatDistanceStrict` (previously `distanceInWordsStrict`) now throw
  `RangeError` if one of the passed arguments is invalid. It reflects behavior of
  `toISOString` and Intl API. See [#1032](https://github.com/date-fns/date-fns/pull/1032).

- **BREAKING**: all functions now implicitly convert arguments by following rules:

  |           | date         | number | string      | boolean |
  | --------- | ------------ | ------ | ----------- | ------- |
  | 0         | new Date(0)  | 0      | '0'         | false   |
  | '0'       | Invalid Date | 0      | '0'         | false   |
  | 1         | new Date(1)  | 1      | '1'         | true    |
  | '1'       | Invalid Date | 1      | '1'         | true    |
  | true      | Invalid Date | NaN    | 'true'      | true    |
  | false     | Invalid Date | NaN    | 'false'     | false   |
  | null      | Invalid Date | NaN    | 'null'      | false   |
  | undefined | Invalid Date | NaN    | 'undefined' | false   |
  | NaN       | Invalid Date | NaN    | 'NaN'       | false   |

  Notes:

  - as before, arguments expected to be `Date` are converted to `Date` using _date-fns'_ `toDate` function;
  - arguments expected to be numbers are converted to integer numbers using our custom `toInteger` implementation
    (see [#765](https://github.com/date-fns/date-fns/pull/765));
  - arguments expected to be strings are converted to strings using JavaScript's `String` function;
  - arguments expected to be booleans are converted to boolean using JavaScript's `Boolean` function.

  `null` and `undefined` passed to optional arguments (i.e. properties of `options` argument)
  are ignored as if no argument was passed.

  If any resulting argument is invalid (i.e. `NaN` for numbers and `Invalid Date` for dates),
  an invalid value will be returned:

  - `false` for functions that return booleans (expect `isValid`);
  - `Invalid Date` for functions that return dates;
  - and `NaN` for functions that return numbers.

  See tests and PRs [#460](https://github.com/date-fns/date-fns/pull/460) and
  [#765](https://github.com/date-fns/date-fns/pull/765) for exact behavior.

- **BREAKING**: all functions now check if the passed number of arguments is less
  than the number of required arguments and will throw `TypeError` exception if so.

- **BREAKING**: all functions that accept numbers as arguments, now coerce
  values using `Number()` and also round off decimals. Positive decimals are
  rounded using `Math.floor`, decimals less than zero are rounded using
  `Math.ceil`.

- **BREAKING**: The Bower & UMD/CDN package versions are no longer supported.

- **BREAKING**: `null` now is not a valid date. `isValid(null)` returns `false`;
  `toDate(null)` returns an invalid date. Since `toDate` is used internally
  by all the functions, operations over `null` will also return an invalid date.
  [See #537](https://github.com/date-fns/date-fns/issues/537) for the reasoning.

- `toDate` (previously `parse`) and `isValid` functions now accept `any` type
  as the first argument.

- [Exclude `docs.json` from the npm package](https://github.com/date-fns/date-fns/pull/837). Kudos to [@hawkrives](https://github.com/hawkrives).

### Added

- FP functions like those in [lodash](https://github.com/lodash/lodash/wiki/FP-Guide),
  that support [currying](https://en.wikipedia.org/wiki/Currying), and, as a consequence,
  functional-style [function composing](https://medium.com/making-internets/why-using-chain-is-a-mistake-9bc1f80d51ba).

  Functions with options (`format`, `parse`, etc.) have two FP counterparts:
  one that has the options object as its first argument and one that hasn't.
  The name of the former has `WithOptions` added to the end of its name.

  In FP functions, the order of arguments is reversed.

  See [FP Guide](https://date-fns.org/docs/FP-Guide) for more information.

  ```javascript
  import addYears from 'date-fns/fp/addYears'
  import formatWithOptions from 'date-fns/fp/formatWithOptions'
  import eo from 'date-fns/locale/eo'

  // If FP function has not received enough arguments, it returns another function
  const addFiveYears = addYears(5)

  // Several arguments can be curried at once
  const dateToString = formatWithOptions({ locale: eo }, 'd MMMM yyyy')

  const dates = [
    new Date(2017, 0 /* Jan */, 1),
    new Date(2017, 1 /* Feb */, 11),
    new Date(2017, 6 /* Jul */, 2),
  ]

  const formattedDates = dates.map((date) => dateToString(addFiveYears(date)))
  //=> ['1 januaro 2022', '11 februaro 2022', '2 julio 2022']
  ```

- Added support for [ECMAScript Modules](http://www.ecma-international.org/ecma-262/6.0/#sec-modules).

  It allows usage with bundlers that support tree-shaking,
  like [rollup.js](http://rollupjs.org) and [webpack](https://webpack.js.org):

  ```javascript
  // Without tree-shaking:
  import format from 'date-fns/format'
  import parse from 'date-fns/parse'

  // With tree-shaking:
  import { format, parse } from 'date-fns'
  ```

  Also, ESM functions provide default export, they can be used with TypeScript
  to import functions in more idiomatic way:

  ```typescript
  // Before
  import * as format from 'date-fns/format'

  // Now
  import format from 'date-fns/format'
  ```

- `formatRelative` function. See [formatRelative](https://date-fns.org/docs/formatRelative)

- Flow typings for `index.js`, `fp/index.js`, `locale/index.js`, and their ESM equivalents.
  See PR [#558](https://github.com/date-fns/date-fns/pull/558)

- New locale-dependent week-numbering year helpers:

  - `getWeek`

  - `getWeekYear`

  - `setWeek`

  - `setWeekYear`

  - `startOfWeekYear`

- Added `eachWeekOfInterval`, the weekly equivalent of `eachDayOfInterval`

- [Added `getUnixTime` function](https://github.com/date-fns/date-fns/pull/870). Kudos to [@Kingwl](https://github.com/Kingwl).

- [New decade helpers](https://github.com/date-fns/date-fns/pull/839). Thanks to [@y-nk](https://github.com/y-nk)!

  - `getDecade`

  - `startOfDecade`

  - `endOfDecade`

  - `lastDayOfDecade`

- [New `roundToNearestMinutes` function](https://github.com/date-fns/date-fns/pull/928). Kudos to [@xkizer](https://github.com/xkizer).

- Added new function `fromUnixTime`. Thansk to [@xkizer](https://github.com/xkizer).

- New interval, month, and year helpers to fetch a list of all Saturdays and Sundays (weekends) for a given date interval. `eachWeekendOfInterval` is the handler function while the other two are wrapper functions. Kudos to [@laekettavong](https://github.com/laekettavong)!

  - `eachWeekendOfInterval`

  - `eachWeekendOfMonth`

  - `eachWeekendOfYear`

- Build-efficient `lightFormat` that only supports the popular subset of tokens. See [#1050](https://github.com/date-fns/date-fns/pull/1015).

- `parseISO` function that parses ISO 8601 strings. See [#1023](https://github.com/date-fns/date-fns/pull/1023).

- Add constants that can be imported directly from `date-fns` or the submodule `date-fns/constants`:

  - `maxTime`

  - `minTime`

- New locales:

  - [Norwegian Nynorsk locale (nn)](https://github.com/date-fns/date-fns/pull/1172)
    by [@draperunner](https://github.com/draperunner).

  - [Ukrainian locale (ua)](https://github.com/date-fns/date-fns/pull/532)
    by [@korzhyk](https://github.com/korzhyk).

  - [Vietnamese locale (vi)](https://github.com/date-fns/date-fns/pull/546)
    by [@trongthanh](https://github.com/trongthanh).

  - [Persian locale (fa-IR)](https://github.com/date-fns/date-fns/pull/1113)
    by [@mort3za](https://github.com/mort3za).

  - [Latvian locale (lv)](https://github.com/date-fns/date-fns/pull/1175)
    by [@prudolfs](https://github.com/prudolfs).

  - [Bengali locale (bb)](https://github.com/date-fns/date-fns/pull/845)
    by [@nutboltu](https://github.com/nutboltu) and [@touhidrahman](https://github.com/touhidrahman).

  - [Hungarian (hu) and Lithuanian (lt) locales](https://github.com/date-fns/date-fns/pull/864)
    by [@izifortune](https://github.com/izifortune) and [pardoeryanair](https://github.com/pardoeryanair).

  - [Canadian English locale (en-CA)](https://github.com/date-fns/date-fns/pull/688)
    by [@markowsiak](https://github.com/markowsiak).

  - [Great Britain English locale (en-GB)](https://github.com/date-fns/date-fns/pull/563)
    by [@glintik](https://github.com/glintik).

  - [Uighur locale (ug)](https://github.com/date-fns/date-fns/pull/1080)
    by [@abduwaly](https://github.com/abduwaly).

- [Added new function `differenceInBusinessDays`](https://github.com/date-fns/date-fns/pull/1194)
  which calculates the difference in business days. Kudos to [@ThorrStevens](https://github.com/ThorrStevens)!

- [Added new function `addBusinessDays`](https://github.com/date-fns/date-fns/pull/1154),
  similar to `addDays` but ignoring weekends. Thanks to [@ThorrStevens](https://github.com/ThorrStevens)!

## [1.30.1] - 2018-12-10

### Fixed

- [Fixed DST issue](https://github.com/date-fns/date-fns/pull/1005). See [#972](https://github.com/date-fns/date-fns/issues/972) and [#992](https://github.com/date-fns/date-fns/issues/992) for more details. This fix was backported from v2.

- Fix a few bugs that appear in timezones with offsets that include seconds (e.g. GMT+00:57:44). See PR [#789](https://github.com/date-fns/date-fns/issues/789). This fix was backported from v2.

- [Fixed misspelled January in the Thai locale](https://github.com/date-fns/date-fns/pull/913). Thanks to [@ratchapol-an](https://github.com/ratchapol-an)!

### Added

- [Added Serbian locale](https://github.com/date-fns/date-fns/pull/717). Kudos to [@mawi12345](https://github.com/mawi12345)!

- [Added Belarusian locale](https://github.com/date-fns/date-fns/pull/716). Kudos to [@mawi12345](https://github.com/mawi12345) again!

### Changed

- [Improved ja translation of distanceInWords](https://github.com/date-fns/date-fns/pull/880). Thanks to [@kudohamu](https://github.com/kudohamu)!

## [1.30.0] - 2018-12-10

⚠️ The release got failed.

## [1.29.0] - 2017-10-11

### Fixed

- Fixed Italian translations for `formatDistance`. ([see the issue: #550](https://github.com/date-fns/date-fns/issues/550); [see the PR: #552](https://github.com/date-fns/date-fns/pull/552))
  Thanks to [@giofilo](https://github.com/giofilo)!

### Added

- [Hungarian locale (hu)](https://github.com/date-fns/date-fns/pull/503)
  (thanks to László Horváth [@horvathlg](https://github.com/horvathlg))

- [Slovenian locale (sl)](https://github.com/date-fns/date-fns/pull/505)
  (thanks to Adam Stradovnik [@Neoglyph](https://github.com/Neoglyph))

- Added `step` to `eachDay` function. Thanks to [@BDav24](https://github.com/BDav24).
  See PR [#487](https://github.com/date-fns/date-fns/pull/487).

## [1.28.5] - 2017-05-19

### Fixed

- Fixed a.m./p.m. formatters in Chinese Simplified locale.
  Thanks to [@fnlctrl](https://github.com/fnlctrl).
  See PR [#486](https://github.com/date-fns/date-fns/pull/486)

## [1.28.4] - 2017-04-26

### Fixed

- Fixed accents on weekdays in the Italian locale.
  See PR [#481](https://github.com/date-fns/date-fns/pull/481).
  Thanks to [@albertorestifo](https://github.com/albertorestifo)

- Fixed typo in `ddd` format token in Spanish language locale.
  Kudos to [@fjaguero](https://github.com/fjaguero).
  See PR [#482](https://github.com/date-fns/date-fns/pull/482)

## [1.28.3] - 2017-04-14

### Fixed

- Fixed ordinal numbers for Danish language locale. Thanks to [@kgram](https://github.com/kgram).
  See PR [#474](https://github.com/date-fns/date-fns/pull/474)

## [1.28.2] - 2017-03-27

### Fixed

- Fixed `dd` and `ddd` formatters in Polish language locale. Kudos to [@justrag](https://github.com/justrag).
  See PR: [#467](https://github.com/date-fns/date-fns/pull/467)

## [1.28.1] - 2017-03-19

### Fixed

- Fixed DST border bug in `addMilliseconds`, `addSeconds`, `addMinutes`, `addHours`,
  `subMilliseconds`, `subSeconds`, `subMinutes` and `subHours`.
  See issue [#465](https://github.com/date-fns/date-fns/issues/465)

- Minor fix for Indonesian locale. Thanks to [@bentinata](https://github.com/bentinata).
  See PR: [#458](https://github.com/date-fns/date-fns/pull/458)

## [1.28.0] - 2017-02-27

### Added

- [Romanian locale (ro)](https://github.com/date-fns/date-fns/pull/446)
  (thanks to Sergiu Munteanu [@jsergiu](https://github.com/jsergiu))

### Fixed

- All functions now convert all their arguments to the respective types.
  See PR: [#443](https://github.com/date-fns/date-fns/pull/443)

- Fixes for ordinals (1er, 2, 3, …) in French locale.
  Thanks to [@fbonzon](https://github.com/fbonzon).
  See PR: [#449](https://github.com/date-fns/date-fns/pull/449)

## [1.27.2] - 2017-02-01

### Fixed

- Various fixes for Dutch locale. See PR: [#416](https://github.com/date-fns/date-fns/pull/416).
  Thanks to Ruben Stolk [@rubenstolk](https://github.com/rubenstolk)

## [1.27.1] - 2017-01-20

### Fixed

- Added generation of TypeScript locale sub-modules, allowing import of locales in TypeScript.

## [1.27.0] - 2017-01-19

### Added

- [Macedonian locale (mk)](https://github.com/date-fns/date-fns/pull/398)
  (thanks to Petar Vlahu [@vlahupetar](https://github.com/vlahupetar))

## [1.26.0] - 2017-01-15

### Added

- `getTime`

### Fixed

- Various fixes for Japanese locale. See PR: [395](https://github.com/date-fns/date-fns/pull/395).
  Thanks to Yamagishi Kazutoshi [@ykzts](https://github.com/ykzts)

## [1.25.0] - 2017-01-11

### Added

- [Bulgarian locale (bg)](https://github.com/date-fns/date-fns/pull/357)
  (thanks to Nikolay Stoynov [@arvigeus](https://github.com/arvigeus))

- [Czech locale (cs)](https://github.com/date-fns/date-fns/pull/386)
  (thanks to David Rus [@davidrus](https://github.com/davidrus))

## [1.24.0] - 2017-01-06

### Added

- [Modern Standard Arabic locale (ar)](https://github.com/date-fns/date-fns/pull/367)
  (thanks to Abdallah Hassan [@AbdallahAHO](https://github.com/AbdallahAHO))

## [1.23.0] - 2017-01-05

### Added

- Auto generate TypeScript and flow typings from documentation on release.
  Thanks to [@mattlewis92](https://github.com/mattlewis92).
  See related PRs: [#355](https://github.com/date-fns/date-fns/pull/355),
  [#370](https://github.com/date-fns/date-fns/pull/370)

- [Croatian locale (hr)](https://github.com/date-fns/date-fns/pull/365)
  (thanks to Matija Marohnić [@silvenon](https://github.com/silvenon))

- [Thai locale (th)](https://github.com/date-fns/date-fns/pull/362)
  (thanks to Athiwat Hirunworawongkun [@athivvat](https://github.com/athivvat))

- [Finnish locale (fi)](https://github.com/date-fns/date-fns/pull/361)
  (thanks to Pyry-Samuli Lahti [@Pyppe](https://github.com/Pyppe))

## [1.22.0] - 2016-12-28

### Added

- [Icelandic locale (is)](https://github.com/date-fns/date-fns/pull/356)
  (thanks to Derek Blank [@derekblank](https://github.com/derekblank))

## [1.21.1] - 2016-12-18

### Fixed

- Fixed `isBefore` and `isAfter` documentation mistakes.

## [1.21.0] - 2016-12-16

### Added

- [Filipino locale (fil)](https://github.com/date-fns/date-fns/pull/339)
  (thanks to Ian De La Cruz [@RIanDeLaCruz](https://github.com/RIanDeLaCruz))

- [Danish locale (da)](https://github.com/date-fns/date-fns/pull/343)
  (kudos to Anders B. Hansen [@Andersbiha](https://github.com/Andersbiha))

## [1.20.1] - 2016-12-14

### Fixed

- Fixed documentation for `getOverlappingDaysInRanges`.

## [1.20.0] - 2016-12-13

### Added

- `areRangesOverlapping` and `getOverlappingDaysInRanges`
  Thanks to Joanna T [@asia-t](https://github.com/asia-t).
  See PR: [#331](https://github.com/date-fns/date-fns/pull/331)

## [1.19.0] - 2016-12-13

### Added

- [Greek locale (el)](https://github.com/date-fns/date-fns/pull/334)
  (kudos to Theodoros Orfanidis [@teoulas](https://github.com/teoulas))

- [Slovak locale (sk)](https://github.com/date-fns/date-fns/pull/336)
  (kudos to Marek Suscak [@mareksuscak](https://github.com/mareksuscak))

- Added yarn support.
  Thanks to Uladzimir Havenchyk [@havenchyk](https://github.com/havenchyk).
  See PR: [#288](https://github.com/date-fns/date-fns/pull/288)

## [1.18.0] - 2016-12-12

### Added

- [Turkish locale (tr)](https://github.com/date-fns/date-fns/pull/329)
  (kudos to Alpcan Aydın [@alpcanaydin](https://github.com/alpcanaydin))

- [Korean locale (ko)](https://github.com/date-fns/date-fns/pull/327)
  (thanks to Hong Chulju [@angdev](https://github.com/angdev))

### Fixed

- `SS` and `SSS` formats in `format` are now correctly displayed with leading zeros.
  Thanks to Paul Dijou [@pauldijou](https://github.com/pauldijou).
  See PR: [#330](https://github.com/date-fns/date-fns/pull/330)

## [1.17.0] - 2016-12-10

### Added

- [Polish locale (pl)](https://github.com/date-fns/date-fns/pull/294)
  (thanks to Mateusz Derks [@ertrzyiks](https://github.com/ertrzyiks))

- [Portuguese locale (pt)](https://github.com/date-fns/date-fns/pull/316)
  (thanks to Dário Freire [@dfreire](https://github.com/dfreire))

- [Swedish locale (sv)](https://github.com/date-fns/date-fns/pull/311)
  (thanks to Johannes Ulén [@ejulen](https://github.com/ejulen))

- [French locale (fr)](https://github.com/date-fns/date-fns/pull/281)
  (thanks to Jean Dupouy [@izeau](https://github.com/izeau))

- Performance tests. See PR: [#289](https://github.com/date-fns/date-fns/pull/289)

### Fixed

- Fixed TypeScript and flow typings for `isValid`.
  See PR: [#310](https://github.com/date-fns/date-fns/pull/310)

- Fixed incorrect locale tests that could potentially lead to `format` bugs.
  Kudos to Mateusz Derks [@ertrzyiks](https://github.com/ertrzyiks).
  See related PRs: [#312](https://github.com/date-fns/date-fns/pull/312),
  [#320](https://github.com/date-fns/date-fns/pull/320)

- Minor language fixes in the documentation.
  Thanks to Vedad Šoše [@vedadsose](https://github.com/vedadsose) ([#314](https://github.com/date-fns/date-fns/pull/314))
  and Asia [@asia-t](https://github.com/asia-t) ([#318](https://github.com/date-fns/date-fns/pull/318))

### Changed

- `format` now returns `String('Invalid Date')` if the passed date is invalid.
  See PR: [#323](https://github.com/date-fns/date-fns/pull/323)

- `distanceInWords`, `distanceInWordsToNow`, `distanceInWordsStrict` and `format` functions now
  check if the passed locale is valid, and fallback to English locale otherwise.
  See PR: [#321](https://github.com/date-fns/date-fns/pull/321)

- _Internal_: use a loop instead of `Object.keys` in `buildFormattingTokensRegExp`
  to improve compatibility with older browsers.
  See PR: [#322](https://github.com/date-fns/date-fns/pull/322)

## [1.16.0] - 2016-12-08

### Added

- [Italian locale (it)](https://github.com/date-fns/date-fns/pull/298)
  (thanks to Alberto Restifo [@albertorestifo](https://github.com/albertorestifo))

- For German `buildDistanceInWordsLocale`, add nominative case translations (for distances without a suffix).
  Kudos to Asia [@asia-t](https://github.com/asia-t).
  See related PR: [#295](https://github.com/date-fns/date-fns/pull/295)

## [1.15.1] - 2016-12-07

### Fixed

- Fixed TypeScript imports from individual modules.
  Thanks to [@mattlewis92](https://github.com/mattlewis92).
  See related PR: [#287](https://github.com/date-fns/date-fns/pull/287)

## [1.15.0] - 2016-12-07

### Added

- [Indonesian locale (id)](https://github.com/date-fns/date-fns/pull/299)
  (thanks to Rahmat Budiharso [@rbudiharso](https://github.com/rbudiharso))

- [Catalan locale (ca)](https://github.com/date-fns/date-fns/pull/300)
  (thanks to Guillermo Grau [@guigrpa](https://github.com/guigrpa))

### Fixed

- Fixed some inaccuracies in Spanish locale.
  Kudos to [@guigrpa](https://github.com/guigrpa).
  See related PR: [#302](https://github.com/date-fns/date-fns/pull/302)

## [1.14.1] - 2016-12-06

### Fixed

- Fixed broken test for Norwegian Bokmål locale.

## [1.14.0] - 2016-12-06

### Added

- [Norwegian Bokmål locale (nb)](https://github.com/date-fns/date-fns/pull/291)
  (thanks to Hans-Kristian Koren [@Hanse](https://github.com/Hanse))

## [1.13.0] - 2016-12-06

### Added

- [Chinese Traditional locale (zh_tw)](https://github.com/date-fns/date-fns/pull/283)
  (thanks to tonypai [@tpai](https://github.com/tpai)).

- [Dutch language locale (nl)](https://github.com/date-fns/date-fns/pull/278)
  (kudos to Jorik Tangelder [@jtangelder](https://github.com/jtangelder))

## [1.12.1] - 2016-12-05

### Fixed

- Added `distanceInWordsStrict` to the list of supported functions in I18n doc.

## [1.12.0] - 2016-12-05

### Added

- [Spanish language locale (es)](https://github.com/date-fns/date-fns/pull/269)
  (thanks to Juan Angosto [@juanangosto](https://github.com/juanangosto)).

### Fixed

- Fixed flow typings for some of the functions.
  See PR: [#273](https://github.com/date-fns/date-fns/pull/273)

## [1.11.2] - 2016-11-28

### Fixed

- Bug in `parse` when it sometimes parses ISO week-numbering dates incorrectly.
  See PR: [#262](https://github.com/date-fns/date-fns/pull/262)

- Bug in some functions which caused them to handle dates earlier than 100 AD incorrectly.
  See PR: [#263](https://github.com/date-fns/date-fns/pull/263)

## [1.11.1] - 2016-11-24

### Fixed

- Include TypeScript typings with npm package.

## [1.11.0] - 2016-11-23

### Added

- `distanceInWordsStrict`.
  Kudos to [@STRML](https://github.com/STRML).
  See related PR: [#254](https://github.com/date-fns/date-fns/pull/254)

- [TypeScript](https://www.typescriptlang.org/) typings for all functions.
  Kudos to [@mattlewis92](https://github.com/mattlewis92).
  See related PR: [#255](https://github.com/date-fns/date-fns/pull/255)

## [1.10.0] - 2016-11-01

### Added

- `parse` now can parse dates that are ISO 8601 centuries (e.g., `19` and `+0019`).

  ```javascript
  var result = parse('19')
  //=> Mon Jan 01 1900 00:00:00
  ```

- In `parse`, added ability to specify the number of additional digits
  for extended year or century format (possible values are 0, 1 or 2; default is 2).

  ```javascript
  parse('+002016-11-01')
  parse('+02016-11-01', { additionalDigits: 1 })
  parse('+2016-11-01', { additionalDigits: 0 })
  ```

## [1.9.0] - 2016-10-25

### Added

- Got index.js imports to work with SystemJS.

## [1.8.1] - 2016-10-24

### Fixed

- Added Japanese and German language locales to the list in I18n doc.

## [1.8.0] - 2016-10-23

### Added

- [Japanese language locale (ja)](https://github.com/date-fns/date-fns/pull/241)
  (thanks to Thomas Eilmsteiner [@DeMuu](https://github.com/DeMuu) again!)

- `getISODay`

- `setISODay`

## [1.7.0] - 2016-10-20

### Added

- [German language locale (de)](https://github.com/date-fns/date-fns/pull/237)
  (thanks to Thomas Eilmsteiner [@DeMuu](https://github.com/DeMuu)).

## [1.6.0] - 2016-10-16

### Added

- [Chinese Simplified locale (zh_cn)](https://github.com/date-fns/date-fns/pull/235)
  (kudos to Changyu [@KingMario](https://github.com/KingMario) Geng).

## [1.5.2] - 2016-10-13

### Fixed

- Incorrectly generated docs for `format`.

- Fixed typo in I18n doc.

## [1.5.1] - 2016-10-12

### Fixed

- A change log entry for [1.5.0] is added.

## [1.5.0] - 2016-10-12

### Added

- [The initial I18n support](https://date-fns.org/docs/I18n)

## [1.4.0] - 2016-10-09

### Added

- Basic [SystemJS](https://github.com/systemjs/systemjs) support.

### Fixed

- Fixed incorrect behaviour of `YYYY` and `YY` for years prior to 1000:
  now `format(new Date('0001-01-01'), 'YYYY-MM-DD')` returns `0001-01-01`
  instead of `1-01-01`.

## [1.3.0] - 2016-05-26

### Added

- `closestIndexTo`

## [1.2.0] - 2016-05-23

### Added

- Added an ability to pass negative numbers to `setDay`.

## [1.1.1] - 2016-05-19

### Fixed

- Fixed [Flow](http://flowtype.org/) declarations for some of the functions.

## [1.1.0] - 2016-05-19

### Added

- [Flow](http://flowtype.org/) declarations for each function
  in [the ".js.flow" style](http://flowtype.org/docs/declarations.html#declaration-files).
  Kudos to [@JohnyDays](https://github.com/JohnyDays). See related PRs:

  - [#205](https://github.com/date-fns/date-fns/pull/205)

  - [#207](https://github.com/date-fns/date-fns/pull/207)

## [1.0.0] - 2016-05-18

### Fixed

- `format` now returns the correct result for key `E`.

- Prevent `startOf...`, `endOf...` and `lastDayOf...` functions
  to return dates with an incorrect time when the date is modifying
  into another time zone.

- `parse` now parses years from 1 AD to 99 AD correctly.

- Fix a bug in `getISOWeek` appearing because of a changing time zone
  (e.g., when the given date is in DST and the start of the ISO year is not).

### Changed

- **BREAKING**: all functions are moved to the root of the library, so they
  are now accessible with `require('date-fns/name_of_function')` or
  `import nameOfFunction from 'date-fns/name_of_function'`.

  ```javascript
  // Before v1.0.0
  var addMonths = require('date-fns/src/add_months')

  // v1.0.0 onward
  var addMonths = require('date-fns/add_months')
  ```

- **BREAKING**: functions that had the last optional argument `weekStartsAt`
  (i.e. `endOfWeek`, `isSameWeek`, `lastDayOfWeek`, `setDay`, `startOfWeek`)
  now instead receive the object `options` with the property `options.weekStartsOn`
  as the last argument.

  ```javascript
  // Before v1.0.0
  var result = endOfWeek(new Date(2014, 8, 2), 1)

  // v1.0.0 onward
  var result = endOfWeek(new Date(2014, 8, 2), { weekStartsOn: 1 })
  ```

- **BREAKING**: remove the function `getTimeSinceMidnight` that was used inside
  the other functions.

- **BREAKING**: `differenceInDays` now returns the number of full days instead
  of calendar days.

- **BREAKING**: `eachDay` and `isWithinRange` now throw an exception
  when the given range boundaries are invalid.

- Faster `isLeapYear`.

- _Internal_: make the documentation more verbose.

- _Internal_: convert the tests from Chai to power-assert allowing them
  to run against IE8.

### Added

- `addISOYears`

- `closestTo`

- `differenceInCalendarDays`

- `differenceInCalendarISOWeeks`

- `differenceInCalendarISOYears`

- `differenceInCalendarMonths`

- `differenceInCalendarQuarters`

- `differenceInCalendarWeeks`

- `differenceInCalendarYears`

- `differenceInHours`

- `differenceInISOYears`

- `differenceInMilliseconds`

- `differenceInMinutes`

- `differenceInMonths`

- `differenceInQuarters`

- `differenceInSeconds`

- `differenceInWeeks`

- `differenceInYears`

- `distanceInWords`

- `distanceInWordsToNow`

- `endOfISOWeek`

- `endOfISOYear`

- `endOfToday`

- `endOfTomorrow`

- `endOfYesterday`

- `getDaysInYear`

- `isDate`

- `isFriday`

- `isMonday`

- `isSameISOWeek`

- `isSameISOYear`

- `isSaturday`

- `isSunday`

- `isThisHour`

- `isThisISOWeek`

- `isThisISOYear`

- `isThisMinute`

- `isThisMonth`

- `isThisQuarter`

- `isThisSecond`

- `isThisWeek`

- `isThisYear`

- `isThursday`

- `isTomorrow`

- `isTuesday`

- `isValid`

- `isWednesday`

- `isYesterday`

- `lastDayOfISOWeek`

- `lastDayOfISOYear`

- `startOfISOWeek`

- `startOfToday`

- `startOfTomorrow`

- `startOfYesterday`

- `subISOYears`

- Add `Qo`, `W`, `Wo`, `WW`, `GG`, `GGGG`, `Z`, `ZZ`, `X`, `x` keys to `format`.

## [0.17.0] - 2015-09-29

### Fixed

- Fixed a lot of bugs appearing when date is modifying into other time zone
  (e.g., when adding months and original date is in DST but new date is not).

- Prevent instances of Date to lose milliseconds value when passed to.
  `parse` in IE10.

### Changed

- `setISOWeek` now keeps time from original date.

- _Internal_: reuse `getDaysInMonth` inside of `addMonths`.

### Added

- `differenceInDays`

- `getTimeSinceMidnight`

- `format` now has new format key `aa`, which returns `a.m.`/`p.m.`
  as opposed to `a` that returns `am`/`pm`.

- Complete UMD package (for Bower and CDN).

## [0.16.0] - 2015-09-01

### Changed

- Use `parse` to clean date arguments in all functions.

- `parse` now fallbacks to `new Date` when the argument
  is not an ISO formatted date.

- _Internal_: reuse `getDaysInMonth` inside of `setMonth`.

### Added

- `addQuarters`

- `addWeeks`

- `endOfQuarter`

- `getDate`

- `getDay`

- `getDaysInMonth`

- `getHours`

- `getISOWeeksInYear`

- `getMilliseconds`

- `getMinutes`

- `getMonth`

- `getSeconds`

- `getYear`

- `isLeapYear`

- `isSameHour`

- `isSameMinute`

- `isSameQuarter`

- `isSameSecond`

- `lastDayOfQuarter`

- `lastDayOfWeek`

- `max`

- `min`

- `setDate`

- `setDay`

- `setHours`

- `setMilliseconds`

- `setMinutes`

- `setSeconds`

- `startOfQuarter`

- `subQuarters`

- `subWeeks`

## [0.15.0] - 2015-08-26

### Changed

- `format` now returns `a.m.`/`p.m.` instead of `am`/`pm`.

- `setMonth` now sets last day of month if original date was last day
  of longer month.

- _Internal_: Fix code style according to ESLint.

- _Internal_: Make tests run through all time zones.

### Added

- `getQuarter`

- `setQuarter`

- `getDayOfYear`

- `setDayOfYear`

- `isPast`

- `addSeconds`

- `subSeconds`

- `startOfSecond`

- `endOfSecond`

- `startOfMinute`

- `endOfMinute`

- `addMilliseconds`

- `subMilliseconds`

- `endOfYear`

- `addYears`

- `subYears`

- `lastDayOfYear`

- `lastDayOfMonth`

## [0.14.11] - 2015-08-21

### Fixed

- `format` now uses `parse` to avoid time zone bugs.

### Changed

- `setIsoWeek` now sets time to the start of the day.

## [0.14.10] - 2015-07-29

### Fixed

- `format` now behaves correctly with 12:00 am.

- `format` now behaves correctly with ordinal numbers.

### Added

- `compareAsc`

- `compareDesc`

- `addHours`

- `subHours`

- `isSameDay`

- `parse`

- `getISOYear`

- `setISOYear`

- `startOfISOYear`

- `getISOWeek`

- `setISOWeek`

## [0.14.9] - 2015-01-14

### Fixed

- `addMonths` now correctly behaves with February
  (see [#18](https://github.com/js-fns/date-fns/pull/18)).

## [0.14.8] - 2014-12-25

### Fixed

- `format` function now behaves correctly with `pm`/`am`.

## [0.14.6] - 2014-12-04

### Fixed

- Fix broken Bower support.

## [0.14.0] - 2014-11-05

### Added

- Bower package.

## [0.13.0] - 2014-10-22

### Added

- `addMinutes`

- `subMinutes`

- `isEqual`

- `isBefore`

- `isAfter`

## [0.12.1] - 2014-10-19

### Fixed

- Incorrect rounding in `DDD` formatter.

## [0.12.0] - 2014-10-15

### Added

- `isSameYear`

## [0.11.0] - 2014-10-15

### Added

- `isWithinRange`

## [0.10.0] - 2014-10-13

### Added

- `format`

- `startOfYear`

## [0.9.0] - 2014-10-10

### Changed

- _Internal_: simplify `isWeekend`

### Added

- `isFuture`

## [0.8.0] - 2014-10-09

### Changed

- _Internal_: reuse `addDays` inside of `subDays`.

### Added

- `addMonths`

- `subMonths`

- `setMonth`

- `setYear`

## [0.7.0] - 2014-10-08

### Added

- `isSameWeek`

## [0.6.0] - 2014-10-07

### Fixed

- Inconsistent behavior of `endOfMonth`.

### Added

- `isFirstDayOfMonth`

- `isLastDayOfMonth`

- `isSameMonth`

## [0.5.0] - 2014-10-07

### Added

- `addDays`

- `subDays`

## [0.4.0] - 2014-10-07

### Added

- `startOfWeek`

- `endOfWeek`

- `eachDay`

## [0.3.0] - 2014-10-06

### Changed

- `startOfDay` now sets milliseconds as well.

### Added

- `endOfDay`

- `startOfMonth`

- `endOfMonth`

## [0.2.0] - 2014-10-06

### Added

- `isToday`

- `isWeekend`

## 0.1.0 - 2014-10-06

### Added

- `startOfDay`

[unreleased]: https://github.com/date-fns/date-fns/compare/v2.16.1...HEAD
[2.16.1]: https://github.com/date-fns/date-fns/compare/v2.16.0...v2.16.1
[2.16.0]: https://github.com/date-fns/date-fns/compare/v2.15.0...v2.16.0
[2.15.0]: https://github.com/date-fns/date-fns/compare/v2.14.0...v2.15.0
[2.14.0]: https://github.com/date-fns/date-fns/compare/v2.13.0...v2.14.0
[2.13.0]: https://github.com/date-fns/date-fns/compare/v2.12.0...v2.13.0
[2.12.0]: https://github.com/date-fns/date-fns/compare/v2.11.1...v2.12.0
[2.11.1]: https://github.com/date-fns/date-fns/compare/v2.11.0...v2.11.1
[2.11.0]: https://github.com/date-fns/date-fns/compare/v2.10.0...v2.11.0
[2.10.0]: https://github.com/date-fns/date-fns/compare/v2.9.0...v2.10.0
[2.9.0]: https://github.com/date-fns/date-fns/compare/v2.8.1...v2.9.0
[2.8.1]: https://github.com/date-fns/date-fns/compare/v2.8.0...v2.8.1
[2.8.0]: https://github.com/date-fns/date-fns/compare/v2.7.0...v2.8.0
[2.7.0]: https://github.com/date-fns/date-fns/compare/v2.6.0...v2.7.0
[2.6.0]: https://github.com/date-fns/date-fns/compare/v2.5.1...v2.6.0
[2.5.1]: https://github.com/date-fns/date-fns/compare/v2.5.0...v2.5.1
[2.5.0]: https://github.com/date-fns/date-fns/compare/v2.4.1...v2.5.0
[2.4.1]: https://github.com/date-fns/date-fns/compare/v2.4.0...v2.4.1
[2.4.0]: https://github.com/date-fns/date-fns/compare/v2.3.0...v2.4.0
[2.3.0]: https://github.com/date-fns/date-fns/compare/v2.2.1...v2.3.0
[2.2.1]: https://github.com/date-fns/date-fns/compare/v2.1.0...v2.2.1
[2.1.0]: https://github.com/date-fns/date-fns/compare/v2.0.1...v2.1.0
[2.0.1]: https://github.com/date-fns/date-fns/compare/v2.0.0...v2.0.1
[2.0.0]: https://github.com/date-fns/date-fns/compare/v1.28.5...v2.0.0
[1.28.5]: https://github.com/date-fns/date-fns/compare/v1.28.4...v1.28.5
[1.28.4]: https://github.com/date-fns/date-fns/compare/v1.28.3...v1.28.4
[1.28.3]: https://github.com/date-fns/date-fns/compare/v1.28.2...v1.28.3
[1.28.2]: https://github.com/date-fns/date-fns/compare/v1.28.1...v1.28.2
[1.28.1]: https://github.com/date-fns/date-fns/compare/v1.28.0...v1.28.1
[1.28.0]: https://github.com/date-fns/date-fns/compare/v1.27.2...v1.28.0
[1.27.2]: https://github.com/date-fns/date-fns/compare/v1.27.1...v1.27.2
[1.27.1]: https://github.com/date-fns/date-fns/compare/v1.27.0...v1.27.1
[1.27.0]: https://github.com/date-fns/date-fns/compare/v1.26.0...v1.27.0
[1.26.0]: https://github.com/date-fns/date-fns/compare/v1.25.0...v1.26.0
[1.25.0]: https://github.com/date-fns/date-fns/compare/v1.24.0...v1.25.0
[1.24.0]: https://github.com/date-fns/date-fns/compare/v1.23.0...v1.24.0
[1.23.0]: https://github.com/date-fns/date-fns/compare/v1.22.0...v1.23.0
[1.22.0]: https://github.com/date-fns/date-fns/compare/v1.21.1...v1.22.0
[1.21.1]: https://github.com/date-fns/date-fns/compare/v1.21.0...v1.21.1
[1.21.0]: https://github.com/date-fns/date-fns/compare/v1.20.1...v1.21.0
[1.20.1]: https://github.com/date-fns/date-fns/compare/v1.20.0...v1.20.1
[1.20.0]: https://github.com/date-fns/date-fns/compare/v1.19.0...v1.20.0
[1.19.0]: https://github.com/date-fns/date-fns/compare/v1.18.0...v1.19.0
[1.18.0]: https://github.com/date-fns/date-fns/compare/v1.17.0...v1.18.0
[1.17.0]: https://github.com/date-fns/date-fns/compare/v1.16.0...v1.17.0
[1.16.0]: https://github.com/date-fns/date-fns/compare/v1.15.1...v1.16.0
[1.15.1]: https://github.com/date-fns/date-fns/compare/v1.15.0...v1.15.1
[1.15.0]: https://github.com/date-fns/date-fns/compare/v1.14.1...v1.15.0
[1.14.1]: https://github.com/date-fns/date-fns/compare/v1.14.0...v1.14.1
[1.14.0]: https://github.com/date-fns/date-fns/compare/v1.13.0...v1.14.0
[1.13.0]: https://github.com/date-fns/date-fns/compare/v1.12.1...v1.13.0
[1.12.1]: https://github.com/date-fns/date-fns/compare/v1.12.0...v1.12.1
[1.12.0]: https://github.com/date-fns/date-fns/compare/v1.11.2...v1.12.0
[1.11.2]: https://github.com/date-fns/date-fns/compare/v1.11.1...v1.11.2
[1.11.1]: https://github.com/date-fns/date-fns/compare/v1.11.0...v1.11.1
[1.11.0]: https://github.com/date-fns/date-fns/compare/v1.10.0...v1.11.0
[1.10.0]: https://github.com/date-fns/date-fns/compare/v1.9.0...v1.10.0
[1.9.0]: https://github.com/date-fns/date-fns/compare/v1.8.1...v1.9.0
[1.8.1]: https://github.com/date-fns/date-fns/compare/v1.8.0...v1.8.1
[1.8.0]: https://github.com/date-fns/date-fns/compare/v1.7.0...v1.8.0
[1.7.0]: https://github.com/date-fns/date-fns/compare/v1.6.0...v1.7.0
[1.6.0]: https://github.com/date-fns/date-fns/compare/v1.5.2...v1.6.0
[1.5.2]: https://github.com/date-fns/date-fns/compare/v1.5.1...v1.5.2
[1.5.1]: https://github.com/date-fns/date-fns/compare/v1.5.0...v1.5.1
[1.5.0]: https://github.com/date-fns/date-fns/compare/v1.4.0...v1.5.0
[1.4.0]: https://github.com/date-fns/date-fns/compare/v1.3.0...v1.4.0
[1.3.0]: https://github.com/date-fns/date-fns/compare/v1.2.0...v1.3.0
[1.2.0]: https://github.com/date-fns/date-fns/compare/v1.1.1...v1.2.0
[1.1.1]: https://github.com/date-fns/date-fns/compare/v1.1.0...v1.1.1
[1.1.0]: https://github.com/date-fns/date-fns/compare/v1.0.0...v1.1.0
[1.0.0]: https://github.com/date-fns/date-fns/compare/v0.17.0...v1.0.0
[0.17.0]: https://github.com/date-fns/date-fns/compare/v0.16.0...v0.17.0
[0.16.0]: https://github.com/date-fns/date-fns/compare/v0.15.0...v0.16.0
[0.15.0]: https://github.com/date-fns/date-fns/compare/v0.14.11...v0.15.0
[0.14.11]: https://github.com/date-fns/date-fns/compare/v0.14.10...v0.14.11
[0.14.10]: https://github.com/date-fns/date-fns/compare/v0.14.9...v0.14.10
[0.14.9]: https://github.com/date-fns/date-fns/compare/v0.14.8...v0.14.9
[0.14.8]: https://github.com/date-fns/date-fns/compare/v0.14.6...v0.14.8
[0.14.6]: https://github.com/date-fns/date-fns/compare/v0.14.0...v0.14.6
[0.14.0]: https://github.com/date-fns/date-fns/compare/v0.13.0...v0.14.0
[0.13.0]: https://github.com/date-fns/date-fns/compare/v0.12.1...v0.13.0
[0.12.1]: https://github.com/date-fns/date-fns/compare/v0.12.0...v0.12.1
[0.12.0]: https://github.com/date-fns/date-fns/compare/v0.11.0...v0.12.0
[0.11.0]: https://github.com/date-fns/date-fns/compare/v0.10.0...v0.11.0
[0.10.0]: https://github.com/date-fns/date-fns/compare/v0.9.0...v0.10.0
[0.9.0]: https://github.com/date-fns/date-fns/compare/v0.8.0...v0.9.0
[0.8.0]: https://github.com/date-fns/date-fns/compare/v0.7.0...v0.8.0
[0.7.0]: https://github.com/date-fns/date-fns/compare/v0.6.0...v0.7.0
[0.6.0]: https://github.com/date-fns/date-fns/compare/v0.5.0...v0.6.0
[0.5.0]: https://github.com/date-fns/date-fns/compare/v0.4.0...v0.5.0
[0.4.0]: https://github.com/date-fns/date-fns/compare/v0.3.0...v0.4.0
[0.3.0]: https://github.com/date-fns/date-fns/compare/v0.2.0...v0.3.0
[0.2.0]: https://github.com/date-fns/date-fns/compare/v0.1.0...v0.2.0
