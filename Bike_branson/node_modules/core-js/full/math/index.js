'use strict';
var parent = require('../../actual/math');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.math.clamp');
require('../../modules/esnext.math.deg-per-rad');
require('../../modules/esnext.math.degrees');
require('../../modules/esnext.math.fscale');
require('../../modules/esnext.math.rad-per-deg');
require('../../modules/esnext.math.radians');
require('../../modules/esnext.math.scale');
require('../../modules/esnext.math.seeded-prng');
require('../../modules/esnext.math.signbit');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.math.iaddh');
require('../../modules/esnext.math.isubh');
require('../../modules/esnext.math.imulh');
require('../../modules/esnext.math.umulh');

module.exports = parent;
