{"version": 3, "file": "index.mjs", "sources": ["../src/built-in-definitions.ts", "../src/add-platform-specific-polyfills.ts", "../src/helpers.ts", "../src/index.ts"], "sourcesContent": ["import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\n\ntype ObjectMap<V> = { [name: string]: V };\n\ntype PolyfillDescriptor<T> = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  meta: T | null;\n};\n\ntype CoreJS2Meta = {\n  minRuntimeVersion: string | null;\n};\n\nconst define = <T>(\n  name: string,\n  pure?: string | null,\n  global: string[] = [],\n  meta?: T | null,\n): PolyfillDescriptor<T> => {\n  return { name, pure, global, meta };\n};\n\nconst pureAndGlobal = (\n  pure: string,\n  global: string[],\n  minRuntimeVersion: string | null = null,\n) => define<CoreJS2Meta>(global[0], pure, global, { minRuntimeVersion });\n\nconst globalOnly = (global: string[]) =>\n  define<CoreJS2Meta>(global[0], null, global);\n\nconst pureOnly = (pure: string, name: string) =>\n  define<CoreJS2Meta>(name, pure, []);\n\nconst ArrayNatureIterators = [\n  \"es6.object.to-string\",\n  \"es6.array.iterator\",\n  \"web.dom.iterable\",\n];\n\nexport const CommonIterators = [\"es6.string.iterator\", ...ArrayNatureIterators];\n\nconst PromiseDependencies = [\"es6.object.to-string\", \"es6.promise\"];\n\nexport const BuiltIns: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  DataView: globalOnly([\"es6.typed.data-view\"]),\n  Float32Array: globalOnly([\"es6.typed.float32-array\"]),\n  Float64Array: globalOnly([\"es6.typed.float64-array\"]),\n  Int8Array: globalOnly([\"es6.typed.int8-array\"]),\n  Int16Array: globalOnly([\"es6.typed.int16-array\"]),\n  Int32Array: globalOnly([\"es6.typed.int32-array\"]),\n  Map: pureAndGlobal(\"map\", [\"es6.map\", ...CommonIterators]),\n  Number: globalOnly([\"es6.number.constructor\"]),\n  Promise: pureAndGlobal(\"promise\", PromiseDependencies),\n  RegExp: globalOnly([\"es6.regexp.constructor\"]),\n  Set: pureAndGlobal(\"set\", [\"es6.set\", ...CommonIterators]),\n  Symbol: pureAndGlobal(\"symbol/index\", [\"es6.symbol\"]),\n  Uint8Array: globalOnly([\"es6.typed.uint8-array\"]),\n  Uint8ClampedArray: globalOnly([\"es6.typed.uint8-clamped-array\"]),\n  Uint16Array: globalOnly([\"es6.typed.uint16-array\"]),\n  Uint32Array: globalOnly([\"es6.typed.uint32-array\"]),\n  WeakMap: pureAndGlobal(\"weak-map\", [\"es6.weak-map\", ...CommonIterators]),\n  WeakSet: pureAndGlobal(\"weak-set\", [\"es6.weak-set\", ...CommonIterators]),\n\n  setImmediate: pureOnly(\"set-immediate\", \"web.immediate\"),\n  clearImmediate: pureOnly(\"clear-immediate\", \"web.immediate\"),\n  parseFloat: pureOnly(\"parse-float\", \"es6.parse-float\"),\n  parseInt: pureOnly(\"parse-int\", \"es6.parse-int\"),\n};\n\nexport const InstanceProperties: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  __defineGetter__: globalOnly([\"es7.object.define-getter\"]),\n  __defineSetter__: globalOnly([\"es7.object.define-setter\"]),\n  __lookupGetter__: globalOnly([\"es7.object.lookup-getter\"]),\n  __lookupSetter__: globalOnly([\"es7.object.lookup-setter\"]),\n  anchor: globalOnly([\"es6.string.anchor\"]),\n  big: globalOnly([\"es6.string.big\"]),\n  bind: globalOnly([\"es6.function.bind\"]),\n  blink: globalOnly([\"es6.string.blink\"]),\n  bold: globalOnly([\"es6.string.bold\"]),\n  codePointAt: globalOnly([\"es6.string.code-point-at\"]),\n  copyWithin: globalOnly([\"es6.array.copy-within\"]),\n  endsWith: globalOnly([\"es6.string.ends-with\"]),\n  entries: globalOnly(ArrayNatureIterators),\n  every: globalOnly([\"es6.array.every\"]),\n  fill: globalOnly([\"es6.array.fill\"]),\n  filter: globalOnly([\"es6.array.filter\"]),\n  finally: globalOnly([\"es7.promise.finally\", ...PromiseDependencies]),\n  find: globalOnly([\"es6.array.find\"]),\n  findIndex: globalOnly([\"es6.array.find-index\"]),\n  fixed: globalOnly([\"es6.string.fixed\"]),\n  flags: globalOnly([\"es6.regexp.flags\"]),\n  flatMap: globalOnly([\"es7.array.flat-map\"]),\n  fontcolor: globalOnly([\"es6.string.fontcolor\"]),\n  fontsize: globalOnly([\"es6.string.fontsize\"]),\n  forEach: globalOnly([\"es6.array.for-each\"]),\n  includes: globalOnly([\"es6.string.includes\", \"es7.array.includes\"]),\n  indexOf: globalOnly([\"es6.array.index-of\"]),\n  italics: globalOnly([\"es6.string.italics\"]),\n  keys: globalOnly(ArrayNatureIterators),\n  lastIndexOf: globalOnly([\"es6.array.last-index-of\"]),\n  link: globalOnly([\"es6.string.link\"]),\n  map: globalOnly([\"es6.array.map\"]),\n  match: globalOnly([\"es6.regexp.match\"]),\n  name: globalOnly([\"es6.function.name\"]),\n  padStart: globalOnly([\"es7.string.pad-start\"]),\n  padEnd: globalOnly([\"es7.string.pad-end\"]),\n  reduce: globalOnly([\"es6.array.reduce\"]),\n  reduceRight: globalOnly([\"es6.array.reduce-right\"]),\n  repeat: globalOnly([\"es6.string.repeat\"]),\n  replace: globalOnly([\"es6.regexp.replace\"]),\n  search: globalOnly([\"es6.regexp.search\"]),\n  small: globalOnly([\"es6.string.small\"]),\n  some: globalOnly([\"es6.array.some\"]),\n  sort: globalOnly([\"es6.array.sort\"]),\n  split: globalOnly([\"es6.regexp.split\"]),\n  startsWith: globalOnly([\"es6.string.starts-with\"]),\n  strike: globalOnly([\"es6.string.strike\"]),\n  sub: globalOnly([\"es6.string.sub\"]),\n  sup: globalOnly([\"es6.string.sup\"]),\n  toISOString: globalOnly([\"es6.date.to-iso-string\"]),\n  toJSON: globalOnly([\"es6.date.to-json\"]),\n  toString: globalOnly([\n    \"es6.object.to-string\",\n    \"es6.date.to-string\",\n    \"es6.regexp.to-string\",\n  ]),\n  trim: globalOnly([\"es6.string.trim\"]),\n  trimEnd: globalOnly([\"es7.string.trim-right\"]),\n  trimLeft: globalOnly([\"es7.string.trim-left\"]),\n  trimRight: globalOnly([\"es7.string.trim-right\"]),\n  trimStart: globalOnly([\"es7.string.trim-left\"]),\n  values: globalOnly(ArrayNatureIterators),\n};\n\n// This isn't present in older @babel/compat-data versions\nif (\"es6.array.slice\" in corejs2Polyfills) {\n  InstanceProperties.slice = globalOnly([\"es6.array.slice\"]);\n}\n\nexport const StaticProperties: ObjectMap<\n  ObjectMap<PolyfillDescriptor<CoreJS2Meta>>\n> = {\n  Array: {\n    from: pureAndGlobal(\"array/from\", [\n      \"es6.symbol\",\n      \"es6.array.from\",\n      ...CommonIterators,\n    ]),\n    isArray: pureAndGlobal(\"array/is-array\", [\"es6.array.is-array\"]),\n    of: pureAndGlobal(\"array/of\", [\"es6.array.of\"]),\n  },\n\n  Date: {\n    now: pureAndGlobal(\"date/now\", [\"es6.date.now\"]),\n  },\n\n  JSON: {\n    stringify: pureOnly(\"json/stringify\", \"es6.symbol\"),\n  },\n\n  Math: {\n    // 'Math' was not included in the 7.0.0\n    // release of '@babel/runtime'. See issue https://github.com/babel/babel/pull/8616.\n    acosh: pureAndGlobal(\"math/acosh\", [\"es6.math.acosh\"], \"7.0.1\"),\n    asinh: pureAndGlobal(\"math/asinh\", [\"es6.math.asinh\"], \"7.0.1\"),\n    atanh: pureAndGlobal(\"math/atanh\", [\"es6.math.atanh\"], \"7.0.1\"),\n    cbrt: pureAndGlobal(\"math/cbrt\", [\"es6.math.cbrt\"], \"7.0.1\"),\n    clz32: pureAndGlobal(\"math/clz32\", [\"es6.math.clz32\"], \"7.0.1\"),\n    cosh: pureAndGlobal(\"math/cosh\", [\"es6.math.cosh\"], \"7.0.1\"),\n    expm1: pureAndGlobal(\"math/expm1\", [\"es6.math.expm1\"], \"7.0.1\"),\n    fround: pureAndGlobal(\"math/fround\", [\"es6.math.fround\"], \"7.0.1\"),\n    hypot: pureAndGlobal(\"math/hypot\", [\"es6.math.hypot\"], \"7.0.1\"),\n    imul: pureAndGlobal(\"math/imul\", [\"es6.math.imul\"], \"7.0.1\"),\n    log1p: pureAndGlobal(\"math/log1p\", [\"es6.math.log1p\"], \"7.0.1\"),\n    log10: pureAndGlobal(\"math/log10\", [\"es6.math.log10\"], \"7.0.1\"),\n    log2: pureAndGlobal(\"math/log2\", [\"es6.math.log2\"], \"7.0.1\"),\n    sign: pureAndGlobal(\"math/sign\", [\"es6.math.sign\"], \"7.0.1\"),\n    sinh: pureAndGlobal(\"math/sinh\", [\"es6.math.sinh\"], \"7.0.1\"),\n    tanh: pureAndGlobal(\"math/tanh\", [\"es6.math.tanh\"], \"7.0.1\"),\n    trunc: pureAndGlobal(\"math/trunc\", [\"es6.math.trunc\"], \"7.0.1\"),\n  },\n\n  Number: {\n    EPSILON: pureAndGlobal(\"number/epsilon\", [\"es6.number.epsilon\"]),\n    MIN_SAFE_INTEGER: pureAndGlobal(\"number/min-safe-integer\", [\n      \"es6.number.min-safe-integer\",\n    ]),\n    MAX_SAFE_INTEGER: pureAndGlobal(\"number/max-safe-integer\", [\n      \"es6.number.max-safe-integer\",\n    ]),\n    isFinite: pureAndGlobal(\"number/is-finite\", [\"es6.number.is-finite\"]),\n    isInteger: pureAndGlobal(\"number/is-integer\", [\"es6.number.is-integer\"]),\n    isSafeInteger: pureAndGlobal(\"number/is-safe-integer\", [\n      \"es6.number.is-safe-integer\",\n    ]),\n    isNaN: pureAndGlobal(\"number/is-nan\", [\"es6.number.is-nan\"]),\n    parseFloat: pureAndGlobal(\"number/parse-float\", [\"es6.number.parse-float\"]),\n    parseInt: pureAndGlobal(\"number/parse-int\", [\"es6.number.parse-int\"]),\n  },\n\n  Object: {\n    assign: pureAndGlobal(\"object/assign\", [\"es6.object.assign\"]),\n    create: pureAndGlobal(\"object/create\", [\"es6.object.create\"]),\n    defineProperties: pureAndGlobal(\"object/define-properties\", [\n      \"es6.object.define-properties\",\n    ]),\n    defineProperty: pureAndGlobal(\"object/define-property\", [\n      \"es6.object.define-property\",\n    ]),\n    entries: pureAndGlobal(\"object/entries\", [\"es7.object.entries\"]),\n    freeze: pureAndGlobal(\"object/freeze\", [\"es6.object.freeze\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"object/get-own-property-descriptor\",\n      [\"es6.object.get-own-property-descriptor\"],\n    ),\n    getOwnPropertyDescriptors: pureAndGlobal(\n      \"object/get-own-property-descriptors\",\n      [\"es7.object.get-own-property-descriptors\"],\n    ),\n    getOwnPropertyNames: pureAndGlobal(\"object/get-own-property-names\", [\n      \"es6.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: pureAndGlobal(\"object/get-own-property-symbols\", [\n      \"es6.symbol\",\n    ]),\n    getPrototypeOf: pureAndGlobal(\"object/get-prototype-of\", [\n      \"es6.object.get-prototype-of\",\n    ]),\n    is: pureAndGlobal(\"object/is\", [\"es6.object.is\"]),\n    isExtensible: pureAndGlobal(\"object/is-extensible\", [\n      \"es6.object.is-extensible\",\n    ]),\n    isFrozen: pureAndGlobal(\"object/is-frozen\", [\"es6.object.is-frozen\"]),\n    isSealed: pureAndGlobal(\"object/is-sealed\", [\"es6.object.is-sealed\"]),\n    keys: pureAndGlobal(\"object/keys\", [\"es6.object.keys\"]),\n    preventExtensions: pureAndGlobal(\"object/prevent-extensions\", [\n      \"es6.object.prevent-extensions\",\n    ]),\n    seal: pureAndGlobal(\"object/seal\", [\"es6.object.seal\"]),\n    setPrototypeOf: pureAndGlobal(\"object/set-prototype-of\", [\n      \"es6.object.set-prototype-of\",\n    ]),\n    values: pureAndGlobal(\"object/values\", [\"es7.object.values\"]),\n  },\n\n  Promise: {\n    all: globalOnly(CommonIterators),\n    race: globalOnly(CommonIterators),\n  },\n\n  Reflect: {\n    apply: pureAndGlobal(\"reflect/apply\", [\"es6.reflect.apply\"]),\n    construct: pureAndGlobal(\"reflect/construct\", [\"es6.reflect.construct\"]),\n    defineProperty: pureAndGlobal(\"reflect/define-property\", [\n      \"es6.reflect.define-property\",\n    ]),\n    deleteProperty: pureAndGlobal(\"reflect/delete-property\", [\n      \"es6.reflect.delete-property\",\n    ]),\n    get: pureAndGlobal(\"reflect/get\", [\"es6.reflect.get\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"reflect/get-own-property-descriptor\",\n      [\"es6.reflect.get-own-property-descriptor\"],\n    ),\n    getPrototypeOf: pureAndGlobal(\"reflect/get-prototype-of\", [\n      \"es6.reflect.get-prototype-of\",\n    ]),\n    has: pureAndGlobal(\"reflect/has\", [\"es6.reflect.has\"]),\n    isExtensible: pureAndGlobal(\"reflect/is-extensible\", [\n      \"es6.reflect.is-extensible\",\n    ]),\n    ownKeys: pureAndGlobal(\"reflect/own-keys\", [\"es6.reflect.own-keys\"]),\n    preventExtensions: pureAndGlobal(\"reflect/prevent-extensions\", [\n      \"es6.reflect.prevent-extensions\",\n    ]),\n    set: pureAndGlobal(\"reflect/set\", [\"es6.reflect.set\"]),\n    setPrototypeOf: pureAndGlobal(\"reflect/set-prototype-of\", [\n      \"es6.reflect.set-prototype-of\",\n    ]),\n  },\n\n  String: {\n    at: pureOnly(\"string/at\", \"es7.string.at\"),\n    fromCodePoint: pureAndGlobal(\"string/from-code-point\", [\n      \"es6.string.from-code-point\",\n    ]),\n    raw: pureAndGlobal(\"string/raw\", [\"es6.string.raw\"]),\n  },\n\n  Symbol: {\n    // FIXME: Pure disabled to work around zloirock/core-js#262.\n    asyncIterator: globalOnly([\"es6.symbol\", \"es7.symbol.async-iterator\"]),\n    for: pureOnly(\"symbol/for\", \"es6.symbol\"),\n    hasInstance: pureOnly(\"symbol/has-instance\", \"es6.symbol\"),\n    isConcatSpreadable: pureOnly(\"symbol/is-concat-spreadable\", \"es6.symbol\"),\n    iterator: define(\"es6.symbol\", \"symbol/iterator\", CommonIterators),\n    keyFor: pureOnly(\"symbol/key-for\", \"es6.symbol\"),\n    match: pureAndGlobal(\"symbol/match\", [\"es6.regexp.match\"]),\n    replace: pureOnly(\"symbol/replace\", \"es6.symbol\"),\n    search: pureOnly(\"symbol/search\", \"es6.symbol\"),\n    species: pureOnly(\"symbol/species\", \"es6.symbol\"),\n    split: pureOnly(\"symbol/split\", \"es6.symbol\"),\n    toPrimitive: pureOnly(\"symbol/to-primitive\", \"es6.symbol\"),\n    toStringTag: pureOnly(\"symbol/to-string-tag\", \"es6.symbol\"),\n    unscopables: pureOnly(\"symbol/unscopables\", \"es6.symbol\"),\n  },\n};\n", "import type { Targets } from \"@babel/helper-define-polyfill-provider\";\n\nconst webPolyfills = {\n  \"web.timers\": {},\n  \"web.immediate\": {},\n  \"web.dom.iterable\": {},\n};\n\nconst purePolyfills = {\n  \"es6.parse-float\": {},\n  \"es6.parse-int\": {},\n  \"es7.string.at\": {},\n};\n\nexport default function (targets: Targets, method: string, polyfills: any) {\n  const targetNames = Object.keys(targets);\n  const isAnyTarget = !targetNames.length;\n  const isWebTarget = targetNames.some(name => name !== \"node\");\n\n  return {\n    ...polyfills,\n    ...(method === \"usage-pure\" ? purePolyfills : null),\n    ...(isAnyTarget || isWebTarget ? webPolyfills : null),\n  };\n}\n", "import semver from \"semver\";\n\nexport function hasMinVersion(\n  minVersion?: string | null,\n  runtimeVersion?: string | number | null,\n) {\n  // If the range is unavailable, we're running the script during Babel's\n  // build process, and we want to assume that all versions are satisfied so\n  // that the built output will include all definitions.\n  if (!runtimeVersion || !minVersion) return true;\n\n  runtimeVersion = String(runtimeVersion);\n\n  // semver.intersects() has some surprising behavior with comparing ranges\n  // with preprelease versions. We add '^' to ensure that we are always\n  // comparing ranges with ranges, which sidesteps this logic.\n  // For example:\n  //\n  //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n  //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n  //\n  // This is because the first falls back to\n  //\n  //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n  //\n  // and this fails because a prerelease version can only satisfy a range\n  // if it is a prerelease within the same major/minor/patch range.\n  //\n  // Note: If this is found to have issues, please also revist the logic in\n  // babel-core's availableHelper() API.\n  if (semver.valid(runtimeVersion)) runtimeVersion = `^${runtimeVersion}`;\n\n  return (\n    !semver.intersects(`<${minVersion}`, runtimeVersion) &&\n    !semver.intersects(`>=8.0.0`, runtimeVersion)\n  );\n}\n", "import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\nimport {\n  BuiltIns,\n  StaticProperties,\n  InstanceProperties,\n  CommonIterators,\n} from \"./built-in-definitions\";\nimport addPlatformSpecificPolyfills from \"./add-platform-specific-polyfills\";\nimport { hasMinVersion } from \"./helpers\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\nconst BABEL_RUNTIME = \"@babel/runtime-corejs2\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\nconst has = Function.call.bind(Object.hasOwnProperty);\n\ntype Options = {\n  [presetEnvCompat]?: {\n    entryInjectRegenerator: boolean;\n    noRuntimeName: boolean;\n  };\n  [runtimeCompat]?: {\n    useBabelRuntime: boolean;\n    runtimeVersion: string;\n    ext: string;\n  };\n};\n\nexport default defineProvider<Options>(function (\n  api,\n  {\n    [presetEnvCompat]: {\n      entryInjectRegenerator = false,\n      noRuntimeName = false,\n    } = {},\n    [runtimeCompat]: {\n      useBabelRuntime = false,\n      runtimeVersion = \"\",\n      ext = \".js\",\n    } = {},\n  },\n) {\n  const resolve = api.createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const { debug, shouldInjectPolyfill, method } = api;\n\n  const polyfills = addPlatformSpecificPolyfills(\n    api.targets,\n    method,\n    corejs2Polyfills,\n  );\n\n  const coreJSBase = useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js`\n    : method === \"usage-pure\"\n      ? \"core-js/library/fn\"\n      : \"core-js/modules\";\n\n  function inject(name: string | string[], utils) {\n    if (typeof name === \"string\") {\n      // Some polyfills aren't always available, for example\n      // web.dom.iterable when targeting node\n      if (has(polyfills, name) && shouldInjectPolyfill(name)) {\n        debug(name);\n        utils.injectGlobalImport(`${coreJSBase}/${name}.js`);\n      }\n      return;\n    }\n\n    name.forEach(name => inject(name, utils));\n  }\n\n  function maybeInjectPure(desc, hint, utils) {\n    let { pure, meta, name } = desc;\n\n    if (!pure || !shouldInjectPolyfill(name)) return;\n\n    if (\n      runtimeVersion &&\n      meta &&\n      meta.minRuntimeVersion &&\n      !hasMinVersion(meta && meta.minRuntimeVersion, runtimeVersion)\n    ) {\n      return;\n    }\n\n    // Unfortunately core-js and @babel/runtime-corejs2 don't have the same\n    // directory structure, so we need to special case this.\n    if (useBabelRuntime && pure === \"symbol/index\") pure = \"symbol\";\n\n    return utils.injectDefaultImport(`${coreJSBase}/${pure}${ext}`, hint);\n  }\n\n  return {\n    name: \"corejs2\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills,\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind === \"import\" && meta.source === \"core-js\") {\n        debug(null);\n\n        inject(Object.keys(polyfills), utils);\n\n        if (entryInjectRegenerator) {\n          utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n        }\n\n        path.remove();\n      }\n    },\n\n    usageGlobal(meta, utils): undefined {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m => m.includes(low));\n      }\n\n      inject(deps, utils);\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/is-iterable${ext}`,\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n\n        if (\n          meta.key === \"Symbol.iterator\" &&\n          shouldInjectPolyfill(\"es6.symbol\") &&\n          path.parentPath.isCallExpression({ callee: path.node }) &&\n          path.parentPath.node.arguments.length === 0\n        ) {\n          path.parentPath.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/get-iterator${ext}`,\n                \"getIterator\",\n              ),\n              [path.node.object],\n            ),\n          );\n          path.skip();\n\n          return;\n        }\n      }\n\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n      if (id) path.replaceWith(id);\n    },\n\n    visitor: method === \"usage-global\" && {\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          inject(\"web.dom.iterable\", api.getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        CommonIterators.forEach(name => inject(name, api.getUtils(path)));\n      },\n    },\n  };\n});\n"], "names": ["define", "name", "pure", "global", "meta", "pureAndGlobal", "minRuntimeVersion", "globalOnly", "pureOnly", "ArrayNatureIterators", "CommonIterators", "PromiseDependencies", "BuiltIns", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Map", "Number", "Promise", "RegExp", "Set", "Symbol", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "WeakMap", "WeakSet", "setImmediate", "clearImmediate", "parseFloat", "parseInt", "InstanceProperties", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "anchor", "big", "bind", "blink", "bold", "codePointAt", "copyWithin", "endsWith", "entries", "every", "fill", "filter", "finally", "find", "findIndex", "fixed", "flags", "flatMap", "fontcolor", "fontsize", "for<PERSON>ach", "includes", "indexOf", "italics", "keys", "lastIndexOf", "link", "map", "match", "padStart", "padEnd", "reduce", "reduceRight", "repeat", "replace", "search", "small", "some", "sort", "split", "startsWith", "strike", "sub", "sup", "toISOString", "toJSON", "toString", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "values", "corejs2Polyfills", "slice", "StaticProperties", "Array", "from", "isArray", "of", "Date", "now", "JSON", "stringify", "Math", "acosh", "asinh", "atanh", "cbrt", "clz32", "cosh", "expm1", "fround", "hypot", "imul", "log1p", "log10", "log2", "sign", "sinh", "tanh", "trunc", "EPSILON", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "isFinite", "isInteger", "isSafeInteger", "isNaN", "Object", "assign", "create", "defineProperties", "defineProperty", "freeze", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "all", "race", "Reflect", "apply", "construct", "deleteProperty", "get", "has", "ownKeys", "set", "String", "at", "fromCodePoint", "raw", "asyncIterator", "for", "hasInstance", "isConcatSpreadable", "iterator", "keyFor", "species", "toPrimitive", "toStringTag", "unscopables", "webPolyfills", "purePolyfills", "targets", "method", "polyfills", "targetNames", "isAnyTarget", "length", "isWebTarget", "hasMinVersion", "minVersion", "runtimeVersion", "semver", "valid", "intersects", "types", "t", "_babel", "default", "BABEL_RUNTIME", "presetEnvCompat", "runtimeCompat", "Function", "call", "hasOwnProperty", "define<PERSON>rovider", "api", "entryInjectRegenerator", "noRuntimeName", "useBabelRuntime", "ext", "resolve", "createMetaResolver", "static", "instance", "debug", "shouldInjectPolyfill", "addPlatformSpecificPolyfills", "coreJSBase", "inject", "utils", "injectGlobalImport", "maybeInjectPure", "desc", "hint", "injectDefaultImport", "runtimeName", "entryGlobal", "path", "kind", "source", "remove", "usageGlobal", "resolved", "deps", "object", "placement", "low", "toLowerCase", "m", "usagePure", "key", "replaceWith", "callExpression", "node", "right", "parentPath", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isCallExpression", "callee", "arguments", "skip", "id", "visitor", "YieldExpression", "delegate", "getUtils", "ForOfStatement|ArrayPattern"], "mappings": ";;;;;AAeA,MAAMA,MAAM,GAAGA,CACbC,IAAY,EACZC,IAAoB,EACpBC,MAAgB,GAAG,EAAE,EACrBC,IAAe,KACW;EAC1B,OAAO;IAAEH,IAAI;IAAEC,IAAI;IAAEC,MAAM;IAAEC;GAAM;AACrC,CAAC;AAED,MAAMC,aAAa,GAAGA,CACpBH,IAAY,EACZC,MAAgB,EAChBG,iBAAgC,GAAG,IAAI,KACpCN,MAAM,CAAcG,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,EAAEC,MAAM,EAAE;EAAEG;AAAkB,CAAC,CAAC;AAExE,MAAMC,UAAU,GAAIJ,MAAgB,IAClCH,MAAM,CAAcG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEA,MAAM,CAAC;AAE9C,MAAMK,QAAQ,GAAGA,CAACN,IAAY,EAAED,IAAY,KAC1CD,MAAM,CAAcC,IAAI,EAAEC,IAAI,EAAE,EAAE,CAAC;AAErC,MAAMO,oBAAoB,GAAG,CAC3B,sBAAsB,EACtB,oBAAoB,EACpB,kBAAkB,CACnB;AAEM,MAAMC,eAAe,GAAG,CAAC,qBAAqB,EAAE,GAAGD,oBAAoB,CAAC;AAE/E,MAAME,mBAAmB,GAAG,CAAC,sBAAsB,EAAE,aAAa,CAAC;AAE5D,MAAMC,QAAoD,GAAG;EAClEC,QAAQ,EAAEN,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC7CO,YAAY,EAAEP,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACrDQ,YAAY,EAAER,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACrDS,SAAS,EAAET,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/CU,UAAU,EAAEV,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjDW,UAAU,EAAEX,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjDY,GAAG,EAAEd,aAAa,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,GAAGK,eAAe,CAAC,CAAC;EAC1DU,MAAM,EAAEb,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAC9Cc,OAAO,EAAEhB,aAAa,CAAC,SAAS,EAAEM,mBAAmB,CAAC;EACtDW,MAAM,EAAEf,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAC9CgB,GAAG,EAAElB,aAAa,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,GAAGK,eAAe,CAAC,CAAC;EAC1Dc,MAAM,EAAEnB,aAAa,CAAC,cAAc,EAAE,CAAC,YAAY,CAAC,CAAC;EACrDoB,UAAU,EAAElB,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjDmB,iBAAiB,EAAEnB,UAAU,CAAC,CAAC,+BAA+B,CAAC,CAAC;EAChEoB,WAAW,EAAEpB,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EACnDqB,WAAW,EAAErB,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EACnDsB,OAAO,EAAExB,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,GAAGK,eAAe,CAAC,CAAC;EACxEoB,OAAO,EAAEzB,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,GAAGK,eAAe,CAAC,CAAC;EAExEqB,YAAY,EAAEvB,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;EACxDwB,cAAc,EAAExB,QAAQ,CAAC,iBAAiB,EAAE,eAAe,CAAC;EAC5DyB,UAAU,EAAEzB,QAAQ,CAAC,aAAa,EAAE,iBAAiB,CAAC;EACtD0B,QAAQ,EAAE1B,QAAQ,CAAC,WAAW,EAAE,eAAe;AACjD,CAAC;AAEM,MAAM2B,kBAA8D,GAAG;EAC5EC,gBAAgB,EAAE7B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;EAC1D8B,gBAAgB,EAAE9B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;EAC1D+B,gBAAgB,EAAE/B,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;EAC1DgC,gBAAgB,EAAEhC,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;EAC1DiC,MAAM,EAAEjC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACzCkC,GAAG,EAAElC,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACnCmC,IAAI,EAAEnC,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACvCoC,KAAK,EAAEpC,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCqC,IAAI,EAAErC,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;EACrCsC,WAAW,EAAEtC,UAAU,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACrDuC,UAAU,EAAEvC,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjDwC,QAAQ,EAAExC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC9CyC,OAAO,EAAEzC,UAAU,CAACE,oBAAoB,CAAC;EACzCwC,KAAK,EAAE1C,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;EACtC2C,IAAI,EAAE3C,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACpC4C,MAAM,EAAE5C,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACxC6C,OAAO,EAAE7C,UAAU,CAAC,CAAC,qBAAqB,EAAE,GAAGI,mBAAmB,CAAC,CAAC;EACpE0C,IAAI,EAAE9C,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACpC+C,SAAS,EAAE/C,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/CgD,KAAK,EAAEhD,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCiD,KAAK,EAAEjD,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCkD,OAAO,EAAElD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CmD,SAAS,EAAEnD,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/CoD,QAAQ,EAAEpD,UAAU,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC7CqD,OAAO,EAAErD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CsD,QAAQ,EAAEtD,UAAU,CAAC,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;EACnEuD,OAAO,EAAEvD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CwD,OAAO,EAAExD,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CyD,IAAI,EAAEzD,UAAU,CAACE,oBAAoB,CAAC;EACtCwD,WAAW,EAAE1D,UAAU,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACpD2D,IAAI,EAAE3D,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;EACrC4D,GAAG,EAAE5D,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;EAClC6D,KAAK,EAAE7D,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCN,IAAI,EAAEM,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACvC8D,QAAQ,EAAE9D,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC9C+D,MAAM,EAAE/D,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC1CgE,MAAM,EAAEhE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACxCiE,WAAW,EAAEjE,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EACnDkE,MAAM,EAAElE,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACzCmE,OAAO,EAAEnE,UAAU,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC3CoE,MAAM,EAAEpE,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACzCqE,KAAK,EAAErE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCsE,IAAI,EAAEtE,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACpCuE,IAAI,EAAEvE,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACpCwE,KAAK,EAAExE,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACvCyE,UAAU,EAAEzE,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD0E,MAAM,EAAE1E,UAAU,CAAC,CAAC,mBAAmB,CAAC,CAAC;EACzC2E,GAAG,EAAE3E,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACnC4E,GAAG,EAAE5E,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACnC6E,WAAW,EAAE7E,UAAU,CAAC,CAAC,wBAAwB,CAAC,CAAC;EACnD8E,MAAM,EAAE9E,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;EACxC+E,QAAQ,EAAE/E,UAAU,CAAC,CACnB,sBAAsB,EACtB,oBAAoB,EACpB,sBAAsB,CACvB,CAAC;EACFgF,IAAI,EAAEhF,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;EACrCiF,OAAO,EAAEjF,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EAC9CkF,QAAQ,EAAElF,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC9CmF,SAAS,EAAEnF,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;EAChDoF,SAAS,EAAEpF,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/CqF,MAAM,EAAErF,UAAU,CAACE,oBAAoB;AACzC,CAAC;;AAED;AACA,IAAI,iBAAiB,IAAIoF,gBAAgB,EAAE;EACzC1D,kBAAkB,CAAC2D,KAAK,GAAGvF,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC5D;AAEO,MAAMwF,gBAEZ,GAAG;EACFC,KAAK,EAAE;IACLC,IAAI,EAAE5F,aAAa,CAAC,YAAY,EAAE,CAChC,YAAY,EACZ,gBAAgB,EAChB,GAAGK,eAAe,CACnB,CAAC;IACFwF,OAAO,EAAE7F,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAChE8F,EAAE,EAAE9F,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,CAAC;GAC/C;EAED+F,IAAI,EAAE;IACJC,GAAG,EAAEhG,aAAa,CAAC,UAAU,EAAE,CAAC,cAAc,CAAC;GAChD;EAEDiG,IAAI,EAAE;IACJC,SAAS,EAAE/F,QAAQ,CAAC,gBAAgB,EAAE,YAAY;GACnD;EAEDgG,IAAI,EAAE;;;IAGJC,KAAK,EAAEpG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DqG,KAAK,EAAErG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DsG,KAAK,EAAEtG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DuG,IAAI,EAAEvG,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DwG,KAAK,EAAExG,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DyG,IAAI,EAAEzG,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5D0G,KAAK,EAAE1G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D2G,MAAM,EAAE3G,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAClE4G,KAAK,EAAE5G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D6G,IAAI,EAAE7G,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5D8G,KAAK,EAAE9G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/D+G,KAAK,EAAE/G,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC/DgH,IAAI,EAAEhH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DiH,IAAI,EAAEjH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DkH,IAAI,EAAElH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DmH,IAAI,EAAEnH,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC5DoH,KAAK,EAAEpH,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,EAAE,OAAO;GAC/D;EAEDe,MAAM,EAAE;IACNsG,OAAO,EAAErH,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAChEsH,gBAAgB,EAAEtH,aAAa,CAAC,yBAAyB,EAAE,CACzD,6BAA6B,CAC9B,CAAC;IACFuH,gBAAgB,EAAEvH,aAAa,CAAC,yBAAyB,EAAE,CACzD,6BAA6B,CAC9B,CAAC;IACFwH,QAAQ,EAAExH,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrEyH,SAAS,EAAEzH,aAAa,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACxE0H,aAAa,EAAE1H,aAAa,CAAC,wBAAwB,EAAE,CACrD,4BAA4B,CAC7B,CAAC;IACF2H,KAAK,EAAE3H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC5D4B,UAAU,EAAE5B,aAAa,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,CAAC,CAAC;IAC3E6B,QAAQ,EAAE7B,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC;GACrE;EAED4H,MAAM,EAAE;IACNC,MAAM,EAAE7H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7D8H,MAAM,EAAE9H,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7D+H,gBAAgB,EAAE/H,aAAa,CAAC,0BAA0B,EAAE,CAC1D,8BAA8B,CAC/B,CAAC;IACFgI,cAAc,EAAEhI,aAAa,CAAC,wBAAwB,EAAE,CACtD,4BAA4B,CAC7B,CAAC;IACF2C,OAAO,EAAE3C,aAAa,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAChEiI,MAAM,EAAEjI,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC7DkI,wBAAwB,EAAElI,aAAa,CACrC,oCAAoC,EACpC,CAAC,wCAAwC,CAC3C,CAAC;IACDmI,yBAAyB,EAAEnI,aAAa,CACtC,qCAAqC,EACrC,CAAC,yCAAyC,CAC5C,CAAC;IACDoI,mBAAmB,EAAEpI,aAAa,CAAC,+BAA+B,EAAE,CAClE,mCAAmC,CACpC,CAAC;IACFqI,qBAAqB,EAAErI,aAAa,CAAC,iCAAiC,EAAE,CACtE,YAAY,CACb,CAAC;IACFsI,cAAc,EAAEtI,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFuI,EAAE,EAAEvI,aAAa,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,CAAC;IACjDwI,YAAY,EAAExI,aAAa,CAAC,sBAAsB,EAAE,CAClD,0BAA0B,CAC3B,CAAC;IACFyI,QAAQ,EAAEzI,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrE0I,QAAQ,EAAE1I,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACrE2D,IAAI,EAAE3D,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACvD2I,iBAAiB,EAAE3I,aAAa,CAAC,2BAA2B,EAAE,CAC5D,+BAA+B,CAChC,CAAC;IACF4I,IAAI,EAAE5I,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACvD6I,cAAc,EAAE7I,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFuF,MAAM,EAAEvF,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC;GAC7D;EAEDgB,OAAO,EAAE;IACP8H,GAAG,EAAE5I,UAAU,CAACG,eAAe,CAAC;IAChC0I,IAAI,EAAE7I,UAAU,CAACG,eAAe;GACjC;EAED2I,OAAO,EAAE;IACPC,KAAK,EAAEjJ,aAAa,CAAC,eAAe,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAC5DkJ,SAAS,EAAElJ,aAAa,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACxEgI,cAAc,EAAEhI,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFmJ,cAAc,EAAEnJ,aAAa,CAAC,yBAAyB,EAAE,CACvD,6BAA6B,CAC9B,CAAC;IACFoJ,GAAG,EAAEpJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACtDkI,wBAAwB,EAAElI,aAAa,CACrC,qCAAqC,EACrC,CAAC,yCAAyC,CAC5C,CAAC;IACDsI,cAAc,EAAEtI,aAAa,CAAC,0BAA0B,EAAE,CACxD,8BAA8B,CAC/B,CAAC;IACFqJ,GAAG,EAAErJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACtDwI,YAAY,EAAExI,aAAa,CAAC,uBAAuB,EAAE,CACnD,2BAA2B,CAC5B,CAAC;IACFsJ,OAAO,EAAEtJ,aAAa,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACpE2I,iBAAiB,EAAE3I,aAAa,CAAC,4BAA4B,EAAE,CAC7D,gCAAgC,CACjC,CAAC;IACFuJ,GAAG,EAAEvJ,aAAa,CAAC,aAAa,EAAE,CAAC,iBAAiB,CAAC,CAAC;IACtD6I,cAAc,EAAE7I,aAAa,CAAC,0BAA0B,EAAE,CACxD,8BAA8B,CAC/B;GACF;EAEDwJ,MAAM,EAAE;IACNC,EAAE,EAAEtJ,QAAQ,CAAC,WAAW,EAAE,eAAe,CAAC;IAC1CuJ,aAAa,EAAE1J,aAAa,CAAC,wBAAwB,EAAE,CACrD,4BAA4B,CAC7B,CAAC;IACF2J,GAAG,EAAE3J,aAAa,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC;GACpD;EAEDmB,MAAM,EAAE;;IAENyI,aAAa,EAAE1J,UAAU,CAAC,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;IACtE2J,GAAG,EAAE1J,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IACzC2J,WAAW,EAAE3J,QAAQ,CAAC,qBAAqB,EAAE,YAAY,CAAC;IAC1D4J,kBAAkB,EAAE5J,QAAQ,CAAC,6BAA6B,EAAE,YAAY,CAAC;IACzE6J,QAAQ,EAAErK,MAAM,CAAC,YAAY,EAAE,iBAAiB,EAAEU,eAAe,CAAC;IAClE4J,MAAM,EAAE9J,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;IAChD4D,KAAK,EAAE/D,aAAa,CAAC,cAAc,EAAE,CAAC,kBAAkB,CAAC,CAAC;IAC1DqE,OAAO,EAAElE,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;IACjDmE,MAAM,EAAEnE,QAAQ,CAAC,eAAe,EAAE,YAAY,CAAC;IAC/C+J,OAAO,EAAE/J,QAAQ,CAAC,gBAAgB,EAAE,YAAY,CAAC;IACjDuE,KAAK,EAAEvE,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC;IAC7CgK,WAAW,EAAEhK,QAAQ,CAAC,qBAAqB,EAAE,YAAY,CAAC;IAC1DiK,WAAW,EAAEjK,QAAQ,CAAC,sBAAsB,EAAE,YAAY,CAAC;IAC3DkK,WAAW,EAAElK,QAAQ,CAAC,oBAAoB,EAAE,YAAY;;AAE5D,CAAC;;ACnTD,MAAMmK,YAAY,GAAG;EACnB,YAAY,EAAE,EAAE;EAChB,eAAe,EAAE,EAAE;EACnB,kBAAkB,EAAE;AACtB,CAAC;AAED,MAAMC,aAAa,GAAG;EACpB,iBAAiB,EAAE,EAAE;EACrB,eAAe,EAAE,EAAE;EACnB,eAAe,EAAE;AACnB,CAAC;AAEc,uCAAUC,OAAgB,EAAEC,MAAc,EAAEC,SAAc,EAAE;EACzE,MAAMC,WAAW,GAAG/C,MAAM,CAACjE,IAAI,CAAC6G,OAAO,CAAC;EACxC,MAAMI,WAAW,GAAG,CAACD,WAAW,CAACE,MAAM;EACvC,MAAMC,WAAW,GAAGH,WAAW,CAACnG,IAAI,CAAC5E,IAAI,IAAIA,IAAI,KAAK,MAAM,CAAC;EAE7D,OAAO;IACL,GAAG8K,SAAS;IACZ,IAAID,MAAM,KAAK,YAAY,GAAGF,aAAa,GAAG,IAAI,CAAC;IACnD,IAAIK,WAAW,IAAIE,WAAW,GAAGR,YAAY,GAAG,IAAI;GACrD;AACH;;ACtBO,SAASS,aAAaA,CAC3BC,UAA0B,EAC1BC,cAAuC,EACvC;;;;EAIA,IAAI,CAACA,cAAc,IAAI,CAACD,UAAU,EAAE,OAAO,IAAI;EAE/CC,cAAc,GAAGzB,MAAM,CAACyB,cAAc,CAAC;;;;;;;;;;;;;;;;;;;EAmBvC,IAAIC,MAAM,CAACC,KAAK,CAACF,cAAc,CAAC,EAAEA,cAAc,GAAI,IAAGA,cAAe,EAAC;EAEvE,OACE,CAACC,MAAM,CAACE,UAAU,CAAE,IAAGJ,UAAW,EAAC,EAAEC,cAAc,CAAC,IACpD,CAACC,MAAM,CAACE,UAAU,CAAE,SAAQ,EAAEH,cAAc,CAAC;AAEjD;;AC1BoE;EAE3DI,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAEnB,MAAME,aAAa,GAAG,wBAAwB;AAE9C,MAAMC,eAAe,GAAG,iDAAiD;AACzE,MAAMC,aAAa,GAAG,8CAA8C;AAEpE,MAAMtC,GAAG,GAAGuC,QAAQ,CAACC,IAAI,CAACxJ,IAAI,CAACuF,MAAM,CAACkE,cAAc,CAAC;AAcrD,YAAeC,cAAc,CAAU,UACrCC,GAAG,EACH;EACE,CAACN,eAAe,GAAG;IACjBO,sBAAsB,GAAG,KAAK;IAC9BC,aAAa,GAAG;GACjB,GAAG,EAAE;EACN,CAACP,aAAa,GAAG;IACfQ,eAAe,GAAG,KAAK;IACvBlB,cAAc,GAAG,EAAE;IACnBmB,GAAG,GAAG;GACP,GAAG;AACN,CAAC,EACD;EACA,MAAMC,OAAO,GAAGL,GAAG,CAACM,kBAAkB,CAAC;IACrCxM,MAAM,EAAES,QAAQ;IAChBgM,MAAM,EAAE7G,gBAAgB;IACxB8G,QAAQ,EAAE1K;GACX,CAAC;EAEF,MAAM;IAAE2K,KAAK;IAAEC,oBAAoB;IAAEjC;GAAQ,GAAGuB,GAAG;EAEnD,MAAMtB,SAAS,GAAGiC,4BAA4B,CAC5CX,GAAG,CAACxB,OAAO,EACXC,MAAM,EACNjF,gBACF,CAAC;EAED,MAAMoH,UAAU,GAAGT,eAAe,GAC7B,GAAEV,aAAc,UAAS,GAC1BhB,MAAM,KAAK,YAAY,GACrB,oBAAoB,GACpB,iBAAiB;EAEvB,SAASoC,MAAMA,CAACjN,IAAuB,EAAEkN,KAAK,EAAE;IAC9C,IAAI,OAAOlN,IAAI,KAAK,QAAQ,EAAE;;;MAG5B,IAAIyJ,GAAG,CAACqB,SAAS,EAAE9K,IAAI,CAAC,IAAI8M,oBAAoB,CAAC9M,IAAI,CAAC,EAAE;QACtD6M,KAAK,CAAC7M,IAAI,CAAC;QACXkN,KAAK,CAACC,kBAAkB,CAAE,GAAEH,UAAW,IAAGhN,IAAK,KAAI,CAAC;;MAEtD;;IAGFA,IAAI,CAAC2D,OAAO,CAAC3D,IAAI,IAAIiN,MAAM,CAACjN,IAAI,EAAEkN,KAAK,CAAC,CAAC;;EAG3C,SAASE,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAEJ,KAAK,EAAE;IAC1C,IAAI;MAAEjN,IAAI;MAAEE,IAAI;MAAEH;KAAM,GAAGqN,IAAI;IAE/B,IAAI,CAACpN,IAAI,IAAI,CAAC6M,oBAAoB,CAAC9M,IAAI,CAAC,EAAE;IAE1C,IACEqL,cAAc,IACdlL,IAAI,IACJA,IAAI,CAACE,iBAAiB,IACtB,CAAC8K,aAAa,CAAChL,IAAI,IAAIA,IAAI,CAACE,iBAAiB,EAAEgL,cAAc,CAAC,EAC9D;MACA;;;;;IAKF,IAAIkB,eAAe,IAAItM,IAAI,KAAK,cAAc,EAAEA,IAAI,GAAG,QAAQ;IAE/D,OAAOiN,KAAK,CAACK,mBAAmB,CAAE,GAAEP,UAAW,IAAG/M,IAAK,GAAEuM,GAAI,EAAC,EAAEc,IAAI,CAAC;;EAGvE,OAAO;IACLtN,IAAI,EAAE,SAAS;IAEfwN,WAAW,EAAElB,aAAa,GAAG,IAAI,GAAGT,aAAa;IAEjDf,SAAS;IAET2C,WAAWA,CAACtN,IAAI,EAAE+M,KAAK,EAAEQ,IAAI,EAAE;MAC7B,IAAIvN,IAAI,CAACwN,IAAI,KAAK,QAAQ,IAAIxN,IAAI,CAACyN,MAAM,KAAK,SAAS,EAAE;QACvDf,KAAK,CAAC,IAAI,CAAC;QAEXI,MAAM,CAACjF,MAAM,CAACjE,IAAI,CAAC+G,SAAS,CAAC,EAAEoC,KAAK,CAAC;QAErC,IAAIb,sBAAsB,EAAE;UAC1Ba,KAAK,CAACC,kBAAkB,CAAC,gCAAgC,CAAC;;QAG5DO,IAAI,CAACG,MAAM,EAAE;;KAEhB;IAEDC,WAAWA,CAAC3N,IAAI,EAAE+M,KAAK,EAAa;MAClC,MAAMa,QAAQ,GAAGtB,OAAO,CAACtM,IAAI,CAAC;MAC9B,IAAI,CAAC4N,QAAQ,EAAE;MAEf,IAAIC,IAAI,GAAGD,QAAQ,CAACV,IAAI,CAACnN,MAAM;MAE/B,IACE6N,QAAQ,CAACJ,IAAI,KAAK,QAAQ,IAC1B,QAAQ,IAAIxN,IAAI,IAChBA,IAAI,CAAC8N,MAAM,IACX9N,IAAI,CAAC+N,SAAS,KAAK,WAAW,EAC9B;QACA,MAAMC,GAAG,GAAGhO,IAAI,CAAC8N,MAAM,CAACG,WAAW,EAAE;QACrCJ,IAAI,GAAGA,IAAI,CAAC9K,MAAM,CAACmL,CAAC,IAAIA,CAAC,CAACzK,QAAQ,CAACuK,GAAG,CAAC,CAAC;;MAG1ClB,MAAM,CAACe,IAAI,EAAEd,KAAK,CAAC;KACpB;IAEDoB,SAASA,CAACnO,IAAI,EAAE+M,KAAK,EAAEQ,IAAI,EAAE;MAC3B,IAAIvN,IAAI,CAACwN,IAAI,KAAK,IAAI,EAAE;QACtB,IAAIxN,IAAI,CAACoO,GAAG,KAAK,iBAAiB,EAAE;UAClCb,IAAI,CAACc,WAAW,CACd9C,CAAC,CAAC+C,cAAc,CACdvB,KAAK,CAACK,mBAAmB,CACtB,GAAEP,UAAW,eAAcR,GAAI,EAAC,EACjC,YACF,CAAC,EACD,CAAEkB,IAAI,CAACgB,IAAI,CAAwBC,KAAK,CAAC;WAE7C,CAAC;;;QAGH;;MAGF,IAAIjB,IAAI,CAACkB,UAAU,CAACC,iBAAiB,CAAC;QAAEC,QAAQ,EAAE;OAAU,CAAC,EAAE;MAE/D,IAAI3O,IAAI,CAACwN,IAAI,KAAK,UAAU,EAAE;;QAE5B,IAAI,CAACD,IAAI,CAACqB,kBAAkB,EAAE,EAAE;QAChC,IAAI,CAACrB,IAAI,CAACsB,YAAY,EAAE,EAAE;QAE1B,IACE7O,IAAI,CAACoO,GAAG,KAAK,iBAAiB,IAC9BzB,oBAAoB,CAAC,YAAY,CAAC,IAClCY,IAAI,CAACkB,UAAU,CAACK,gBAAgB,CAAC;UAAEC,MAAM,EAAExB,IAAI,CAACgB;SAAM,CAAC,IACvDhB,IAAI,CAACkB,UAAU,CAACF,IAAI,CAACS,SAAS,CAAClE,MAAM,KAAK,CAAC,EAC3C;UACAyC,IAAI,CAACkB,UAAU,CAACJ,WAAW,CACzB9C,CAAC,CAAC+C,cAAc,CACdvB,KAAK,CAACK,mBAAmB,CACtB,GAAEP,UAAW,gBAAeR,GAAI,EAAC,EAClC,aACF,CAAC,EACD,CAACkB,IAAI,CAACgB,IAAI,CAACT,MAAM,CACnB,CACF,CAAC;UACDP,IAAI,CAAC0B,IAAI,EAAE;UAEX;;;MAIJ,MAAMrB,QAAQ,GAAGtB,OAAO,CAACtM,IAAI,CAAC;MAC9B,IAAI,CAAC4N,QAAQ,EAAE;MAEf,MAAMsB,EAAE,GAAGjC,eAAe,CAACW,QAAQ,CAACV,IAAI,EAAEU,QAAQ,CAAC/N,IAAI,EAAEkN,KAAK,CAAC;MAC/D,IAAImC,EAAE,EAAE3B,IAAI,CAACc,WAAW,CAACa,EAAE,CAAC;KAC7B;IAEDC,OAAO,EAAEzE,MAAM,KAAK,cAAc,IAAI;;MAEpC0E,eAAeA,CAAC7B,IAAiC,EAAE;QACjD,IAAIA,IAAI,CAACgB,IAAI,CAACc,QAAQ,EAAE;UACtBvC,MAAM,CAAC,kBAAkB,EAAEb,GAAG,CAACqD,QAAQ,CAAC/B,IAAI,CAAC,CAAC;;OAEjD;;MAGD,6BAA6BgC,CAC3BhC,IAAiD,EACjD;QACAjN,eAAe,CAACkD,OAAO,CAAC3D,IAAI,IAAIiN,MAAM,CAACjN,IAAI,EAAEoM,GAAG,CAACqD,QAAQ,CAAC/B,IAAI,CAAC,CAAC,CAAC;;;GAGtE;AACH,CAAC,CAAC;;;;"}