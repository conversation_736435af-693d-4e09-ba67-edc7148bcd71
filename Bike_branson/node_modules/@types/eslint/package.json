{"name": "@types/eslint", "version": "8.56.12", "description": "TypeScript definitions for eslint", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "githubUsername": "j-f1", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "githubUsername": "JasonHK", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "githubUsername": "brad<PERSON><PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "githubUsername": "bmish", "url": "https://github.com/bmish"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "typesPublisherContentHash": "804eabaa45a9d9fa17f60c85a8d5139ef8826929e60e1de0ada0c3dbf17f6e2b", "typeScriptVersion": "4.8"}