{"name": "ast-types-flow", "version": "0.0.8", "description": "Flow types for the Javascript AST", "main": "lib/types.js", "files": ["lib"], "scripts": {"build": "gulp build", "test": "flow"}, "repository": {"type": "git", "url": "git+https://github.com/kyldvs/ast-types-flow.git"}, "keywords": ["flow", "ast", "javascript"], "author": "kyldvs", "license": "MIT", "bugs": {"url": "https://github.com/kyldvs/ast-types-flow/issues"}, "homepage": "https://github.com/kyldvs/ast-types-flow#readme", "devDependencies": {"gulp": "^3.9.0", "gulp-util": "^3.0.6", "jscodeshift": "^0.3.7", "nuclide-node-transpiler": "0.0.30", "through2": "^2.0.0"}}