'use strict';
// https://github.com/tc39/proposal-explicit-resource-management
require('../modules/esnext.suppressed-error.constructor');
require('../modules/esnext.async-disposable-stack.constructor');
require('../modules/esnext.async-iterator.async-dispose');
require('../modules/esnext.disposable-stack.constructor');
require('../modules/esnext.iterator.dispose');
require('../modules/esnext.symbol.async-dispose');
require('../modules/esnext.symbol.dispose');
