{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAG1B,mDAA+C;AAC/C,uCAAyC;AACzC,gFAA6E;AAC7E,kFAA+E;AAC/E,gFAA6E;AAC7E,wEAAqE;AAErE,8EAG8C;AAG9C,6EAA0E;AAC1E,iFAAkF;AAGlF,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,4CAA4C,CAAC,CAAC;AAEhE;;;;GAIG;AACH,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA6B,CAAC;AAC9D,SAAS,iBAAiB;IACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC3B,CAAC;AA6NC,8CAAiB;AA3NnB;;;;GAIG;AACH,SAAS,gBAAgB,CACvB,aAA4B,EAC5B,2BAAoC;IAEpC,OAAO,CACL,CAAC,aAAa,CAAC,QAAQ;QACrB,IAAA,yCAAmB,EAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QAC7D,CAAC,2BAA2B,IAAI,IAAA,2CAAoB,EAAC,aAAa,CAAC,CAAC;QACpE,CAAC,2BAA2B;YAC1B,aAAa,CAAC,oBAAoB;YAClC,IAAA,2CAAoB,EAAC,aAAa,CAAC,CAAC;QACtC,IAAA,6CAAqB,EAAC,aAAa,CAAC,CACrC,CAAC;AACJ,CAAC;AAkBD,SAAS,KAAK,CACZ,IAAY,EACZ,OAAW;IAEX,MAAM,EAAE,GAAG,EAAE,GAAG,yBAAyB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAChE,OAAO,GAAG,CAAC;AACb,CAAC;AA4KC,sBAAK;AA1KP,SAAS,yBAAyB,CAChC,IAAY,EACZ,OAAsB,EACtB,sBAA+B;IAE/B;;OAEG;IACH,MAAM,aAAa,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEzD;;OAEG;IACH,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,2CAA2C,EAAE;QACxD,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;KACH;IAED;;OAEG;IACH,MAAM,GAAG,GAAG,IAAA,mCAAgB,EAAC,aAAa,CAAC,CAAC;IAE5C;;OAEG;IACH,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,4BAAY,EACtC,GAAG,EACH,aAAa,EACb,sBAAsB,CACvB,CAAC;IAEF,OAAO;QACL,GAAG,EAAE,MAAgB;QACrB,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;QACpD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;KACrD,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,IAAY,EACZ,OAAW;IAEX,OAAO,yBAAyB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACxD,CAAC;AA+HC,8CAAiB;AA7HnB,IAAI,6BAA6B,GAAmC,EAAE,CAAC;AACvE,mFAAmF;AACnF,SAAS,kCAAkC;IACzC,6BAA6B,GAAG,EAAE,CAAC;AACrC,CAAC;AA6HC,gFAAkC;AA3HpC,SAAS,wBAAwB,CAC/B,IAAY,EACZ,OAAU;;IAEV;;OAEG;IACH,MAAM,aAAa,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEzD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,IACE,OAAO,OAAO,CAAC,2CAA2C;YACxD,SAAS;YACX,OAAO,CAAC,2CAA2C,EACnD;YACA,aAAa,CAAC,2CAA2C,GAAG,IAAI,CAAC;SAClE;KACF;IAED;;;;OAIG;IACH,IACE,aAAa,CAAC,SAAS;QACvB,CAAC,aAAa,CAAC,QAAQ;QACvB,CAAA,MAAA,aAAa,CAAC,QAAQ,0CAAE,MAAM,IAAG,CAAC,EAClC;QACA,aAAa,CAAC,QAAQ,GAAG;YACvB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAChB,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,QAAQ,EAAE;oBAC/C,MAAM,eAAe,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACzD,IAAI,eAAe,EAAE;wBACnB,MAAM,eAAe,CAAC;qBACvB;yBAAM;wBACL,GAAG,CACD,oFAAoF,EACpF,UAAU,CACX,CAAC;wBACF,MAAM,UAAU,GAAG,IAAA,iDAA2B,EAAC,UAAU,CAAC,CAAC;wBAC3D,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;wBAC7C,MAAM,UAAU,CAAC;qBAClB;iBACF;YACH,CAAC;SACF,CAAC;KACH;IAED;;OAEG;IACH,MAAM,2BAA2B,GAC/B,aAAa,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAA,MAAA,aAAa,CAAC,QAAQ,0CAAE,MAAM,IAAG,CAAC,CAAC;IAEvE;;;;;;;OAOG;IACH,IAAI,aAAa,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,EAAE;QAC/C,6BAA6B,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,CAAC,6BAA6B,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;KAC9D;IAED,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GACpB,aAAa,CAAC,SAAS;QACvB,OAAO,CAAC,QAAQ;QAChB,6BAA6B,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjD,CAAC,CAAC,IAAA,6CAAqB,EAAC,aAAa,CAAC;QACtC,CAAC,CAAC,gBAAgB,CAAC,aAAa,EAAE,2BAA2B,CAAE,CAAC;IAEpE;;;OAGG;IACH,MAAM,sBAAsB,GAC1B,OAAO,aAAa,CAAC,gBAAgB,KAAK,SAAS;QACjD,CAAC,CAAC,aAAa,CAAC,gBAAgB;QAChC,CAAC,CAAC,IAAI,CAAC;IAEX,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,4BAAY,EACtC,GAAG,EACH,aAAa,EACb,sBAAsB,CACvB,CAAC;IAEF;;;OAGG;IACH,IAAI,OAAO,IAAI,aAAa,CAAC,2CAA2C,EAAE;QACxE,MAAM,KAAK,GAAG,IAAA,+DAAgC,EAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7D,IAAI,KAAK,EAAE;YACT,MAAM,IAAA,sBAAY,EAAC,KAAK,CAAC,CAAC;SAC3B;KACF;IAED;;OAEG;IACH,OAAO;QACL,GAAG,EAAE,MAAgB;QACrB,QAAQ,EAAE;YACR,sBAAsB,EAAE,2BAA2B;YACnD,OAAO;YACP,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;SACrD;KACF,CAAC;AACJ,CAAC;AAKC,4DAAwB"}