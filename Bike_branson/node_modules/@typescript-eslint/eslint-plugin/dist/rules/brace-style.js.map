{"version": 3, "file": "brace-style.js", "sourceRoot": "", "sources": ["../../src/rules/brace-style.ts"], "names": [], "mappings": ";;AAMA,kCAAwD;AACxD,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,aAAa,CAAC,CAAC;AAKlD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;KAC7B;IACD,cAAc,EAAE,CAAC,MAAM,CAAC;IACxB,MAAM,CAAC,OAAO;QACZ,MAAM,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,GAAG,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;QAC7D,uFAAuF;QACvF,OAAO,CAAC,OAAO,CAAC;QAElB,MAAM,aAAa,GAAG,KAAK,KAAK,QAAQ,CAAC;QACzC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC;;WAEG;QACH,SAAS,iBAAiB,CACxB,iBAAiC,EACjC,iBAAiC;YAEjC,IACE,eAAe;gBACf,IAAA,wBAAiB,EAAC,iBAAiB,EAAE,iBAAiB,CAAC,EACvD;gBACA,OAAO;aACR;YAED,MAAM,uBAAuB,GAC3B,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAE,CAAC;YAChD,MAAM,uBAAuB,GAC3B,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAE,CAAC;YAChD,MAAM,sBAAsB,GAC1B,UAAU,CAAC,aAAa,CAAC,iBAAiB,CAAE,CAAC;YAE/C,IACE,CAAC,aAAa;gBACd,CAAC,IAAA,wBAAiB,EAAC,uBAAuB,EAAE,iBAAiB,CAAC,EAC9D;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,iBAAiB;oBACvB,SAAS,EAAE,cAAc;oBACzB,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,MAAM,SAAS,GAAmB;4BAChC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;4BAChC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC3B,CAAC;wBACF,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CACvC,SAAS,CAAC,CAAC,CAAC,EACZ,SAAS,CAAC,CAAC,CAAC,CACb,CAAC;wBAEF,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE;4BACtB,OAAO,IAAI,CAAC;yBACb;wBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;oBAChD,CAAC;iBACF,CAAC,CAAC;aACJ;YAED,IACE,aAAa;gBACb,IAAA,wBAAiB,EAAC,uBAAuB,EAAE,iBAAiB,CAAC,EAC7D;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,iBAAiB;oBACvB,SAAS,EAAE,cAAc;oBACzB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC;iBAC9D,CAAC,CAAC;aACJ;YAED,IACE,IAAA,wBAAiB,EAAC,iBAAiB,EAAE,sBAAsB,CAAC;gBAC5D,sBAAsB,KAAK,iBAAiB,EAC5C;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,iBAAiB;oBACvB,SAAS,EAAE,eAAe;oBAC1B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAAC;iBAC7D,CAAC,CAAC;aACJ;YAED,IACE,IAAA,wBAAiB,EAAC,uBAAuB,EAAE,iBAAiB,CAAC;gBAC7D,uBAAuB,KAAK,iBAAiB,EAC7C;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,iBAAiB;oBACvB,SAAS,EAAE,iBAAiB;oBAC5B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC;iBAC9D,CAAC,CAAC;aACJ;QACH,CAAC;QAED,uCACK,KAAK,KACR,gCAAgC,CAC9B,IAAuD;gBAEvD,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAE,CAAC;gBAEpD,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAChD,CAAC;YACD,iBAAiB,CAAC,IAAI;gBACpB,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAE,CAAC;gBACpD,MAAM,YAAY,GAAG,UAAU,CAAC,cAAc,CAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CACpD,CAAC;gBAEH,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAChD,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}