{"version": 3, "file": "semi.js", "sourceRoot": "", "sources": ["../../src/rules/semi.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,MAAM,CAAC,CAAC;AAK3C,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,oCAAoC;YACpC,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,+FAA+F;QAC/F,QAAQ,EAAE,MAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,mCAAI;YAClC,WAAW,EAAE,oBAAoB;YACjC,SAAS,EAAE,kBAAkB;SAC9B;KACF;IACD,cAAc,EAAE;QACd,QAAQ;QACR;YACE,sBAAsB,EAAE,KAAK;YAC7B,gCAAgC,EAAE,KAAK;SACxC;KACF;IACD,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,iBAAiB,GACrB,KAAK,CAAC,mBAA2D,CAAC;QAEpE;;;;;;;UAOE;QACF,MAAM,YAAY,GAAG;YACnB,sBAAc,CAAC,kBAAkB;YACjC,sBAAc,CAAC,4BAA4B;YAC3C,sBAAc,CAAC,iBAAiB;YAChC,sBAAc,CAAC,kBAAkB;YACjC,sBAAc,CAAC,yBAAyB;YACxC,sBAAc,CAAC,sBAAsB;YACrC,sBAAc,CAAC,6BAA6B;SAC7C,CAAC,MAAM,CAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC5C,GAAG,CAAC,IAAc,CAAC,GAAG,iBAAiB,CAAC;YACxC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,qDACK,KAAK,GACL,YAAY,KACf,wBAAwB,CAAC,IAAI;gBAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE;oBACnE,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;iBACtC;YACH,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}