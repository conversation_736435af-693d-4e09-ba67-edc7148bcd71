{"version": 3, "file": "consistent-indexed-object-style.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-indexed-object-style.ts"], "names": [], "mappings": ";;AACA,oDAAoE;AAEpE,kCAAqC;AAKrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uCAAuC;YACpD,WAAW,EAAE,QAAQ;SACtB;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,gDAAgD;YAC9D,oBAAoB,EAAE,gDAAgD;SACvE;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;aACpC;SACF;KACF;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,YAAY,CACnB,OAA+B,EAC/B,IAA8D,EAC9D,QAAyC,EACzC,MAAc,EACd,OAAe,EACf,OAAO,GAAG,IAAI;YAEd,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO;aACR;YACD,MAAM,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;YAEzB,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;gBACnD,OAAO;aACR;YAED,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;YAEtC,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO;aACR;YAED,IAAI,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;gBAChD,OAAO;aACR;YACD,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC;YACzC,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO;aACR;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;YACxC,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO;aACR;YAED,IAAI,QAAQ,EAAE;gBACZ,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACjC,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7D,IAAI,QAAQ,EAAE;oBACZ,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CACzC,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,eAAe;wBACpB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wBACzC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5C,CAAC;oBACF,IAAI,UAAU,EAAE;wBACd,OAAO;qBACR;iBACF;aACF;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,cAAc;gBACzB,GAAG,EAAE,OAAO;oBACV,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE;wBAC1B,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBACvD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;wBAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ;4BAC5B,CAAC,CAAC,mBAAmB,GAAG,KAAK,KAAK,IAAI;4BACtC,CAAC,CAAC,UAAU,GAAG,KAAK,KAAK,GAAG,CAAC;wBAC/B,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC;oBACjE,CAAC;oBACH,CAAC,CAAC,IAAI;aACT,CAAC,CAAC;QACL,CAAC;QAED,uCACK,CAAC,IAAI,KAAK,iBAAiB,IAAI;YAChC,eAAe,CAAC,IAAI;;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;oBAC/C,OAAO;iBACR;gBACD,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;oBAC9B,OAAO;iBACR;gBAED,MAAM,MAAM,GAAG,MAAA,IAAI,CAAC,cAAc,0CAAE,MAAM,CAAC;gBAC3C,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,MAAK,CAAC,EAAE;oBACxB,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,sBAAsB;oBACjC,GAAG,CAAC,KAAK;wBACP,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3C,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC;oBAC/D,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC,GACC,CAAC,IAAI,KAAK,QAAQ,IAAI;YACvB,aAAa,CAAC,IAAI;gBAChB,MAAM,MAAM,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBAC3C,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACvD,CAAC;YACD,sBAAsB,CAAC,IAAI;;gBACzB,IAAI,YAAY,GAAG,EAAE,CAAC;gBAEtB,IAAI,CAAC,MAAA,MAAA,IAAI,CAAC,cAAc,0CAAE,MAAM,mCAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClD,YAAY,GAAG,IAAI,MAAA,IAAI,CAAC,cAAc,0CAAE,MAAM,CAC3C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;iBAClB;gBAED,YAAY,CACV,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,IAAI,EACJ,IAAI,CAAC,EAAE,EACP,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY,KAAK,EACxC,GAAG,EACH,CAAC,CAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,CAAA,CACtB,CAAC;YACJ,CAAC;SACF,CAAC,EACF;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,qBAAqB,CAC5B,IAAmB;IAEnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;QACvE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE;YAC9D,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;QACD,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3C;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}