{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/format.ts"], "names": [], "mappings": ";;;AAAA,mCAA4C;AAE5C;;;;;;EAME;AAEF;;;;EAIE;AAEF,SAAS,YAAY,CAAC,IAAY;IAChC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AACD,SAAS,kBAAkB,CAAC,IAAY;IACtC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AACD,SAAS,iBAAiB,CAAC,IAAY;IACrC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAY,EAAE,OAAgB;IACzD,SAAS,eAAe,CAAC,IAAY;QACnC,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;IACpE,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnB,OAAO,KAAK,CAAC;SACd;QACD,IAAI,OAAO,KAAK,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YACxC,IAAI,OAAO,EAAE;gBACX,OAAO,KAAK,CAAC;aACd;SACF;aAAM;YACL,OAAO,GAAG,CAAC,OAAO,CAAC;SACpB;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,0DAA0D;AAC1D,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,aAAa,GAAG,KAAK,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnB,IAAI,aAAa,EAAE;gBACjB,OAAO,KAAK,CAAC;aACd;YACD,aAAa,GAAG,IAAI,CAAC;SACtB;aAAM;YACL,aAAa,GAAG,KAAK,CAAC;SACvB;KACF;IACD,OAAO,CAAC,aAAa,CAAC;AACxB,CAAC;AAED,MAAM,+BAA+B,GAEjC;IACF,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,YAAY;IAC5C,CAAC,yBAAiB,CAAC,gBAAgB,CAAC,EAAE,kBAAkB;IACxD,CAAC,yBAAiB,CAAC,SAAS,CAAC,EAAE,WAAW;IAC1C,CAAC,yBAAiB,CAAC,eAAe,CAAC,EAAE,iBAAiB;IACtD,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,WAAW;IAC3C,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,WAAW;CAC5C,CAAC;AAEO,0EAA+B"}