{"version": 3, "file": "no-duplicate-imports.js", "sourceRoot": "", "sources": ["../../src/rules/no-duplicate-imports.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,sBAAsB,CAAC,CAAC;AAK3D,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,sBAAsB,CAAC;QACpC,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4BAA4B;YACzC,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,kCACH,QAAQ,CAAC,IAAI,CAAC,QAAQ,KACzB,UAAU,EAAE,uCAAuC,EACnD,YAAY,EAAE,sDAAsD,EACpE,UAAU,EAAE,uCAAuC,EACnD,YAAY,EAAE,sDAAsD,GACrE;KACF;IACD,cAAc,EAAE;QACd;YACE,cAAc,EAAE,KAAK;SACtB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;QAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACpC,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAE9B,SAAS,MAAM,CACb,SAAqB,EACrB,IAAmB,EACnB,MAAc;YAEd,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS;gBACT,IAAI;gBACJ,IAAI,EAAE;oBACJ,MAAM;iBACP;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,iBAAiB,CAAC,IAAgC;YACzD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAC1B,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAC/D,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,IAAgC;YACvD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChC,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC/C,IACE,cAAc;oBACZ,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC9B,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EACjC;oBACA,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;iBACnC;gBAED,IAAI,cAAc,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC5C,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;iBACrC;gBACD,IAAI,cAAc,EAAE;oBAClB,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC9B;qBAAM;oBACL,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;iBAC/B;aACF;QACH,CAAC;QAED,SAAS,eAAe,CACtB,IAAqE;YAErE,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAChC,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC1B,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;iBACnC;gBACD,IAAI,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACjE,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;iBACrC;gBACD,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACxB;QACH,CAAC;QAED,uCACK,KAAK,KACR,iBAAiB,CAAC,IAAI;gBACpB,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;oBAC9B,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,OAAO;iBACR;gBACD,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;YACD,sBAAsB,CAAC,IAAI;;gBACzB,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;oBAChD,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,OAAO;iBACR;gBACD,MAAA,KAAK,CAAC,sBAAsB,sDAAG,IAAI,CAAC,CAAC;YACvC,CAAC;YACD,oBAAoB,CAAC,IAAI;;gBACvB,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;oBAChD,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,OAAO;iBACR;gBACD,MAAA,KAAK,CAAC,oBAAoB,sDAAG,IAAI,CAAC,CAAC;YACrC,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}