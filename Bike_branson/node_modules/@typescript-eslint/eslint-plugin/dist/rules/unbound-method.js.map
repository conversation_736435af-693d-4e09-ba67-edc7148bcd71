{"version": 3, "file": "unbound-method.js", "sourceRoot": "", "sources": ["../../src/rules/unbound-method.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAChC,kCAAuC;AAcvC;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC;IACtC,aAAa;IACb,cAAc;IACd,iBAAiB;IACjB,gBAAgB;IAChB,oBAAoB;IACpB,yBAAyB;IACzB,uBAAuB;IACvB,wBAAwB;IACxB,wBAAwB;IACxB,aAAa;IACb,kCAAkC;IAClC,wBAAwB;IACxB,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,2BAA2B;IAC3B,aAAa;IACb,wBAAwB;CACzB,CAAC,CAAC;AACH,MAAM,iBAAiB,GAAG;IACxB,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;CACE,CAAC;AACX,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;IAC7D,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE;QAC1B,+EAA+E;QAC/E,qEAAqE;QACrE,OAAO,EAAE,CAAC;KACX;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;SACtC,MAAM,CACL,IAAI,CAAC,EAAE,CACL,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACrB,OAAQ,MAAkC,CAAC,IAAI,CAAC,KAAK,UAAU,CAClE;SACA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC;AACzC,CAAC,CAAC;KACC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;KAC7C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAEtD,MAAM,aAAa,GAAG,CACpB,MAAiB,EACjB,iBAA4C,EACnC,EAAE;IACX,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE;QACrB,sEAAsE;QACtE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,CAAC,CAAC,iBAAiB;QACnB,iBAAiB,KAAK,gBAAgB,CAAC,aAAa,EAAE,CACvD,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAAmB,EAAiB,EAAE,CACzD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAE7D,MAAM,iBAAiB,GAAG,CAAC,IAA+B,EAAU,EAAE,CACpE,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;AAE9D,MAAM,YAAY,GAChB,oFAAoF,CAAC;AAEvF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;YAChE,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,YAAY;YACrB,4BAA4B,EAC1B,YAAY;gBACZ,IAAI;gBACJ,8HAA8H;SACjI;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,WAAW,EACT,wEAAwE;wBAC1E,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,KAAK;SACpB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC;QAChC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAC5D,OAAO,CAAC,WAAW,EAAE,CACtB,CAAC;QAEF,SAAS,oBAAoB,CAC3B,IAAmB,EACnB,MAA6B;YAE7B,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YAED,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC1E,IAAI,SAAS,EAAE;gBACb,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EACP,gBAAgB,KAAK,KAAK;wBACxB,CAAC,CAAC,8BAA8B;wBAChC,CAAC,CAAC,SAAS;oBACf,IAAI;iBACL,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAA+B;gBAC9C,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;oBACnB,OAAO;iBACR;gBAED,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAC9C,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CACtD,CAAC;gBAEF,IACE,YAAY;oBACZ,oBAAoB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACtD,aAAa,CAAC,YAAY,EAAE,iBAAiB,CAAC,EAC9C;oBACA,OAAO;iBACR;gBAED,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEpE,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;YACxE,CAAC;YACD,0CAA0C,CACxC,IAAiE;gBAEjE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GACtB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;oBACtB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE9B,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE;oBAC5D,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAClE,MAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;oBACxD,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBAEpD,MAAM,WAAW,GACf,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;oBAE/D,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBACnC,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;4BACzC,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC/C;4BACA,IACE,WAAW;gCACX,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;gCAC3B,oBAAoB,CAAC,QAAQ,CAC3B,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CACxC,EACD;gCACA,OAAO;6BACR;4BAED,oBAAoB,CAClB,QAAQ,CAAC,GAAG,EACZ,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CACzC,CAAC;yBACH;oBACH,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,WAAW,CAClB,MAAiB,EACjB,YAAqB;;IAErB,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE;QACrB,sEAAsE;QACtE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;KAC7B;IAED,QAAQ,gBAAgB,CAAC,IAAI,EAAE;QAC7B,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;YACpC,OAAO;gBACL,SAAS,EACP,CAAA,MAAC,gBAA2C,CAAC,WAAW,0CAAE,IAAI;oBAC9D,EAAE,CAAC,UAAU,CAAC,kBAAkB;aACnC,CAAC;QACJ,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,gBAES,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,gBAAgB,GACpB,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,CAAC,IAAI,MAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAClD,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,CAAC,WAAW,MAAK,MAAM,CAAC;YAC1C,MAAM,aAAa,GACjB,gBAAgB;gBAChB,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,0CAAE,IAAI,MAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAEvD,OAAO;gBACL,SAAS,EACP,CAAC,aAAa;oBACd,CAAC,CACC,YAAY;wBACZ,OAAO,CAAC,WAAW,CACjB,IAAA,mBAAY,EAAC,gBAAgB,CAAC,EAC9B,EAAE,CAAC,UAAU,CAAC,aAAa,CAC5B,CACF;gBACH,gBAAgB;aACjB,CAAC;SACH;KACF;IAED,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,SAAS,CAAC,IAAmB;IACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAQ,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAE;QACpB,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,IAAI,CAAC;QAEd,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;QAEhC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;QAE9B,KAAK,sBAAc,CAAC,wBAAwB;YAC1C,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;QAE7B,KAAK,sBAAc,CAAC,eAAe;YACjC,qCAAqC;YACrC,uCAAuC;YACvC,wCAAwC;YACxC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErE,KAAK,sBAAc,CAAC,gBAAgB;YAClC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE5E,KAAK,sBAAc,CAAC,oBAAoB;YACtC,OAAO,CACL,MAAM,CAAC,QAAQ,KAAK,GAAG;gBACvB,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;oBACnB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,KAAK;wBACzC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBACpD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC,CAAC,CAChE,CAAC;QAEJ,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;QAE3B,KAAK,sBAAc,CAAC,iBAAiB;YACnC,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;gBACpD,qEAAqE;gBACrE,OAAO,IAAI,CAAC;aACb;YAED,oFAAoF;YACpF,0CAA0C;YAC1C,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;KAC5B;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}