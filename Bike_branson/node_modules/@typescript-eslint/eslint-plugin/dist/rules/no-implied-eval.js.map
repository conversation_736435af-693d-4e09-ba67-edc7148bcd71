{"version": 3, "file": "no-implied-eval.js", "sourceRoot": "", "sources": ["../../src/rules/no-implied-eval.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,MAAM,oBAAoB,GAAG,UAAU,CAAC;AACxC,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;AACtE,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;IAChC,cAAc;IACd,aAAa;IACb,YAAY;IACZ,YAAY;CACb,CAAC,CAAC;AAEH,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,WAAW,EAAE,OAAO;YACpB,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,kBAAkB,EAAE,4CAA4C;YAChE,qBAAqB,EACnB,wEAAwE;SAC3E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACvC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,SAAS,aAAa,CACpB,IAAqC;YAErC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;gBAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;aAClB;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC9C,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EACvC;gBACA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;oBACpD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;iBAC3B;gBAED,IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EACvC;oBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;iBAC5B;aACF;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,cAAc,CAAC,IAAmB;;YACzC,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhC,IACE,MAAM;gBACN,OAAO,CAAC,eAAe,CACrB,MAAM,EACN,EAAE,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAChD,EACD;gBACA,OAAO,IAAI,CAAC;aACb;YAED,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,oBAAoB,EAAE;gBACzD,MAAM,YAAY,GAAG,MAAA,MAAM,CAAC,eAAe,EAAE,mCAAI,EAAE,CAAC;gBACpD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;oBACtC,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;oBAC/C,IAAI,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE;wBAClD,OAAO,IAAI,CAAC;qBACb;iBACF;aACF;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,mBAAmB,CAC5C,IAAI,EACJ,EAAE,CAAC,aAAa,CAAC,IAAI,CACtB,CAAC;YAEF,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,CAAC;QAED,SAAS,MAAM,CAAC,IAAmB;YACjC,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAClD,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvB,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;QACtE,CAAC;QAED,SAAS,UAAU,CAAC,IAAmB;YACrC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;gBAC5C,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,IAAI,CAAC;gBAEd,KAAK,sBAAc,CAAC,OAAO,CAAC;gBAC5B,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,KAAK,CAAC;gBAEf,KAAK,sBAAc,CAAC,cAAc;oBAChC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;gBAErD;oBACE,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;aAC/B;QACH,CAAC;QAED,SAAS,2BAA2B,CAAC,UAAkB;YACrD,MAAM,GAAG,GAAG,OAAO;iBAChB,QAAQ,EAAE;iBACV,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;YAE9D,mCAAmC;YACnC,OAAO,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,QAAQ,CAAA,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAsD;;YAEtD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,OAAO;aACR;YAED,IAAI,UAAU,KAAK,oBAAoB,EAAE;gBACvC,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrE,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChC,IAAI,MAAM,EAAE;oBACV,MAAM,YAAY,GAAG,MAAA,MAAM,CAAC,eAAe,EAAE,mCAAI,EAAE,CAAC;oBACpD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;wBACtC,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;wBAC/C,IAAI,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE;4BAClD,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC,CAAC;4BAC7D,OAAO;yBACR;qBACF;iBACF;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC,CAAC;oBAC7D,OAAO;iBACR;aACF;YAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/B,OAAO;aACR;YAED,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,IACE,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;gBACjC,CAAC,UAAU,CAAC,OAAO,CAAC;gBACpB,2BAA2B,CAAC,UAAU,CAAC,EACvC;gBACA,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAE,CAAC,CAAC;aACpE;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,gBAAgB;YAC/B,cAAc,EAAE,gBAAgB;SACjC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}