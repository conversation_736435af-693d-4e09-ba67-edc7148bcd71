"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CatchScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class CatchScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>ana<PERSON>, ScopeType_1.ScopeType.catch, upperScope, block, false);
    }
}
exports.CatchScope = CatchScope;
//# sourceMappingURL=CatchScope.js.map