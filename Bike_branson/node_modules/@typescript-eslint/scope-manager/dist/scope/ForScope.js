"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ForScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ForScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>anager, ScopeType_1.ScopeType.for, upperScope, block, false);
    }
}
exports.ForScope = ForScope;
//# sourceMappingURL=ForScope.js.map