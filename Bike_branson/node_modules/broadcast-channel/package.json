{"name": "broadcast-channel", "version": "3.7.0", "description": "A BroadcastChannel that works in New Browsers, Old Browsers, WebWorkers and NodeJs", "homepage": "https://github.com/pubkey/broadcast-channel#readme", "keywords": ["broadcast-channel", "broadcastchannel", "broadcast", "polyfill", "localstorage", "indexeddb", "postMessage", "crosstab", "ipc", "leader-election"], "repository": {"type": "git", "url": "git+https://github.com/pubkey/broadcast-channel.git"}, "author": "pubkey", "license": "MIT", "bugs": {"url": "https://github.com/pubkey/broadcast-channel/issues"}, "main": "./dist/lib/index.es5.js", "jsnext:main": "./dist/es/index.js", "module": "./dist/es/index.js", "sideEffects": false, "types": "./types/index.d.ts", "scripts": {"test": "echo \"RUN ALL:\" && npm run test:node && npm run test:browser && npm run test:e2e", "test:node": "npm run build && mocha ./test/index.test.js -b --timeout 6000 --exit", "test:node:loop": "npm run test:node && npm run test:node:loop", "test:browser": "npm run build && karma start ./config/karma.conf.js --single-run", "test:e2e": "concurrently \"npm run docs:serve\" \"sleep 20 && testcafe -b && testcafe chrome -e test/e2e.test.js --hostname localhost\" --kill-others --success first", "test:typings": "npm run build && mocha ./test/typings.test.js -b --timeout 12000 --exit", "test:performance": "npm run build && mocha ./test/performance.test.js -b --timeout 24000 --exit", "test:simple": "npm run build && node ./test_tmp/simple.test.js", "test:electron": "(cd ./test-electron && npm run test)", "size:prewebpack": "npm run build && cross-env NODE_ENV=build webpack --config ./config/webpack.config.js", "size:webpack": "npm run size:prewebpack && echo \"Build-Size Webpack (minified+gzip):\" && gzip-size --raw ./test_tmp/webpack.bundle.js", "size:browserify": "npm run build && rimraf test_tmp/browserify.js && browserify --no-builtins dist/lib/browserify.index.js > test_tmp/browserify.js && uglifyjs --compress --mangle --output test_tmp/browserify.min.js -- test_tmp/browserify.js && echo \"Build-Size browserify (minified+gzip):\" && gzip-size --raw test_tmp/browserify.min.js", "size:rollup": "npm run build && rollup --config ./config/rollup.config.js && echo \"Build-Size Rollup (minified+gzip):\" && gzip-size --raw ./test_tmp/rollup.bundle.js", "lint": "eslint src test config", "clear": "rimraf -rf ./dist && rimraf -rf ./gen", "build:es6": "rimraf -rf dist/es && cross-env NODE_ENV=es6 babel src --out-dir dist/es", "build:es5": "cross-env NODE_ENV=es5 babel src --out-dir dist/lib", "build:test": "cross-env NODE_ENV=es5 babel test --out-dir test_tmp", "build:index": "browserify test_tmp/scripts/index.js > docs/index.js", "build:browser": "browserify test_tmp/scripts/e2e.js > docs/e2e.js", "build:worker": "browserify test_tmp/scripts/worker.js > docs/worker.js", "build:iframe": "browserify test_tmp/scripts/iframe.js > docs/iframe.js", "build:leader-iframe": "browserify test_tmp/scripts/leader-iframe.js > docs/leader-iframe.js", "build:lib-browser": "browserify dist/lib/browserify.index.js > dist/lib/browser.js", "build:lib-browser:min": "uglifyjs --compress --mangle --output dist/lib/browser.min.js -- dist/lib/browser.js", "build": "npm run clear && concurrently \"npm run build:es6\" \"npm run build:es5\" \"npm run build:test\" && concurrently \"npm run build:index\" \"npm run build:browser\" \"npm run build:worker\" \"npm run build:iframe\" \"npm run build:leader-iframe\" && npm run build:lib-browser && npm run build:lib-browser:min", "build:min": "uglifyjs --compress --mangle --output dist/lib/browserify.min.js -- dist/lib/browserify.index.js", "docs:only": "http-server ./docs --silent", "docs:serve": "npm run build && echo \"Open http://localhost:8080/\" && npm run docs:only"}, "pre-commit": ["lint"], "dependencies": {"@babel/runtime": "^7.7.2", "detect-node": "^2.1.0", "js-sha3": "0.8.0", "microseconds": "0.2.0", "nano-time": "1.0.0", "oblivious-set": "1.0.0", "rimraf": "3.0.2", "unload": "2.2.0"}, "devDependencies": {"@babel/cli": "7.14.3", "@babel/core": "7.14.3", "@babel/plugin-proposal-object-rest-spread": "7.14.4", "@babel/plugin-transform-member-expression-literals": "7.12.13", "@babel/plugin-transform-property-literals": "7.12.13", "@babel/plugin-transform-runtime": "7.14.3", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.14.4", "@babel/types": "7.14.4", "@types/core-js": "2.5.4", "assert": "2.0.0", "async-test-util": "1.7.3", "browserify": "17.0.0", "child-process-promise": "2.2.1", "clone": "2.1.2", "concurrently": "6.2.0", "convert-hrtime": "5.0.0", "copyfiles": "2.4.1", "cross-env": "7.0.3", "eslint": "7.27.0", "gzip-size-cli": "5.0.0", "http-server": "0.12.3", "jest": "27.0.3", "karma": "6.3.3", "karma-babel-preprocessor": "8.0.1", "karma-browserify": "8.0.0", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.3", "karma-detect-browsers": "2.3.3", "karma-edge-launcher": "0.4.2", "karma-firefox-launcher": "2.1.1", "karma-ie-launcher": "1.0.0", "karma-mocha": "2.0.1", "karma-opera-launcher": "1.0.0", "karma-safari-launcher": "1.0.0", "mocha": "8.4.0", "pre-commit": "1.2.2", "random-int": "3.0.0", "random-token": "0.0.8", "rollup": "2.50.5", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-uglify": "6.0.4", "testcafe": "1.14.2", "ts-node": "10.0.0", "typescript": "4.3.2", "watchify": "4.0.0", "webpack": "5.38.1", "webpack-cli": "4.7.0"}, "browser": {"./src/methods/node.js": false, "./dist/es/methods/node.js": false, "./dist/lib/methods/node.js": false}}