# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  all:
    # The type of runner that the job will run on
    runs-on: ubuntu-18.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # https://docs.github.com/en/free-pro-team@latest/actions/guides/caching-dependencies-to-speed-up-workflows
      - name: Reuse npm cache folder
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          # reuse the npm-cache and some node_modules folders
          path: |
            ~/.npm
            ./node_modules
            ./test-electron/node_modules
          # invalidate cache when any package.json changes
          key: ${{ runner.os }}-npm-${{ env.cache-name }}-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-${{ env.cache-name }}-
            ${{ runner.os }}-npm-
            ${{ runner.os }}-

      # install
      - name: install node modules
        run: npm install

      - name: build
        run: npm run build

      - name: check build size webpack
        run: npm run size:webpack

      - name: check build size browserify
        run: npm run size:browserify

      - name: check build size rollup
        run: npm run size:rollup

      - name: code format
        run: npm run lint

      - name: test typings
        run: npm run test:typings

      - name: test node
        run: npm run test:node

      - name: test browser
        uses: GabrielBB/xvfb-action@v1
        with:
          working-directory: ./ #optional
          run: npm run test:browser

      - name: test performance
        run: npm run test:performance

      - name: test e2e
        uses: GabrielBB/xvfb-action@v1
        with:
          working-directory: ./ #optional
          run: npm run test:e2e

# TODO this does not work atm. fix this.
#      - name: test electron
#        uses: GabrielBB/xvfb-action@v1
#        with:
#          working-directory: ./test-electron
#          run: npm install --depth 0 --silent && npm run test
