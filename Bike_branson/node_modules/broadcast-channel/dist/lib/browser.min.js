!function r(o,i,s){function a(t,e){if(!i[t]){if(!o[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(u)return u(t,!0);throw(n=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",n}n=i[t]={exports:{}},o[t][0].call(n.exports,function(e){return a(o[t][1][e]||e)},n,n.exports,r,o,i,s)}return i[t].exports}for(var u="function"==typeof require&&require,e=0;e<s.length;e++)a(s[e]);return a}({1:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.clearNodeFolder=function(e){e=(0,s.fillOptionsWithDefaults)(e);e=(0,i.chooseMethod)(e);return"node"===e.type?e.clearNodeFolder().then(function(){return!0}):Promise.resolve(!1)},n.enforceOptions=function(e){r=e},n.BroadcastChannel=void 0;var r,o=e("./util.js"),i=e("./method-chooser.js"),s=e("./options.js"),e=function(e,t){var n;this.name=e,r&&(t=r),this.options=(0,s.fillOptionsWithDefaults)(t),this.method=(0,i.chooseMethod)(this.options),this._iL=!1,this._onML=null,this._addEL={message:[],internal:[]},this._uMP=new Set,this._befC=[],this._prepP=null,t=(n=this).method.create(n.name,n.options),(0,o.isPromise)(t)?(n._prepP=t).then(function(e){n._state=e}):n._state=t};function a(t,e,n){var r={time:t.method.microSeconds(),type:e,data:n};return(t._prepP||Promise.resolve()).then(function(){var e=t.method.postMessage(t._state,r);return t._uMP.add(e),e.catch().then(function(){return t._uMP.delete(e)}),e})}function u(e){return 0<e._addEL.message.length||0<e._addEL.internal.length}function c(e,t,n){e._addEL[t].push(n),function(e){{var t,n;!e._iL&&u(e)&&(t=function(t){e._addEL[t.type].forEach(function(e){t.time>=e.time&&e.fn(t.data)})},n=e.method.microSeconds(),e._prepP?e._prepP.then(function(){e._iL=!0,e.method.onMessage(e._state,t,n)}):(e._iL=!0,e.method.onMessage(e._state,t,n)))}}(e)}function l(e,t,n){e._addEL[t]=e._addEL[t].filter(function(e){return e!==n}),function(e){{var t;e._iL&&!u(e)&&(e._iL=!1,t=e.method.microSeconds(),e.method.onMessage(e._state,null,t))}}(e)}(n.BroadcastChannel=e)._pubkey=!0,e.prototype={postMessage:function(e){if(this.closed)throw new Error("BroadcastChannel.postMessage(): Cannot post message after channel has closed");return a(this,"message",e)},postInternal:function(e){return a(this,"internal",e)},set onmessage(e){var t={time:this.method.microSeconds(),fn:e};l(this,"message",this._onML),e&&"function"==typeof e?(this._onML=t,c(this,"message",t)):this._onML=null},addEventListener:function(e,t){var n=this.method.microSeconds();c(this,e,{time:n,fn:t})},removeEventListener:function(e,t){var n=this._addEL[e].find(function(e){return e.fn===t});l(this,e,n)},close:function(){var e=this;if(!this.closed){this.closed=!0;var t=this._prepP||Promise.resolve();return this._onML=null,this._addEL.message=[],t.then(function(){return Promise.all(Array.from(e._uMP))}).then(function(){return Promise.all(e._befC.map(function(e){return e()}))}).then(function(){return e.method.close(e._state)})}},get type(){return this.method.type},get isClosed(){return this.closed}}},{"./method-chooser.js":6,"./options.js":11,"./util.js":12}],2:[function(e,t,n){"use strict";var r=e("./index.es5.js"),e=r.BroadcastChannel,r=r.createLeaderElection;window.BroadcastChannel2=e,window.createLeaderElection=r},{"./index.es5.js":3}],3:[function(e,t,n){"use strict";e=e("./index.js");t.exports={BroadcastChannel:e.BroadcastChannel,createLeaderElection:e.createLeaderElection,clearNodeFolder:e.clearNodeFolder,enforceOptions:e.enforceOptions,beLeader:e.beLeader}},{"./index.js":4}],4:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"BroadcastChannel",{enumerable:!0,get:function(){return r.BroadcastChannel}}),Object.defineProperty(n,"clearNodeFolder",{enumerable:!0,get:function(){return r.clearNodeFolder}}),Object.defineProperty(n,"enforceOptions",{enumerable:!0,get:function(){return r.enforceOptions}}),Object.defineProperty(n,"createLeaderElection",{enumerable:!0,get:function(){return o.createLeaderElection}}),Object.defineProperty(n,"beLeader",{enumerable:!0,get:function(){return o.beLeader}});var r=e("./broadcast-channel"),o=e("./leader-election")},{"./broadcast-channel":1,"./leader-election":5}],5:[function(e,t,n){"use strict";var r=e("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(n,"__esModule",{value:!0}),n.beLeader=u,n.createLeaderElection=function(e,t){if(e._leaderElector)throw new Error("BroadcastChannel already has a leader-elector");t=function(e,t){e=e||{};(e=JSON.parse(JSON.stringify(e))).fallbackInterval||(e.fallbackInterval=3e3);e.responseTime||(e.responseTime=t.method.averageResponseTime(t.options));return e}(t,e);var n=new s(e,t);return e._befC.push(function(){return n.die()}),e._leaderElector=n};var i=e("./util.js"),o=r(e("unload")),s=function(e,t){this._channel=e,this._options=t,this.isLeader=!1,this.isDead=!1,this.token=(0,i.randomToken)(),this._isApl=!1,this._reApply=!1,this._unl=[],this._lstns=[],this._invs=[],this._dpL=function(){},this._dpLC=!1};function a(e,t){t={context:"leader",action:t,token:e.token};return e._channel.postInternal(t)}function u(t){t.isLeader=!0;var e=o.default.add(function(){return t.die()});t._unl.push(e);e=function(e){"leader"===e.context&&"apply"===e.action&&a(t,"tell"),"leader"!==e.context||"tell"!==e.action||t._dpLC||(t._dpLC=!0,t._dpL(),a(t,"tell"))};return t._channel.addEventListener("internal",e),t._lstns.push(e),a(t,"tell")}s.prototype={applyOnce:function(){var t=this;if(this.isLeader)return Promise.resolve(!1);if(this.isDead)return Promise.resolve(!1);if(this._isApl)return this._reApply=!0,Promise.resolve(!1);function n(e){"leader"===e.context&&e.token!=t.token&&(o.push(e),"apply"===e.action&&e.token>t.token&&(r=!0),"tell"===e.action&&(r=!0))}var r=!(this._isApl=!0),o=[];return this._channel.addEventListener("internal",n),a(this,"apply").then(function(){return(0,i.sleep)(t._options.responseTime)}).then(function(){return r?Promise.reject(new Error):a(t,"apply")}).then(function(){return(0,i.sleep)(t._options.responseTime)}).then(function(){return r?Promise.reject(new Error):a(t)}).then(function(){return u(t)}).then(function(){return!0}).catch(function(){return!1}).then(function(e){return t._channel.removeEventListener("internal",n),t._isApl=!1,!e&&t._reApply?(t._reApply=!1,t.applyOnce()):e})},awaitLeadership:function(){var i;return this._aLP||(this._aLP=(i=this).isLeader?Promise.resolve():new Promise(function(e){var t=!1;function n(){t||(t=!0,clearInterval(r),i._channel.removeEventListener("internal",o),e(!0))}i.applyOnce().then(function(){i.isLeader&&n()});var r=setInterval(function(){i.applyOnce().then(function(){i.isLeader&&n()})},i._options.fallbackInterval);i._invs.push(r);var o=function(e){"leader"===e.context&&"death"===e.action&&i.applyOnce().then(function(){i.isLeader&&n()})};i._channel.addEventListener("internal",o),i._lstns.push(o)})),this._aLP},set onduplicate(e){this._dpL=e},die:function(){var t=this;if(!this.isDead)return this.isDead=!0,this._lstns.forEach(function(e){return t._channel.removeEventListener("internal",e)}),this._invs.forEach(function(e){return clearInterval(e)}),this._unl.forEach(function(e){e.remove()}),a(this,"death")}}},{"./util.js":12,"@babel/runtime/helpers/interopRequireDefault":13,unload:19}],6:[function(e,t,n){"use strict";var r=e("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(n,"__esModule",{value:!0}),n.chooseMethod=function(t){var e=[].concat(t.methods,u).filter(Boolean);if(t.type){if("simulate"===t.type)return s.default;var n=e.find(function(e){return e.type===t.type});if(n)return n;throw new Error("method-type "+t.type+" not found")}t.webWorkerSupport||a.isNode||(e=e.filter(function(e){return"idb"!==e.type}));e=e.find(function(e){return e.canBeUsed()});{if(e)return e;throw new Error("No useable methode found:"+JSON.stringify(u.map(function(e){return e.type})))}};var o=r(e("./methods/native.js")),i=r(e("./methods/indexed-db.js")),n=r(e("./methods/localstorage.js")),s=r(e("./methods/simulate.js")),a=e("./util"),u=[o.default,i.default,n.default];!a.isNode||"function"==typeof(e=e("../../src/methods/node.js")).canBeUsed&&u.push(e)},{"./methods/indexed-db.js":7,"./methods/localstorage.js":8,"./methods/native.js":9,"./methods/simulate.js":10,"./util":12,"@babel/runtime/helpers/interopRequireDefault":13}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getIdb=u,n.createDatabase=c,n.writeMessage=l,n.getAllMessages=function(e){var n=e.transaction(a).objectStore(a),r=[];return new Promise(function(t){n.openCursor().onsuccess=function(e){e=e.target.result;e?(r.push(e.value),e.continue()):t(r)}})},n.getMessagesHigherThan=d,n.removeMessageById=f,n.getOldMessages=h,n.cleanOldMessages=p,n.create=m,n.close=b,n.postMessage=g,n.onMessage=w,n.canBeUsed=_,n.averageResponseTime=y,n.default=n.type=n.microSeconds=void 0;var o=e("../util.js"),i=e("oblivious-set"),s=e("../options"),e=o.microSeconds;n.microSeconds=e;var r="pubkey.broadcast-channel-0-",a="messages";function u(){if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof window){if(void 0!==window.mozIndexedDB)return window.mozIndexedDB;if(void 0!==window.webkitIndexedDB)return window.webkitIndexedDB;if(void 0!==window.msIndexedDB)return window.msIndexedDB}return!1}function c(e){var n=u().open(r+e,1);return n.onupgradeneeded=function(e){e.target.result.createObjectStore(a,{keyPath:"id",autoIncrement:!0})},new Promise(function(e,t){n.onerror=function(e){return t(e)},n.onsuccess=function(){e(n.result)}})}function l(e,t,n){var r={uuid:t,time:(new Date).getTime(),data:n},o=e.transaction([a],"readwrite");return new Promise(function(e,t){o.oncomplete=function(){return e()},o.onerror=function(e){return t(e)},o.objectStore(a).add(r)})}function d(e,n){var r=e.transaction(a).objectStore(a),o=[];return new Promise(function(t){(function(){try{var e=IDBKeyRange.bound(n+1,1/0);return r.openCursor(e)}catch(e){return r.openCursor()}})().onsuccess=function(e){e=e.target.result;e?e.value.id<n+1?e.continue(n+1):(o.push(e.value),e.continue()):t(o)}})}function f(e,t){var n=e.transaction([a],"readwrite").objectStore(a).delete(t);return new Promise(function(e){n.onsuccess=function(){return e()}})}function h(e,t){var r=(new Date).getTime()-t,o=e.transaction(a).objectStore(a),i=[];return new Promise(function(n){o.openCursor().onsuccess=function(e){var t,e=e.target.result;e&&(t=e.value).time<r?(i.push(t),e.continue()):n(i)}})}function p(t,e){return h(t,e).then(function(e){return Promise.all(e.map(function(e){return f(t,e.id)}))})}function m(n,r){return r=(0,s.fillOptionsWithDefaults)(r),c(n).then(function(e){var t={closed:!1,lastCursorId:0,channelName:n,options:r,uuid:(0,o.randomToken)(),eMIs:new i.ObliviousSet(2*r.idb.ttl),writeBlockPromise:Promise.resolve(),messagesCallback:null,readQueuePromises:[],db:e};return e.onclose=function(){t.closed=!0,r.idb.onclose&&r.idb.onclose()},function e(t){if(t.closed)return;v(t).then(function(){return(0,o.sleep)(t.options.idb.fallbackInterval)}).then(function(){return e(t)})}(t),t})}function v(n){return!n.closed&&n.messagesCallback?d(n.db,n.lastCursorId).then(function(e){return e.filter(function(e){return!!e}).map(function(e){return e.id>n.lastCursorId&&(n.lastCursorId=e.id),e}).filter(function(e){return t=n,(e=e).uuid!==t.uuid&&(!t.eMIs.has(e.id)&&!(e.data.time<t.messagesCallbackTime));var t}).sort(function(e,t){return e.time-t.time}).forEach(function(e){n.messagesCallback&&(n.eMIs.add(e.id),n.messagesCallback(e.data))}),Promise.resolve()}):Promise.resolve()}function b(e){e.closed=!0,e.db.close()}function g(e,t){return e.writeBlockPromise=e.writeBlockPromise.then(function(){return l(e.db,e.uuid,t)}).then(function(){0===(0,o.randomInt)(0,10)&&p(e.db,e.options.idb.ttl)}),e.writeBlockPromise}function w(e,t,n){e.messagesCallbackTime=n,e.messagesCallback=t,v(e)}function _(){return!o.isNode&&!!u()}function y(e){return 2*e.idb.fallbackInterval}n.type="idb",n.default={create:m,close:b,onMessage:w,postMessage:g,canBeUsed:_,type:"idb",averageResponseTime:y,microSeconds:e}},{"../options":11,"../util.js":12,"oblivious-set":16}],8:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getLocalStorage=u,n.storageKey=c,n.postMessage=l,n.addStorageEventListener=d,n.removeStorageEventListener=f,n.create=h,n.close=p,n.onMessage=m,n.canBeUsed=v,n.averageResponseTime=b,n.default=n.type=n.microSeconds=void 0;var i=e("oblivious-set"),s=e("../options"),a=e("../util"),r=a.microSeconds;n.microSeconds=r;var o="pubkey.broadcastChannel-",e="localstorage";function u(){var e;if("undefined"==typeof window)return null;try{e=window.localStorage,e=window["ie8-eventlistener/storage"]||window.localStorage}catch(e){}return e}function c(e){return o+e}function l(o,i){return new Promise(function(r){(0,a.sleep)().then(function(){var e=c(o.channelName),t={token:(0,a.randomToken)(),time:(new Date).getTime(),data:i,uuid:o.uuid},n=JSON.stringify(t);u().setItem(e,n);t=document.createEvent("Event");t.initEvent("storage",!0,!0),t.key=e,t.newValue=n,window.dispatchEvent(t),r()})})}function d(e,t){var n=o+e,e=function(e){e.key===n&&t(JSON.parse(e.newValue))};return window.addEventListener("storage",e),e}function f(e){window.removeEventListener("storage",e)}function h(e,t){if(t=(0,s.fillOptionsWithDefaults)(t),!v())throw new Error("BroadcastChannel: localstorage cannot be used");var n=(0,a.randomToken)(),r=new i.ObliviousSet(t.localstorage.removeTimeout),o={channelName:e,uuid:n,eMIs:r};return o.listener=d(e,function(e){o.messagesCallback&&e.uuid!==n&&e.token&&!r.has(e.token)&&(e.data.time&&e.data.time<o.messagesCallbackTime||(r.add(e.token),o.messagesCallback(e.data)))}),o}function p(e){f(e.listener)}function m(e,t,n){e.messagesCallbackTime=n,e.messagesCallback=t}function v(){if(a.isNode)return!1;var e=u();if(!e)return!1;try{var t="__broadcastchannel_check";e.setItem(t,"works"),e.removeItem(t)}catch(e){return!1}return!0}function b(){var e=navigator.userAgent.toLowerCase();return e.includes("safari")&&!e.includes("chrome")?240:120}n.type=e,n.default={create:h,close:p,onMessage:m,postMessage:l,canBeUsed:v,type:e,averageResponseTime:b,microSeconds:r}},{"../options":11,"../util":12,"oblivious-set":16}],9:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.create=o,n.close=i,n.postMessage=s,n.onMessage=a,n.canBeUsed=u,n.averageResponseTime=c,n.default=n.type=n.microSeconds=void 0;var r=e("../util"),e=r.microSeconds;n.microSeconds=e;function o(e){var t={messagesCallback:null,bc:new BroadcastChannel(e),subFns:[]};return t.bc.onmessage=function(e){t.messagesCallback&&t.messagesCallback(e.data)},t}function i(e){e.bc.close(),e.subFns=[]}function s(e,t){try{return e.bc.postMessage(t,!1),Promise.resolve()}catch(e){return Promise.reject(e)}}function a(e,t){e.messagesCallback=t}function u(){if(r.isNode&&"undefined"==typeof window)return!1;if("function"!=typeof BroadcastChannel)return!1;if(BroadcastChannel._pubkey)throw new Error("BroadcastChannel: Do not overwrite window.BroadcastChannel with this module, this is not a polyfill");return!0}function c(){return 150}n.type="native",n.default={create:o,close:i,onMessage:a,postMessage:s,canBeUsed:u,type:"native",averageResponseTime:c,microSeconds:e}},{"../util":12}],10:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.create=i,n.close=s,n.postMessage=a,n.onMessage=u,n.canBeUsed=c,n.averageResponseTime=l,n.default=n.type=n.microSeconds=void 0;var r=e("../util").microSeconds;n.microSeconds=r;e="simulate";n.type=e;var o=new Set;function i(e){e={name:e,messagesCallback:null};return o.add(e),e}function s(e){o.delete(e)}function a(t,n){return new Promise(function(e){return setTimeout(function(){Array.from(o).filter(function(e){return e.name===t.name}).filter(function(e){return e!==t}).filter(function(e){return!!e.messagesCallback}).forEach(function(e){return e.messagesCallback(n)}),e()},5)})}function u(e,t){e.messagesCallback=t}function c(){return!0}function l(){return 5}n.default={create:i,close:s,onMessage:u,postMessage:a,canBeUsed:c,type:e,averageResponseTime:l,microSeconds:r}},{"../util":12}],11:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.fillOptionsWithDefaults=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=JSON.parse(JSON.stringify(e));void 0===t.webWorkerSupport&&(t.webWorkerSupport=!0);t.idb||(t.idb={});t.idb.ttl||(t.idb.ttl=45e3);t.idb.fallbackInterval||(t.idb.fallbackInterval=150);e.idb&&"function"==typeof e.idb.onclose&&(t.idb.onclose=e.idb.onclose);t.localstorage||(t.localstorage={});t.localstorage.removeTimeout||(t.localstorage.removeTimeout=6e4);e.methods&&(t.methods=e.methods);t.node||(t.node={});t.node.ttl||(t.node.ttl=12e4);void 0===t.node.useFastPath&&(t.node.useFastPath=!0);return t}},{}],12:[function(e,t,o){!function(r){!function(){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.isPromise=function(e){return!(!e||"function"!=typeof e.then)},o.sleep=function(t){t=t||0;return new Promise(function(e){return setTimeout(e,t)})},o.randomInt=function(e,t){return Math.floor(Math.random()*(t-e+1)+e)},o.randomToken=function(){return Math.random().toString(36).substring(2)},o.microSeconds=function(){var e=(new Date).getTime();return e===t?1e3*e+ ++n:(n=0,1e3*(t=e))},o.isNode=void 0;var t=0,n=0;var e="[object process]"===Object.prototype.toString.call(void 0!==r?r:0);o.isNode=e}.call(this)}.call(this,e("_process"))},{_process:17}],13:[function(e,t,n){t.exports=function(e){return e&&e.__esModule?e:{default:e}},t.exports.default=t.exports,t.exports.__esModule=!0},{}],14:[function(e,t,n){},{}],15:[function(e,t,n){t.exports=!1},{}],16:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.now=n.removeTooOldValues=n.ObliviousSet=void 0;var r=(o.prototype.has=function(e){return this.set.has(e)},o.prototype.add=function(e){var t=this;this.timeMap.set(e,s()),this.set.add(e),setTimeout(function(){i(t)},0)},o.prototype.clear=function(){this.set.clear(),this.timeMap.clear()},o);function o(e){this.ttl=e,this.set=new Set,this.timeMap=new Map}function i(e){for(var t=s()-e.ttl,n=e.set[Symbol.iterator]();;){var r=n.next().value;if(!r)return;if(!(e.timeMap.get(r)<t))return;e.timeMap.delete(r),e.set.delete(r)}}function s(){return(new Date).getTime()}n.ObliviousSet=r,n.removeTooOldValues=i,n.now=s},{}],17:[function(e,t,n){var r,o,t=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(t){if(r===setTimeout)return setTimeout(t,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(e){r=i}try{o="function"==typeof clearTimeout?clearTimeout:s}catch(e){o=s}}();var u,c=[],l=!1,d=-1;function f(){l&&u&&(l=!1,u.length?c=u.concat(c):d=-1,c.length&&h())}function h(){if(!l){var e=a(f);l=!0;for(var t=c.length;t;){for(u=c,c=[];++d<t;)u&&u[d].run();d=-1,t=c.length}u=null,l=!1,function(t){if(o===clearTimeout)return clearTimeout(t);if((o===s||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function m(){}t.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new p(e,t)),1!==c.length||l||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=m,t.addListener=m,t.once=m,t.off=m,t.removeListener=m,t.removeAllListeners=m,t.emit=m,t.prependListener=m,t.prependOnceListener=m,t.listeners=function(e){return[]},t.binding=function(e){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(e){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},{}],18:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.default={add:function(e){"function"==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope||"function"==typeof window.addEventListener&&(window.addEventListener("beforeunload",function(){e()},!0),window.addEventListener("unload",function(){e()},!0))}}},{}],19:[function(e,t,n){"use strict";var r=e("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(n,"__esModule",{value:!0}),n.add=c,n.runAll=l,n.removeAll=d,n.getSize=f,n.default=void 0;var o=r(e("detect-node")),i=r(e("./browser.js")),e=r(e("./node.js")),s=(o.default?e:i).default,a=new Set,u=!1;function c(e){if(u||(u=!0,s.add(l)),"function"!=typeof e)throw new Error("Listener is no function");return a.add(e),{remove:function(){return a.delete(e)},run:function(){return a.delete(e),e()}}}function l(){var t=[];return a.forEach(function(e){t.push(e()),a.delete(e)}),Promise.all(t)}function d(){a.clear()}function f(){return a.size}n.default={add:c,runAll:l,removeAll:d,getSize:f}},{"./browser.js":18,"./node.js":14,"@babel/runtime/helpers/interopRequireDefault":13,"detect-node":15}]},{},[2]);