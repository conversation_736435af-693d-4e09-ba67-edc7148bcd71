{"name": "@svgr/plugin-jsx", "description": "Transform SVG into JSX", "version": "5.5.0", "main": "lib/index.js", "repository": "https://github.com/gregberge/svgr/tree/master/packages/plugin-jsx", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["svgr-plugin"], "engines": {"node": ">=10"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"prebuild": "rm -rf lib/", "build": "babel --config-file ../../babel.config.js -d lib --ignore \"**/*.test.js\" src", "prepublishOnly": "yarn run build"}, "dependencies": {"@babel/core": "^7.12.3", "@svgr/babel-preset": "^5.5.0", "@svgr/hast-util-to-babel-ast": "^5.5.0", "svg-parser": "^2.0.2"}, "gitHead": "b5920550bd966f876cb65c5e23af180461e5aa23"}