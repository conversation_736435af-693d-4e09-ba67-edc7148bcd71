-- Bike Branson Rental System Database Schema
-- MySQL Database for comprehensive bike rental management

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Create database (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS bike_branson_rental CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE bike_branson_rental;

-- Users table for customers and admin authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20) NULL,
    user_type ENUM('customer', 'admin', 'staff') NOT NULL DEFAULT 'customer',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255) NULL,
    reset_token VARCHAR(255) NULL,
    reset_token_expires DATETIME NULL,
    emergency_contact_name <PERSON><PERSON><PERSON><PERSON>(200) NULL,
    emergency_contact_phone VARCHAR(20) NULL,
    waiver_signed BOOLEAN DEFAULT FALSE,
    waiver_signed_date DATETIME NULL,
    waiver_ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bike inventory table
CREATE TABLE bikes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bike_number VARCHAR(50) NOT NULL UNIQUE,
    model VARCHAR(100) NOT NULL,
    brand VARCHAR(100) NOT NULL,
    bike_type ENUM('standard', 'mountain', 'comfort', 'tandem') NOT NULL DEFAULT 'standard',
    battery_capacity VARCHAR(50) NULL,
    max_range_miles INT NULL,
    weight_limit_lbs INT NULL,
    color VARCHAR(50) NULL,
    size ENUM('small', 'medium', 'large', 'extra_large') NOT NULL,
    purchase_date DATE NULL,
    purchase_price DECIMAL(10,2) NULL,
    current_condition ENUM('excellent', 'good', 'fair', 'needs_repair', 'out_of_service') DEFAULT 'excellent',
    maintenance_notes TEXT NULL,
    last_maintenance_date DATE NULL,
    next_maintenance_due DATE NULL,
    current_location ENUM('shop', 'dogwood_canyon', 'table_rock', 'other') DEFAULT 'shop',
    hourly_rate DECIMAL(8,2) NOT NULL,
    daily_rate DECIMAL(8,2) NOT NULL,
    image_url VARCHAR(500) NULL,
    specifications JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_bike_number (bike_number),
    INDEX idx_bike_type (bike_type),
    INDEX idx_current_condition (current_condition),
    INDEX idx_current_location (current_location),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Trail information table
CREATE TABLE trails (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    difficulty_level ENUM('easy', 'moderate', 'difficult') NOT NULL,
    distance_miles DECIMAL(5,2) NOT NULL,
    estimated_duration_hours DECIMAL(3,1) NOT NULL,
    elevation_gain_feet INT NULL,
    trail_type ENUM('paved', 'gravel', 'dirt', 'mixed') NOT NULL,
    location_address TEXT NULL,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    features JSON NULL, -- scenic_views, wildlife, historical_sites, etc.
    best_seasons JSON NULL,
    difficulty_description TEXT NULL,
    safety_notes TEXT NULL,
    image_gallery JSON NULL,
    is_delivery_available BOOLEAN DEFAULT FALSE,
    delivery_fee DECIMAL(8,2) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bookings table - core reservation system
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_number VARCHAR(50) NOT NULL UNIQUE,
    user_id INT NULL,
    customer_first_name VARCHAR(100) NOT NULL,
    customer_last_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    emergency_contact_name VARCHAR(200) NULL,
    emergency_contact_phone VARCHAR(20) NULL,
    rental_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    rental_duration_hours DECIMAL(3,1) NOT NULL,
    rental_type ENUM('onsite_pickup', 'delivery', 'guided_tour') NOT NULL,
    trail_id INT NULL,
    pickup_location VARCHAR(500) NULL,
    delivery_address TEXT NULL,
    number_of_bikes INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    deposit_amount DECIMAL(10,2) NULL,
    delivery_fee DECIMAL(8,2) NULL,
    discount_amount DECIMAL(8,2) NULL DEFAULT 0,
    discount_code VARCHAR(50) NULL,
    booking_status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show') DEFAULT 'pending',
    payment_status ENUM('pending', 'deposit_paid', 'paid_full', 'refunded', 'partial_refund') DEFAULT 'pending',
    special_requests TEXT NULL,
    waiver_signed BOOLEAN DEFAULT FALSE,
    waiver_signed_date DATETIME NULL,
    google_calendar_event_id VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (trail_id) REFERENCES trails(id) ON DELETE SET NULL,
    INDEX idx_booking_number (booking_number),
    INDEX idx_customer_email (customer_email),
    INDEX idx_rental_date (rental_date),
    INDEX idx_booking_status (booking_status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Booking bikes junction table (many-to-many relationship)
CREATE TABLE booking_bikes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    bike_id INT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    returned_at TIMESTAMP NULL,
    condition_at_pickup ENUM('excellent', 'good', 'fair', 'damaged') DEFAULT 'excellent',
    condition_at_return ENUM('excellent', 'good', 'fair', 'damaged') NULL,
    damage_notes TEXT NULL,
    damage_photos JSON NULL,

    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (bike_id) REFERENCES bikes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_bike (booking_id, bike_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_bike_id (bike_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payments table for Stripe integration
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    stripe_payment_intent_id VARCHAR(255) NULL,
    stripe_charge_id VARCHAR(255) NULL,
    payment_type ENUM('deposit', 'full_payment', 'balance', 'refund') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_status ENUM('pending', 'succeeded', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(100) NULL,
    stripe_fee DECIMAL(8,2) NULL,
    net_amount DECIMAL(10,2) NULL,
    failure_reason TEXT NULL,
    refund_amount DECIMAL(10,2) NULL,
    refund_reason TEXT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_stripe_payment_intent_id (stripe_payment_intent_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_processed_at (processed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Maintenance records for bikes
CREATE TABLE maintenance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bike_id INT NOT NULL,
    maintenance_type ENUM('routine', 'repair', 'inspection', 'cleaning', 'battery_service') NOT NULL,
    description TEXT NOT NULL,
    cost DECIMAL(8,2) NULL,
    performed_by VARCHAR(100) NOT NULL,
    maintenance_date DATE NOT NULL,
    next_maintenance_due DATE NULL,
    parts_replaced JSON NULL,
    before_condition ENUM('excellent', 'good', 'fair', 'poor') NULL,
    after_condition ENUM('excellent', 'good', 'fair', 'poor') NULL,
    photos JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (bike_id) REFERENCES bikes(id) ON DELETE CASCADE,
    INDEX idx_bike_id (bike_id),
    INDEX idx_maintenance_date (maintenance_date),
    INDEX idx_maintenance_type (maintenance_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Availability calendar for blocking dates/times
CREATE TABLE availability_calendar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    time_slot TIME NOT NULL,
    available_bikes INT NOT NULL DEFAULT 0,
    max_capacity INT NOT NULL DEFAULT 20,
    is_blocked BOOLEAN DEFAULT FALSE,
    block_reason VARCHAR(255) NULL,
    weather_status ENUM('good', 'caution', 'closed') DEFAULT 'good',
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_date_time (date, time_slot),
    INDEX idx_date (date),
    INDEX idx_is_blocked (is_blocked)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Discount codes and promotions
CREATE TABLE discount_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255) NOT NULL,
    discount_type ENUM('percentage', 'fixed_amount') NOT NULL,
    discount_value DECIMAL(8,2) NOT NULL,
    minimum_order_amount DECIMAL(8,2) NULL,
    max_uses INT NULL,
    current_uses INT DEFAULT 0,
    valid_from DATE NOT NULL,
    valid_until DATE NOT NULL,
    applicable_to ENUM('all', 'first_time', 'returning', 'specific_trails') DEFAULT 'all',
    trail_restrictions JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_valid_dates (valid_from, valid_until),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contact form submissions
CREATE TABLE contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    subject VARCHAR(300) NOT NULL,
    message TEXT NOT NULL,
    submission_type ENUM('general', 'booking_inquiry', 'support', 'partnership') DEFAULT 'general',
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    assigned_to INT NULL,
    response_notes TEXT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings for configuration
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'Bike Branson', 'string', 'Website name', TRUE),
('tagline', 'The Outdoors, Effortlessly!', 'string', 'Site tagline', TRUE),
('contact_email', '<EMAIL>', 'string', 'Main contact email', TRUE),
('contact_phone', '+1 (417) 555-BIKE', 'string', 'Main contact phone', TRUE),
('business_address', '123 Main St, Branson, MO 65616', 'string', 'Business address', TRUE),
('business_hours', '{"monday": "9:00-18:00", "tuesday": "9:00-18:00", "wednesday": "9:00-18:00", "thursday": "9:00-18:00", "friday": "9:00-19:00", "saturday": "8:00-19:00", "sunday": "8:00-18:00"}', 'json', 'Business operating hours', TRUE),
('seasonal_closure_dates', '[]', 'json', 'Dates when business is closed', FALSE),
('stripe_publishable_key', '', 'string', 'Stripe publishable key', FALSE),
('stripe_secret_key', '', 'string', 'Stripe secret key', FALSE),
('google_calendar_id', '', 'string', 'Google Calendar ID for bookings', FALSE),
('google_api_key', '', 'string', 'Google API key', FALSE),
('max_bikes_per_booking', '10', 'number', 'Maximum bikes per single booking', FALSE),
('booking_advance_days', '30', 'number', 'How many days in advance bookings can be made', FALSE),
('cancellation_hours', '24', 'number', 'Hours before rental for free cancellation', FALSE),
('deposit_percentage', '25', 'number', 'Deposit percentage required', FALSE),
('delivery_radius_miles', '15', 'number', 'Maximum delivery radius in miles', FALSE),
('weather_api_key', '', 'string', 'Weather API key', FALSE);

-- Insert default trails
INSERT INTO trails (name, slug, description, difficulty_level, distance_miles, estimated_duration_hours, trail_type, location_address, features, is_delivery_available, delivery_fee) VALUES
('Table Rock Lake Shoreline Trail', 'table-rock-lake-shoreline', 'A scenic paved trail that follows the beautiful shoreline of Table Rock Lake. Perfect for families and beginners, offering stunning lake views and wildlife spotting opportunities.', 'easy', 10.5, 2.5, 'paved', 'Table Rock State Park, Branson, MO', '["scenic_views", "wildlife", "family_friendly", "lake_access"]', TRUE, 15.00),
('Dogwood Canyon Nature Park', 'dogwood-canyon', 'Experience the natural beauty of Dogwood Canyon with its pristine streams, waterfalls, and diverse wildlife. This trail offers both paved and natural surface options.', 'moderate', 6.2, 2.0, 'mixed', 'Dogwood Canyon Nature Park, Lampe, MO', '["waterfalls", "wildlife", "streams", "photography"]', TRUE, 20.00),
('Branson Creek Trail', 'branson-creek', 'A peaceful trail winding through Branson Creek area with gentle hills and beautiful forest scenery. Great for intermediate riders looking for a moderate challenge.', 'moderate', 8.3, 2.5, 'gravel', 'Branson Creek, Branson, MO', '["forest", "creek_views", "moderate_hills"]', FALSE, 0.00),
('White River Trail', 'white-river', 'Follow the historic White River on this easy, mostly flat trail. Rich in history and perfect for leisurely rides with opportunities to see local wildlife.', 'easy', 12.0, 3.0, 'paved', 'White River, Branson, MO', '["historic", "river_views", "wildlife", "flat_terrain"]', TRUE, 18.00);

-- Insert sample bike inventory
INSERT INTO bikes (bike_number, model, brand, bike_type, battery_capacity, max_range_miles, weight_limit_lbs, color, size, current_condition, hourly_rate, daily_rate, specifications) VALUES
('BB001', 'RadRover 6 Plus', 'Rad Power Bikes', 'mountain', '48V 14Ah', 45, 275, 'Black', 'large', 'excellent', 25.00, 120.00, '{"motor": "750W", "top_speed": "20mph", "weight": "73lbs"}'),
('BB002', 'RadRover 6 Plus', 'Rad Power Bikes', 'mountain', '48V 14Ah', 45, 275, 'White', 'medium', 'excellent', 25.00, 120.00, '{"motor": "750W", "top_speed": "20mph", "weight": "73lbs"}'),
('BB003', 'RadCity 5 Plus', 'Rad Power Bikes', 'comfort', '48V 14Ah', 50, 275, 'Blue', 'medium', 'excellent', 22.00, 110.00, '{"motor": "750W", "top_speed": "20mph", "weight": "65lbs"}'),
('BB004', 'RadCity 5 Plus', 'Rad Power Bikes', 'comfort', '48V 14Ah', 50, 275, 'Red', 'small', 'excellent', 22.00, 110.00, '{"motor": "750W", "top_speed": "20mph", "weight": "65lbs"}'),
('BB005', 'RadWagon 4', 'Rad Power Bikes', 'standard', '48V 14Ah', 45, 350, 'Green', 'large', 'excellent', 30.00, 150.00, '{"motor": "750W", "top_speed": "20mph", "weight": "76lbs", "cargo_capacity": "350lbs"}');

-- Create admin user (password: admin123 - CHANGE THIS!)
INSERT INTO users (email, password_hash, first_name, last_name, user_type, email_verified, is_active) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', TRUE, TRUE);

-- Insert sample discount codes
INSERT INTO discount_codes (code, description, discount_type, discount_value, valid_from, valid_until, applicable_to) VALUES
('WELCOME10', 'Welcome discount for new customers', 'percentage', 10.00, '2024-01-01', '2024-12-31', 'first_time'),
('SUMMER2024', 'Summer season discount', 'percentage', 15.00, '2024-06-01', '2024-08-31', 'all'),
('FAMILY20', 'Family discount for 4+ bikes', 'percentage', 20.00, '2024-01-01', '2024-12-31', 'all');

COMMIT;
