# 📸 Bike Branson Image Upload Guide

## 🎯 **Quick Start - Priority Images**

To get your website looking professional immediately, add these images first:

### **1. Logo Files** (Place in `/public/images/`)
```
logo.png              - Main logo (200x60px, transparent PNG)
logo-white.png        - White logo for dark backgrounds  
favicon.ico           - Browser tab icon (32x32px)
```

### **2. Hero Background** (Place in `/public/images/`)
```
hero-trail-background.jpg  - Mobile fallback image (1920x1080px)
```

### **3. Bike Photos** (Place in `/public/images/bikes/`)
```
rad-rover-6-plus-black.jpg    - Black Rad Rover bike
rad-rover-6-plus-white.jpg    - White Rad Rover bike
rad-city-5-plus-blue.jpg      - Blue Rad City bike
rad-city-5-plus-red.jpg       - Red Rad City bike
rad-wagon-4-green.jpg         - Green Rad Wagon bike
bike-placeholder.jpg          - Default bike image
```

### **4. Trail Photos** (Place in `/public/images/trails/`)
```
table-rock-lake-1.jpg         - Table Rock Lake trail
dogwood-canyon-1.jpg          - Dogwood Canyon trail
branson-creek-1.jpg           - Branson Creek trail
white-river-1.jpg             - White River trail
trail-placeholder.jpg         - Default trail image
```

## 📁 **Folder Structure**

```
Bike_branson/
└── public/
    └── images/
        ├── logo.png                    ← Main logo
        ├── logo-white.png              ← White logo
        ├── favicon.ico                 ← Browser icon
        ├── hero-trail-background.jpg   ← Mobile hero image
        ├── bikes/
        │   ├── rad-rover-6-plus-black.jpg
        │   ├── rad-rover-6-plus-white.jpg
        │   ├── rad-city-5-plus-blue.jpg
        │   ├── rad-city-5-plus-red.jpg
        │   ├── rad-wagon-4-green.jpg
        │   └── bike-placeholder.jpg
        ├── trails/
        │   ├── table-rock-lake-1.jpg
        │   ├── dogwood-canyon-1.jpg
        │   ├── branson-creek-1.jpg
        │   ├── white-river-1.jpg
        │   └── trail-placeholder.jpg
        └── logos/
            ├── logo192.png             ← Mobile app icon
            └── logo512.png             ← Mobile app icon
```

## 🖼️ **Image Specifications**

### **Logo Requirements**
- **Format**: PNG with transparent background
- **Size**: 200x60px (main logo)
- **Colors**: Should work on white and orange backgrounds
- **Style**: Clean, professional, readable at small sizes

### **Bike Photos**
- **Format**: JPG (high quality)
- **Size**: 800x600px minimum
- **Style**: Clean white/neutral background preferred
- **Angle**: 3/4 view showing bike clearly
- **Lighting**: Bright, even lighting

### **Trail Photos**
- **Format**: JPG (high quality)  
- **Size**: 1200x800px minimum
- **Style**: Scenic, inviting, shows trail clearly
- **Time**: Golden hour or bright daylight preferred
- **Composition**: Wide shots showing trail and scenery

## 🚀 **How to Add Images**

### **Method 1: File Manager (Easiest)**
1. Open your file manager
2. Navigate to: `Bike_branson/public/images/`
3. Drag and drop your images into the correct folders
4. Rename files to match the exact names above

### **Method 2: Command Line**
```bash
# Navigate to images folder
cd Bike_branson/public/images/

# Copy your logo
cp /path/to/your/logo.png ./logo.png

# Copy bike photos
cp /path/to/bike-photos/* ./bikes/

# Copy trail photos  
cp /path/to/trail-photos/* ./trails/
```

### **Method 3: VS Code**
1. Open VS Code in your project folder
2. Navigate to `public/images/` in the file explorer
3. Right-click and "Reveal in File Explorer"
4. Drag and drop your images

## ✅ **Testing Your Images**

After adding images:

1. **Restart your development server**:
   ```bash
   # Stop server (Ctrl+C)
   # Restart
   npx react-scripts start
   ```

2. **Check the homepage**:
   - Logo should appear in navigation
   - Bike photos should show in "Our Fleet" section
   - Trail photos should show in "Featured Trails"

3. **Test responsive design**:
   - Open browser dev tools (F12)
   - Toggle mobile view
   - Check that images load and scale properly

## 🎨 **Image Optimization Tips**

### **Before Uploading**
- **Resize** images to recommended dimensions
- **Compress** to reduce file size (aim for <500KB each)
- **Rename** to match exact filenames needed
- **Check quality** on different devices

### **Tools for Optimization**
- **Online**: TinyPNG, Squoosh.app
- **Desktop**: Photoshop, GIMP, Canva
- **Batch**: ImageOptim (Mac), FileOptimizer (Windows)

## 🔧 **Troubleshooting**

### **Image Not Showing**
- Check filename matches exactly (case-sensitive)
- Ensure image is in correct folder
- Restart development server
- Check browser console for errors

### **Image Too Large/Small**
- Resize to recommended dimensions
- Use CSS to adjust display size if needed
- Provide multiple sizes for responsive design

### **Poor Quality**
- Use higher resolution source images
- Avoid over-compression
- Ensure proper lighting in original photos

## 📱 **Mobile Considerations**

- Images should look good on small screens
- File sizes should be optimized for mobile data
- Consider providing different images for mobile vs desktop
- Test on actual mobile devices

## 🎯 **Pro Tips**

1. **Consistent Style**: Use similar lighting and angles for all bike photos
2. **Brand Colors**: Include your orange brand color (#f15808) in images when possible
3. **Action Shots**: Trail photos should show people enjoying the trails
4. **Seasonal Updates**: Update trail photos seasonally to stay current
5. **Backup**: Keep high-resolution originals for future use

Once you add these images, your Bike Branson website will look completely professional and branded! 🚴‍♂️✨
