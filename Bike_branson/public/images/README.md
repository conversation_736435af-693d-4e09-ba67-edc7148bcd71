# Bike Branson Media Assets

This folder contains all images and media assets for the Bike Branson website.

## 📁 Folder Structure

### **Logos & Branding**
- `logo.png` - Main logo (transparent background, ~200x60px)
- `logo-white.png` - White version for dark backgrounds
- `favicon.ico` - Website favicon (32x32px)
- `logo192.png` - App icon for mobile (192x192px)
- `logo512.png` - App icon for mobile (512x512px)

### **Hero & Background Images**
- `hero-ebike.jpg` - Main hero image (1920x1080px)
- `hero-trail-background.jpg` - Mobile fallback background (1920x1080px)
- `og-image.jpg` - Open Graph image for social sharing (1200x630px)
- `twitter-image.jpg` - Twitter card image (1200x600px)

### **Bike Fleet Images**
- `bikes/` folder containing:
  - `rad-rover-6-plus-black.jpg`
  - `rad-rover-6-plus-white.jpg`
  - `rad-city-5-plus-blue.jpg`
  - `rad-city-5-plus-red.jpg`
  - `rad-wagon-4-green.jpg`
  - `bike-placeholder.jpg` - Default placeholder

### **Trail Images**
- `trails/` folder containing:
  - `table-rock-lake-1.jpg`
  - `table-rock-lake-2.jpg`
  - `dogwood-canyon-1.jpg`
  - `dogwood-canyon-2.jpg`
  - `branson-creek-1.jpg`
  - `white-river-1.jpg`
  - `trail-placeholder.jpg` - Default placeholder

### **General Images**
- `business-image.jpg` - Business photo for structured data
- `team-photo.jpg` - About page team photo
- `safety-equipment.jpg` - Safety gear images
- `placeholder.jpg` - General placeholder image

## 📐 Recommended Image Sizes

### **Logos**
- Main logo: 200x60px (PNG with transparency)
- Favicon: 32x32px (ICO format)
- App icons: 192x192px and 512x512px (PNG)

### **Hero Images**
- Desktop hero: 1920x1080px (JPG, high quality)
- Mobile hero: 1200x800px (JPG, optimized)

### **Product Images (Bikes)**
- Primary: 800x600px (JPG, high quality)
- Thumbnails: 400x300px (JPG, optimized)
- Gallery: 1200x900px (JPG, high quality)

### **Trail Images**
- Hero shots: 1200x800px (JPG, high quality)
- Gallery: 800x600px (JPG, optimized)
- Thumbnails: 400x300px (JPG, optimized)

### **Social Media**
- Open Graph: 1200x630px (JPG)
- Twitter Card: 1200x600px (JPG)

## 🎨 Image Guidelines

### **Style Consistency**
- Use consistent lighting and color grading
- Maintain the orange brand color scheme (#f15808)
- High contrast for web readability
- Professional, outdoor adventure aesthetic

### **File Naming Convention**
- Use lowercase with hyphens: `bike-model-color.jpg`
- Include descriptive keywords: `table-rock-lake-sunset.jpg`
- Version numbers if needed: `logo-v2.png`

### **Optimization**
- Compress images for web (aim for <500KB for large images)
- Use WebP format when possible for better compression
- Provide multiple sizes for responsive images
- Include alt text descriptions for accessibility

## 📱 Responsive Images

The website uses responsive images, so provide multiple sizes:
- Small: 400px width
- Medium: 800px width  
- Large: 1200px width
- Extra Large: 1920px width

## 🔄 Image Replacement

To replace placeholder images:
1. Add your image to the appropriate folder
2. Use the exact filename shown in the code
3. Ensure proper dimensions and optimization
4. Test on different devices and screen sizes

## 📋 Current Image References in Code

### **Homepage**
- Hero background video (YouTube embed)
- Mobile fallback: `/images/hero-trail-background.jpg`
- Bike images: `/images/bikes/[bike-model].jpg`
- Trail images: `/images/trails/[trail-name].jpg`

### **Navigation**
- Logo: `/images/logo.png`
- White logo: `/images/logo-white.png`

### **Placeholders Used**
- Bike placeholder: `/images/bike-placeholder.jpg`
- Trail placeholder: `/images/trail-placeholder.jpg`
- General placeholder: `/images/placeholder.jpg`

## 🎯 Priority Images to Add

1. **Logo files** (logo.png, logo-white.png)
2. **Hero trail background** for mobile
3. **Bike fleet photos** for inventory display
4. **Trail photos** for featured trails section
5. **Favicon** for browser tab

Once you add these images, the website will look completely professional and branded!
