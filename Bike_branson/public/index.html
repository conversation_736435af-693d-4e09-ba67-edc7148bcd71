<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#f15808" />
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Bike Branson - E-bike rentals in Branson, Missouri. The Outdoors, Effortlessly! Explore Table Rock Lake, Dogwood Canyon, and more scenic trails with our premium e-bike fleet." />
    <meta name="keywords" content="bike rental, e-bike, Branson Missouri, Table Rock Lake, Dogwood Canyon, outdoor recreation, electric bike, trail riding" />
    <meta name="author" content="Bike Branson" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Bike Branson - E-bike Rentals in Branson, Missouri" />
    <meta property="og:description" content="The Outdoors, Effortlessly! Premium e-bike rentals for exploring Branson's beautiful trails and scenic areas." />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.jpg" />
    <meta property="og:url" content="https://bikebranson.com" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="Bike Branson" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Bike Branson - E-bike Rentals in Branson, Missouri" />
    <meta name="twitter:description" content="The Outdoors, Effortlessly! Premium e-bike rentals for exploring Branson's beautiful trails and scenic areas." />
    <meta name="twitter:image" content="%PUBLIC_URL%/twitter-image.jpg" />
    
    <!-- Structured Data for Local Business -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Bike Branson",
        "description": "E-bike rental company in Branson, Missouri offering premium electric bike rentals for trail exploration",
        "url": "https://bikebranson.com",
        "telephone": "******-555-BIKE",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "123 Main St",
            "addressLocality": "Branson",
            "addressRegion": "MO",
            "postalCode": "65616",
            "addressCountry": "US"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "36.6436",
            "longitude": "-93.2185"
        },
        "openingHours": [
            "Mo-Th 09:00-18:00",
            "Fr 09:00-19:00",
            "Sa 08:00-19:00",
            "Su 08:00-18:00"
        ],
        "priceRange": "$22-$30 per hour",
        "image": "%PUBLIC_URL%/business-image.jpg",
        "sameAs": [
            "https://www.facebook.com/bikebranson",
            "https://www.instagram.com/bikebranson"
        ]
    }
    </script>
    
    <!-- Favicon and App Icons -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://js.stripe.com">
    <link rel="preconnect" href="https://api.stripe.com">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    
    <!-- Custom CSS Variables -->
    <style>
        :root {
            --primary-color: #f15808;
            --primary-light: #ff7043;
            --primary-dark: #d84315;
            --secondary-color: #ff9800;
            --secondary-light: #ffb74d;
            --secondary-dark: #f57c00;
            --accent-color: #ff5722;
            --text-dark: #1c0d0a;
            --text-light: #6C757D;
            --link-color: #1c0d0a;
            --link-hover-color: #f15808;
            --background-light: #F8F9FA;
            --white: #FFFFFF;
            --border-color: #e2e2e2;
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --font-family-primary: 'Inter', sans-serif;
            --font-family-heading: 'Poppins', sans-serif;
        }
        
        body {
            font-family: var(--font-family-primary);
            color: var(--text-dark);
            line-height: 1.6;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-family-heading);
            font-weight: 600;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: var(--secondary-dark);
            border-color: var(--secondary-dark);
            color: white;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        a {
            color: var(--link-color);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover-color);
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    
    <title>Bike Branson - E-bike Rentals in Branson, Missouri | The Outdoors, Effortlessly!</title>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Stripe.js -->
    <script src="https://js.stripe.com/v3/"></script>
</body>
</html>
