# Bike Branson Rental System - Environment Variables
# Copy this file to .env and update with your actual values

# Application Configuration
NODE_ENV=development
PORT=3001
REACT_APP_API_URL=http://localhost:3001/api
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=bike_branson_rental
DB_USER=your_db_username
DB_PASSWORD=your_db_password

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key-here

# Stripe Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Google Calendar Integration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3001/auth/google/callback
GOOGLE_CALENDAR_ID=<EMAIL>

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Bike Branson

# Weather API (OpenWeatherMap)
WEATHER_API_KEY=your-openweather-api-key
WEATHER_LOCATION=Branson,MO,US

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Business Configuration
BUSINESS_NAME=Bike Branson
BUSINESS_PHONE=+1 (417) 555-BIKE
BUSINESS_EMAIL=<EMAIL>
BUSINESS_ADDRESS=123 Main St, Branson, MO 65616
BUSINESS_TIMEZONE=America/Chicago

# Booking Configuration
MAX_ADVANCE_BOOKING_DAYS=30
MIN_BOOKING_HOURS=2
MAX_BOOKING_HOURS=8
CANCELLATION_HOURS=24
DEPOSIT_PERCENTAGE=25

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Development/Debug
DEBUG=bike-branson:*
LOG_LEVEL=info

# Production Settings (uncomment for production)
# NODE_ENV=production
# FRONTEND_URL=https://bikebranson.com
# BACKEND_URL=https://api.bikebranson.com
# DB_HOST=your-production-db-host
# SMTP_HOST=smtp.hostinger.com
# SMTP_USER=<EMAIL>
