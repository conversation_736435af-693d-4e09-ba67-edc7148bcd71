{"name": "patent-nft-marketplace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "compile": "hardhat compile", "test": "hardhat test", "deploy": "hardhat run scripts/deploy.js --network localhost", "node": "hardhat node"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.8.1", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "ethers": "^6.14.0", "@openzeppelin/contracts": "^4.9.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "hardhat": "^2.17.0", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@typechain/hardhat": "^8.0.0", "dotenv": "^16.3.1"}}