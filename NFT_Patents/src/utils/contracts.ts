import { ethers, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>son<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Contract } from 'ethers';

// Import contract ABIs
import PatentNFTAbi from '../artifacts/contracts/PatentNFT.sol/PatentNFT.json';
import contractAddresses from '../contracts/contract-address.json';

// Contract addresses from deployment
const CONTRACT_ADDRESSES = contractAddresses;

export const getPatentNFTContract = (
  providerOrSigner: BrowserProvider | JsonRpcSigner
) => {
  return new Contract(
    CONTRACT_ADDRESSES.PatentNFT,
    PatentNFTAbi.abi,
    providerOrSigner
  );
};

// Add more contract interaction functions as needed