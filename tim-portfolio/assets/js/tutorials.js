// Tutorials Page JavaScript - Video Management and YouTube Integration

// Video data structure for each category
let videoData = {
    linux: [],
    blockchain: [],
    docker: [],
    networking: []
};

// Initialize tutorials page
document.addEventListener('DOMContentLoaded', function() {
    initVideoHandlers();
    loadSampleVideos();
    initQuickNavigation();
});

// Initialize video click handlers and modal functionality
function initVideoHandlers() {
    // Handle video card clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('[data-video-url]')) {
            e.preventDefault();
            const videoUrl = e.target.closest('[data-video-url]').getAttribute('data-video-url');
            const videoTitle = e.target.closest('.card').querySelector('.card-title').textContent;
            openVideoModal(videoUrl, videoTitle);
        }
    });

    // Handle modal close to stop video
    const videoModal = document.getElementById('videoModal');
    if (videoModal) {
        videoModal.addEventListener('hidden.bs.modal', function() {
            const videoFrame = document.getElementById('videoFrame');
            videoFrame.src = '';
        });
    }
}

// Open video in modal
function openVideoModal(videoUrl, title) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const videoFrame = document.getElementById('videoFrame');
    const modalTitle = document.getElementById('videoModalLabel');
    
    // Convert YouTube URL to embed format
    const embedUrl = convertToEmbedUrl(videoUrl);
    
    videoFrame.src = embedUrl;
    modalTitle.textContent = title;
    modal.show();
}

// Convert YouTube URL to embed format
function convertToEmbedUrl(url) {
    if (url.includes('youtube.com/watch?v=')) {
        const videoId = url.split('v=')[1].split('&')[0];
        return `https://www.youtube.com/embed/${videoId}`;
    } else if (url.includes('youtu.be/')) {
        const videoId = url.split('youtu.be/')[1].split('?')[0];
        return `https://www.youtube.com/embed/${videoId}`;
    }
    return url; // Return as-is if already in embed format
}

// Load sample videos (replace with your actual video data)
function loadSampleVideos() {
    // Sample Linux videos
    videoData.linux = [
        {
            title: "Linux Command Line Basics",
            description: "Learn essential Linux commands for file management, navigation, and system administration.",
            duration: "15:30",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        },
        {
            title: "Linux File Permissions",
            description: "Understanding and managing file permissions in Linux systems.",
            duration: "12:45",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        }
    ];

    // Sample Blockchain videos
    videoData.blockchain = [
        {
            title: "Smart Contract Development",
            description: "Introduction to smart contracts, Solidity programming, and Ethereum development.",
            duration: "22:45",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        },
        {
            title: "DApp Development Tutorial",
            description: "Building decentralized applications with Web3.js and React.",
            duration: "28:30",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        }
    ];

    // Sample Docker videos
    videoData.docker = [
        {
            title: "Docker Fundamentals",
            description: "Learn Docker basics, containerization concepts, and how to build and deploy containers.",
            duration: "18:20",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        },
        {
            title: "Kubernetes Orchestration",
            description: "Container orchestration with Kubernetes, pods, services, and deployments.",
            duration: "25:15",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        }
    ];

    // Sample Networking videos
    videoData.networking = [
        {
            title: "Network Fundamentals",
            description: "Introduction to networking concepts, TCP/IP, and network protocols.",
            duration: "25:15",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        },
        {
            title: "Network Security Basics",
            description: "Understanding network security principles, firewalls, and VPNs.",
            duration: "20:30",
            thumbnail: "https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg",
            url: "YOUTUBE_URL_HERE"
        }
    ];

    // Render all video categories
    renderVideos('linux', 'linux-videos');
    renderVideos('blockchain', 'blockchain-videos');
    renderVideos('docker', 'docker-videos');
    renderVideos('networking', 'networking-videos');
}

// Render videos for a specific category
function renderVideos(category, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Clear existing content except the first placeholder card
    const existingCards = container.querySelectorAll('.col-lg-4:not(:first-child)');
    existingCards.forEach(card => card.remove());

    // Add videos from data
    videoData[category].forEach(video => {
        const videoCard = createVideoCard(video);
        container.appendChild(videoCard);
    });
}

// Create a video card element
function createVideoCard(video) {
    const col = document.createElement('div');
    col.className = 'col-lg-4 col-md-6 mb-4';
    
    col.innerHTML = `
        <div class="card h-100 shadow-sm video-card">
            <div class="video-thumbnail">
                <img src="${video.thumbnail}" class="card-img-top" alt="${video.title}">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            <div class="card-body">
                <h5 class="card-title">${video.title}</h5>
                <p class="card-text">${video.description}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">Duration: ${video.duration}</small>
                    <a href="#" class="btn btn-primary btn-sm" data-video-url="${video.url}">
                        <i class="fab fa-youtube"></i> Watch
                    </a>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

// Functions to add new videos (for admin/content management)
function addLinuxVideo() {
    const video = promptForVideoDetails('Linux');
    if (video) {
        videoData.linux.push(video);
        renderVideos('linux', 'linux-videos');
        showNotification('Linux tutorial added successfully!', 'success');
    }
}

function addBlockchainVideo() {
    const video = promptForVideoDetails('Blockchain');
    if (video) {
        videoData.blockchain.push(video);
        renderVideos('blockchain', 'blockchain-videos');
        showNotification('Blockchain tutorial added successfully!', 'success');
    }
}

function addDockerVideo() {
    const video = promptForVideoDetails('Docker/Kubernetes');
    if (video) {
        videoData.docker.push(video);
        renderVideos('docker', 'docker-videos');
        showNotification('Docker/Kubernetes tutorial added successfully!', 'success');
    }
}

function addNetworkingVideo() {
    const video = promptForVideoDetails('Networking');
    if (video) {
        videoData.networking.push(video);
        renderVideos('networking', 'networking-videos');
        showNotification('Networking tutorial added successfully!', 'success');
    }
}

// Prompt for video details (simple implementation - can be enhanced with a proper form modal)
function promptForVideoDetails(category) {
    const title = prompt(`Enter the title for the ${category} tutorial:`);
    if (!title) return null;
    
    const description = prompt('Enter the video description:');
    if (!description) return null;
    
    const url = prompt('Enter the YouTube URL:');
    if (!url) return null;
    
    const duration = prompt('Enter the video duration (e.g., 15:30):') || '0:00';
    
    // Extract video ID for thumbnail
    let videoId = 'PLACEHOLDER';
    if (url.includes('youtube.com/watch?v=')) {
        videoId = url.split('v=')[1].split('&')[0];
    } else if (url.includes('youtu.be/')) {
        videoId = url.split('youtu.be/')[1].split('?')[0];
    }
    
    return {
        title: title,
        description: description,
        duration: duration,
        thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
        url: url
    };
}

// Quick navigation between sections
function initQuickNavigation() {
    // Add smooth scrolling to section links
    const sectionLinks = document.querySelectorAll('a[href^="#"]');
    sectionLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 100; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Search functionality (can be enhanced)
function searchVideos(query) {
    const allVideos = [
        ...videoData.linux.map(v => ({...v, category: 'linux'})),
        ...videoData.blockchain.map(v => ({...v, category: 'blockchain'})),
        ...videoData.docker.map(v => ({...v, category: 'docker'})),
        ...videoData.networking.map(v => ({...v, category: 'networking'}))
    ];
    
    const results = allVideos.filter(video => 
        video.title.toLowerCase().includes(query.toLowerCase()) ||
        video.description.toLowerCase().includes(query.toLowerCase())
    );
    
    return results;
}

// Export video data (for backup/management)
function exportVideoData() {
    const dataStr = JSON.stringify(videoData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'tutorial-videos-data.json';
    link.click();
    
    URL.revokeObjectURL(url);
}

// Import video data (for backup/management)
function importVideoData(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);
            videoData = importedData;
            
            // Re-render all sections
            renderVideos('linux', 'linux-videos');
            renderVideos('blockchain', 'blockchain-videos');
            renderVideos('docker', 'docker-videos');
            renderVideos('networking', 'networking-videos');
            
            showNotification('Video data imported successfully!', 'success');
        } catch (error) {
            showNotification('Error importing video data. Please check the file format.', 'error');
        }
    };
    reader.readAsText(file);
}

// Utility function to get YouTube video ID from URL
function getYouTubeVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Console helper for developers
console.log('Tutorials page loaded. Available functions:', {
    addLinuxVideo,
    addBlockchainVideo,
    addDockerVideo,
    addNetworkingVideo,
    searchVideos,
    exportVideoData,
    importVideoData
});
