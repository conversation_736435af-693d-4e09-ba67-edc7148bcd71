/* Custom CSS for <PERSON>th Portfolio */

/* Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white: #ffffff;
    --text-dark: #212529;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    padding-top: 76px; /* Account for fixed navbar */
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation Styles */
.navbar {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.navbar-brand img {
    transition: transform 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

/* Social links in navbar */
.navbar-nav .nav-link i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.2);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=1920&h=1080') center/cover;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-overlay {
    width: 100%;
    height: 100%;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-section h4 {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-section p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.6s both;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    margin-top: 76px;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    transition: transform 0.3s ease;
    height: 250px;
    object-fit: cover;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-title {
    color: var(--text-dark);
    font-weight: 600;
}

.card-text {
    color: var(--text-muted);
}

/* Button Styles */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, var(--dark-color), #1a1d20) !important;
}

footer h5 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1rem;
}

footer p {
    color: #adb5bd;
    margin-bottom: 0.5rem;
}

footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--white);
}

.social-links a {
    display: inline-block;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    color: var(--primary-color) !important;
}

/* Newsletter Form */
.newsletter-form .input-group {
    border-radius: 25px;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    border-radius: 0;
}

.newsletter-form .btn {
    border-radius: 0;
    padding: 0.75rem 1.5rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section h4 {
        font-size: 1.25rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
    
    .card-img-top {
        height: 200px;
    }
    
    .btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    body {
        padding-top: 70px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .card-img-top {
        height: 180px;
    }
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Tutorials Page Specific Styles */
.video-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.video-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
}

.video-thumbnail img {
    transition: transform 0.3s ease;
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    opacity: 0;
}

.video-card:hover .play-button {
    opacity: 1;
    background: rgba(0, 123, 255, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.play-button i {
    margin-left: 3px; /* Adjust play icon position */
}

/* Category Icons */
.fab.fa-linux,
.fas.fa-cube,
.fab.fa-docker,
.fas.fa-network-wired {
    transition: all 0.3s ease;
}

.card:hover .fab.fa-linux,
.card:hover .fas.fa-cube,
.card:hover .fab.fa-docker,
.card:hover .fas.fa-network-wired {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* Video Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 0;
}

.ratio {
    border-radius: 0 0 15px 15px;
    overflow: hidden;
}

/* Tutorial Section Headers */
.tutorials-section-header {
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

/* Add Video Buttons */
.btn-outline-primary {
    border-width: 2px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Category Overview Cards */
.category-overview .card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.category-overview .card:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff, #ffffff);
}

/* Responsive adjustments for tutorials */
@media (max-width: 768px) {
    .video-thumbnail img {
        height: 180px;
    }

    .play-button {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .display-4 {
        font-size: 2rem;
    }
}
