# Tim Illguth Portfolio Website

A modern, responsive portfolio website showcasing expertise in blockchain, DevOps, and technology education.

## 🚀 Features

- **Responsive Design**: Built with Bootstrap 5 for mobile-first responsive design
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Contact Forms**: Functional contact forms with validation
- **SEO Optimized**: Proper meta tags and semantic HTML structure
- **Performance Optimized**: Optimized images, CSS, and JavaScript
- **Cross-browser Compatible**: Works on all modern browsers

## 📁 Project Structure

```
tim-portfolio/
├── index.html          # Home page
├── blog.html           # Blog page
├── tutorials.html      # Tutorial videos page
├── portfolio.html      # Portfolio page
├── assets/
│   ├── css/
│   │   └── style.css   # Custom CSS styles
│   ├── js/
│   │   ├── script.js   # Main JavaScript functionality
│   │   └── tutorials.js # Tutorials page specific JavaScript
│   └── images/         # Image assets (to be added)
└── README.md           # This file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Custom styles with CSS Grid and Flexbox
- **Bootstrap 5**: Responsive framework
- **JavaScript**: Interactive functionality
- **Font Awesome**: Icons
- **Google Fonts**: Typography

## 📋 Setup Instructions

1. **Download/Clone the files** to your local machine or web server
2. **Add your images** to the `assets/images/` folder:
   - `ti-logo.png` - Your logo (recommended size: 210x60px)
   - `certifications-screenshot.png` - Your certifications screenshot
   - Any other project images you want to use

3. **Customize the content**:
   - Update contact information in all HTML files
   - Replace placeholder text with your actual content
   - Update social media links
   - Add your actual projects and descriptions

4. **Test locally**:
   - Open `index.html` in a web browser
   - Test all navigation links
   - Test contact forms
   - Check responsive design on different screen sizes

## 🎨 Customization Guide

### Colors
The website uses CSS custom properties (variables) for easy color customization. Edit these in `assets/css/style.css`:

```css
:root {
    --primary-color: #007bff;    /* Main brand color */
    --secondary-color: #6c757d;  /* Secondary color */
    --dark-color: #343a40;       /* Dark backgrounds */
    --light-color: #f8f9fa;      /* Light backgrounds */
}
```

### Content Updates
1. **Navigation**: Update links in the navbar section of each HTML file
2. **Hero Section**: Modify the main heading and description in `index.html`
3. **Projects**: Add your actual projects in `portfolio.html`
4. **Contact Info**: Update email, phone, and social links throughout
5. **Blog Posts**: Add your blog content to `blog.html`
6. **Tutorial Videos**: Add your YouTube video links in `tutorials.html` or use the JavaScript functions

### Tutorial Videos Management
The tutorials page includes four categories:
- **Linux**: System administration and command line tutorials
- **Blockchain Development**: Smart contracts and DApp development
- **Docker & Kubernetes**: Containerization and orchestration
- **Networking**: Network protocols and infrastructure

To add videos:
1. **Manual Method**: Edit the `videoData` object in `assets/js/tutorials.js`
2. **Interactive Method**: Use the "Add Tutorial" buttons on the page (opens prompts)
3. **Bulk Import**: Use the `importVideoData()` function with a JSON file

Video data structure:
```javascript
{
    title: "Video Title",
    description: "Video description",
    duration: "15:30",
    thumbnail: "https://img.youtube.com/vi/VIDEO_ID/maxresdefault.jpg",
    url: "https://youtube.com/watch?v=VIDEO_ID"
}
```

### Images
Replace the Unsplash placeholder images with your own:
- Hero background image
- Project screenshots
- Certification images
- Profile photos

## 📱 Responsive Breakpoints

- **Mobile**: < 576px
- **Tablet**: 576px - 768px
- **Desktop**: 768px - 992px
- **Large Desktop**: > 992px

## 🔧 Form Functionality

The contact forms include:
- Client-side validation
- Loading states
- Success/error notifications
- Email format validation

**Note**: Forms currently show success messages but don't actually send emails. To make them functional, you'll need to:
1. Set up a backend service (PHP, Node.js, etc.)
2. Configure email sending (SMTP, SendGrid, etc.)
3. Update the form action URLs

## 🚀 Deployment Options

### Option 1: Static Hosting (Recommended)
- **Netlify**: Drag and drop the folder to Netlify
- **Vercel**: Connect your GitHub repository
- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Hostinger**: Upload files via FTP/File Manager

### Option 2: Traditional Web Hosting
- Upload all files to your web server's public folder
- Ensure proper file permissions
- Test all functionality

## 📈 SEO Optimization

The website includes:
- Proper meta tags for each page
- Semantic HTML structure
- Alt text for images
- Structured data markup ready
- Fast loading times
- Mobile-friendly design

## 🔒 Security Considerations

- All external links open in new tabs
- Form validation prevents basic attacks
- No sensitive data in client-side code
- HTTPS recommended for production

## 📞 Support & Contact

For questions about this website template:
- **Email**: <EMAIL>
- **LinkedIn**: [Tim Illguth](https://www.linkedin.com/in/tim-illguth-31814b67/)
- **GitHub**: [pttransamdriver](https://github.com/pttransamdriver)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🎯 Next Steps

1. **Add your content** and images
2. **Test thoroughly** on different devices
3. **Set up form backend** for contact functionality
4. **Deploy to your hosting platform**
5. **Set up analytics** (Google Analytics, etc.)
6. **Configure domain** and SSL certificate
7. **Submit to search engines**

---

**Built with ❤️ for Tim Illguth's Portfolio**
