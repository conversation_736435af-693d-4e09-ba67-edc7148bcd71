<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON>'s Tutorial Videos - Linux, Blockchain Development, Docker & Kubernetes, and Networking tutorials">
    <meta name="keywords" content="<PERSON>, tutorials, Linux, blockchain, Docker, Kubernetes, networking, videos, education">
    <meta name="author" content="Tim Illguth">
    
    <title>Tutorial Videos - Linux, Blockchain, Docker & Kubernetes, Networking | Tim <PERSON>guth</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="assets/images/ti-logo.png" alt="Tim Illguth logo" height="40" class="d-inline-block align-text-top">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="tutorials.html">Tutorials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="portfolio.html">Portfolio</a>
                    </li>
                </ul>
                
                <!-- Social Links in Navigation -->
                <div class="navbar-nav ms-3">
                    <a class="nav-link" href="https://www.facebook.com/" target="_blank" title="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a class="nav-link" href="https://www.instagram.com/" target="_blank" title="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a class="nav-link" href="https://www.linkedin.com/in/tim-illguth-31814b67/" target="_blank" title="LinkedIn">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a class="nav-link" href="https://www.twitter.com/" target="_blank" title="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header bg-primary text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4 fw-bold">Tutorial Videos</h1>
                    <p class="lead">Comprehensive video tutorials covering Linux, Blockchain Development, Docker & Kubernetes, and Networking</p>
                    <div class="mt-4">
                        <i class="fab fa-youtube fa-3x text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tutorial Categories Overview -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fab fa-linux fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Linux</h5>
                            <p class="card-text">System administration, command line, and server management</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-cube fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Blockchain Development</h5>
                            <p class="card-text">Smart contracts, DApps, and blockchain fundamentals</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fab fa-docker fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Docker & Kubernetes</h5>
                            <p class="card-text">Containerization, orchestration, and DevOps practices</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-network-wired fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Networking</h5>
                            <p class="card-text">Network protocols, security, and infrastructure</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Linux Tutorials Section -->
    <section class="py-5" id="linux">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex align-items-center mb-4">
                        <i class="fab fa-linux fa-2x text-primary me-3"></i>
                        <h2 class="mb-0">Linux Tutorials</h2>
                    </div>
                    <p class="text-muted mb-4">Master Linux system administration, command line operations, and server management with these comprehensive tutorials.</p>
                </div>
            </div>
            <div class="row" id="linux-videos">
                <!-- Linux videos will be populated here -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm video-card">
                        <div class="video-thumbnail">
                            <img src="https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg" class="card-img-top" alt="Linux Tutorial">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">Linux Fundamentals</h5>
                            <p class="card-text">Introduction to Linux operating system, basic commands, and file system navigation.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Duration: 15:30</small>
                                <a href="#" class="btn btn-primary btn-sm" data-video-url="YOUTUBE_URL_HERE">
                                    <i class="fab fa-youtube"></i> Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add more Linux video cards here -->
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="addLinuxVideo()">
                    <i class="fas fa-plus"></i> Add Linux Tutorial
                </button>
            </div>
        </div>
    </section>

    <!-- Blockchain Development Tutorials Section -->
    <section class="py-5 bg-light" id="blockchain">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex align-items-center mb-4">
                        <i class="fas fa-cube fa-2x text-primary me-3"></i>
                        <h2 class="mb-0">Blockchain Development Tutorials</h2>
                    </div>
                    <p class="text-muted mb-4">Learn blockchain development, smart contract programming, and decentralized application (DApp) creation.</p>
                </div>
            </div>
            <div class="row" id="blockchain-videos">
                <!-- Blockchain videos will be populated here -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm video-card">
                        <div class="video-thumbnail">
                            <img src="https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg" class="card-img-top" alt="Blockchain Tutorial">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">Smart Contract Basics</h5>
                            <p class="card-text">Introduction to smart contracts, Solidity programming, and Ethereum development.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Duration: 22:45</small>
                                <a href="#" class="btn btn-primary btn-sm" data-video-url="YOUTUBE_URL_HERE">
                                    <i class="fab fa-youtube"></i> Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add more Blockchain video cards here -->
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="addBlockchainVideo()">
                    <i class="fas fa-plus"></i> Add Blockchain Tutorial
                </button>
            </div>
        </div>
    </section>

    <!-- Docker & Kubernetes Tutorials Section -->
    <section class="py-5" id="docker-kubernetes">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex align-items-center mb-4">
                        <i class="fab fa-docker fa-2x text-primary me-3"></i>
                        <h2 class="mb-0">Docker & Kubernetes Tutorials</h2>
                    </div>
                    <p class="text-muted mb-4">Master containerization with Docker and orchestration with Kubernetes for modern DevOps practices.</p>
                </div>
            </div>
            <div class="row" id="docker-videos">
                <!-- Docker & Kubernetes videos will be populated here -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm video-card">
                        <div class="video-thumbnail">
                            <img src="https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg" class="card-img-top" alt="Docker Tutorial">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">Docker Fundamentals</h5>
                            <p class="card-text">Learn Docker basics, containerization concepts, and how to build and deploy containers.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Duration: 18:20</small>
                                <a href="#" class="btn btn-primary btn-sm" data-video-url="YOUTUBE_URL_HERE">
                                    <i class="fab fa-youtube"></i> Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add more Docker & Kubernetes video cards here -->
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="addDockerVideo()">
                    <i class="fas fa-plus"></i> Add Docker/K8s Tutorial
                </button>
            </div>
        </div>
    </section>

    <!-- Networking Tutorials Section -->
    <section class="py-5 bg-light" id="networking">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex align-items-center mb-4">
                        <i class="fas fa-network-wired fa-2x text-primary me-3"></i>
                        <h2 class="mb-0">Networking Tutorials</h2>
                    </div>
                    <p class="text-muted mb-4">Understand network protocols, security, infrastructure design, and troubleshooting techniques.</p>
                </div>
            </div>
            <div class="row" id="networking-videos">
                <!-- Networking videos will be populated here -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm video-card">
                        <div class="video-thumbnail">
                            <img src="https://img.youtube.com/vi/PLACEHOLDER/maxresdefault.jpg" class="card-img-top" alt="Networking Tutorial">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">Network Fundamentals</h5>
                            <p class="card-text">Introduction to networking concepts, TCP/IP, and network protocols.</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Duration: 25:15</small>
                                <a href="#" class="btn btn-primary btn-sm" data-video-url="YOUTUBE_URL_HERE">
                                    <i class="fab fa-youtube"></i> Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add more Networking video cards here -->
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" onclick="addNetworkingVideo()">
                    <i class="fas fa-plus"></i> Add Networking Tutorial
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5>Socials</h5>
                    <div class="social-links">
                        <a href="https://github.com/pttransamdriver" target="_blank" class="text-light me-3">
                            <i class="fab fa-github fa-2x"></i>
                        </a>
                        <a href="https://www.linkedin.com/in/tim-illguth-31814b67/" target="_blank" class="text-light">
                            <i class="fab fa-linkedin-in fa-2x"></i>
                        </a>
                    </div>
                    <p class="mt-3">Explore Tim Illguth's expertise and projects.</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>Contact Info</h5>
                    <p><strong>Contact Portal</strong></p>
                    <p><a href="mailto:<EMAIL>" class="text-light"><EMAIL></a></p>
                    <p>***-***-0979</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>Newsletter</h5>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Enter your email address here">
                            <button class="btn btn-primary" type="submit">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2025. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Video Modal -->
    <div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel">Tutorial Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="ratio ratio-16x9">
                        <iframe id="videoFrame" src="" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/script.js"></script>
    <!-- Tutorials specific JS -->
    <script src="assets/js/tutorials.js"></script>
</body>
</html>
