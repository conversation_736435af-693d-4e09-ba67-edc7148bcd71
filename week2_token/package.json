{"name": "token-contract", "version": "1.0.0", "description": "ERC20 Token Contract Project", "main": "index.js", "scripts": {"test": "hardhat test", "compile": "hardhat compile", "deploy": "hardhat run scripts/deploy.js --network localhost", "node": "hardhat node"}, "keywords": ["ethereum", "solidity", "token", "erc20", "hardhat"], "author": "", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "chai": "^4.3.7", "ethereum-waffle": "^4.0.10", "ethers": "^5.7.2", "hardhat": "^2.19.0", "solidity-coverage": "^0.8.5", "mocha": "^10.2.0"}}